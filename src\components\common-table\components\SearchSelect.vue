// 样式在index.vue中
<template>
	<div class="table-select-filter">
		<div class="describe-text">添加筛选条件</div>
		<div class="title-text">{{ titleText }}</div>
		<el-select
			v-if="showSelect"
			:teleported="false"
			v-model="selectValue"
			placeholder="请选择"
			class="search-type-select"
		>
			<el-option
				v-for="item in options"
				:key="item.value"
				:label="item.label"
				:value="item.value"
			/>
		</el-select>
		<div class="select-box">
			<el-input
				v-model="searchKeyword"
				class="n-t-input-box"
				clearable
				:maxlength="maxLength"
				:placeholder="placeholder"
			>
			</el-input>
			<div
				class="check-box"
				v-if="props.selectType === 'checkbox'"
			>
				<el-checkbox
					v-model="checkAll"
					:indeterminate="isIndeterminate"
					@change="handleCheckAllChange"
					class="block-check-item"
					>全选</el-checkbox
				>
				<el-checkbox-group
					v-model="checkedList"
					@change="handleCheckedListChange"
				>
					<el-checkbox
						v-for="(item, index) in filteredList"
						:key="index"
						:label="item.label"
						:value="item.value"
						class="block-check-item"
					>
						{{ item.label }}
					</el-checkbox>
				</el-checkbox-group>
			</div>
			<div
				class="radio-box"
				v-if="props.selectType === 'radio'"
			>
				<div style="min-height: 26px;">
					<el-button
						link
						type="primary"
						@click="resetRadioGroup"
						>重置</el-button
					>
				</div>
				<el-radio-group
					v-model="checkedItem"
					@change="handleCheckedItemChange"
					>>
					<el-radio
						v-for="(item, index) in filteredList"
						:key="index"
						:label="item.value"
						class="block-radio-item"
						>{{ item.label }}</el-radio
					>
				</el-radio-group>
			</div>
		</div>
		<div class="btn-box">
			<el-button
				@click="handleClick"
				class="submit-btn"
				type="primary"
				size="small"
				>确定</el-button
			>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue';
interface Props {
	value: string;
	placeholder?: string;
	maxLength?: number;
	slotName?: 'prefix' | 'suffix';
	selectType?: 'checkbox' | 'radio'; // 选择类型 checkbox多选 radio单选
	titleText: string;
	searchRule?: string | boolean; // 查询匹配规则。父组件传入，根据此规则，生成查询匹配规则
	rule?: string | boolean; // 匹配规则下拉框内容
	filters: any[]; // 下拉框选项列表
}

const props = withDefaults(defineProps<Props>(), {
	maxLength: 200,
	slotName: 'suffix',
	placeholder: '搜索',
	titleText: '',
	searchRule: 'contain',
	rule: '',
	selectType: 'checkbox',
	filters: () => [],
});
const emit = defineEmits<{
	(ev: 'update:value', value: string): void;
	(ev: 'update:rule', rule: string): void;
	(ev: 'search', row: any): void; //
}>();
const searchKeyword = ref('');

const showSelect = ref(false); // 是否展示下拉框
const options = [
	{
		value: 'contain',
		label: '包含',
	},
	{
		value: 'noContain',
		label: '不包含',
	},
];

// 下拉框，搜索规则
const selectValue = ref();

// 多选框，条件组
const checkAll = ref<boolean>(false);
const isIndeterminate = ref(false);
const checkedList = ref<any[]>([]);

// 单选框组
const checkedItem = ref<any>();

// 条件组数据类型，兼容数据类型为number情况下的，数据回显问题
let valueType = 'string';

watch(
	() => props.value,
	(val) => {
		if (val) {
			if (valueType == 'string') {
				checkedList.value = val.split(',');
			} else {
				checkedList.value = val.split(',').map(Number);
			}
		} else {
			checkedList.value = [];
			checkedItem.value = undefined;
		}
		handleCheckedListChange(checkedList.value);
	},
);

watch(
	() => props.filters,
	(val) => {
		if (val && val.length > 0) {
			valueType = typeof val[0].value;
		}
	},
	{
		immediate: true,
	},
);

watch(
	() => props.searchRule,
	(val) => {
		// 为true，展示查询规则下拉框
		if (val === true) {
			showSelect.value = true;
			selectValue.value = 'contain';
		} else if (val === false) {
			// 为false，不展示下拉框，默认为包含规则
			showSelect.value = false;
			selectValue.value = 'contain';
		} else {
			// 其他枚举字段，直接赋值即可
			showSelect.value = false;
			selectValue.value = val ? val : 'contain';
		}
	},
	{
		immediate: true,
	},
);

// 多选条件组
const filteredList = computed(() => {
	return props.filters.filter((item) => item.label.includes(searchKeyword.value));
});

const handleCheckAllChange = (value: any) => {
	let arr = filteredList.value.map((item) => item.value);
	checkedList.value = (value as boolean) ? arr : [];
	isIndeterminate.value = false;
};

const handleCheckedListChange = (value: any[]) => {
	const checkedCount = value.length;
	checkAll.value = checkedCount === filteredList.value.length;
	isIndeterminate.value = checkedCount > 0 && checkedCount < filteredList.value.length;
};

const handleCheckedItemChange = (val: any) => {
	checkedItem.value = val;
};

const resetRadioGroup = () => {
	checkedItem.value = undefined;
};

function handleClick() {
	// 判断选择类型
	const selectType = props.selectType;

	selectType === 'checkbox' && emit('update:value', checkedList.value.join());
	selectType === 'radio' && emit('update:value', checkedItem.value ? [checkedItem.value].join() : [].join());
	emit('update:rule', selectValue.value);

	// 筛选符合条件的列表
	let arr =
		selectType === 'checkbox'
			? props.filters.filter((item) => checkedList.value.includes(item.value))
			: checkedItem.value
			? [props.filters.find((i) => i.value === checkedItem.value)]
			: [];
	emit('search', arr);
}
</script>
<style lang="scss" scoped>
.n-t-input-box {
	flex-shrink: 0;
	width: auto;
}
.t-input-icon {
	order: 1;
	cursor: pointer;
}

.table-select-filter {
	padding: 0 12px 12px 12px;
	.describe-text {
		font-family: MicrosoftYaHei;
		font-size: 12px;
		color: #86909c;
		line-height: 20px;
		height: 34px;
		padding: 10px 0 4px 0;
		box-sizing: border-box;
	}

	.title-text {
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		font-size: 14px;
		color: #1d2129;
		line-height: 22px;
		margin-bottom: 6px;
	}

	.search-type-select {
		margin-bottom: 12px;
	}

	.select-box {
		width: 190px;
		height: 170px;
		background: #ffffff;
		border-radius: 4px;
		border: 1px solid #e5e6eb;
		padding: 8px 12px;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		.check-box,
		.radio-box {
			flex: 1;
			margin-top: 5px;
			overflow: auto;
			display: flex;
			flex-direction: column;
			justify-content: flex-start;
			align-items: flex-start;
		}
		.block-check-item {
			display: block;
		}
		.block-radio-item {
			width: 100%;
			display: block;
		}
	}

	.btn-box {
		text-align: right;

		.submit-btn {
			margin-top: 12px;
			height: 28px;
		}
	}
}
</style>
