<template>
	<el-dialog @closed="closeDialog" v-model="dialogVisible" title="发起申请"
		class="xirui-flex-col xirui-custom-common-dialog2 xirui-small-dialog2">
		<div class="xirui-flex-row item-box">
			<div v-for="item in showDataList" :key="item.cNumber" class="xirui-flex-row-center item"
				:class="{ 'item-selected': item.selected }" @click="handleItemClick(item)">
				<img class="img" :src="itemObj(item).imgUrl" alt="" />
				<div class="text">{{ itemObj(item).cName }}</div>
			</div>
		</div>
		<template #footer>
			<el-button @click="dialogVisible = false">取消</el-button>
			<el-button @click="handleNextAction" type="primary" :loading="btnLoading">下一步</el-button>
		</template>
	</el-dialog>
</template>

<script lang="tsx" setup>
import { watch, ref, defineProps, defineEmits } from 'vue';
import { TypeMap, defaultTypImage } from '../api/common'

// #region 参数定义
interface Props {
	// 弹窗显隐
	visible: boolean;
	// 数据列表
	dataList: any[];
}
const props = withDefaults(defineProps<Props>(), {
	visible: false,
	dataList: () => {
		return [];
	},
});

const emit = defineEmits<{
	(e: 'update:visible', value: boolean): void;
	(e: 'next', value: any): void;
}>();

const showDataList = ref([...props.dataList]);

watch(
	() => props.visible,
	(val) => {
		dialogVisible.value = val || false;
	},
);
watch(
	() => props.dataList,
	(val) => {
		showDataList.value = [...val];
	},
	{ deep: true },
);
// #endregion

// 控制弹窗显隐
const dialogVisible = ref<boolean>(false);

// 提交按钮loading
const btnLoading = ref<boolean>(false);

// 记录选中的项
const selectedItem = ref<any>(null);

// 列表项图片
const itemObj = (item: any) => {
	let tempObj = { imgUrl: defaultTypImage, cName: item.cName };
	if (Object.keys(TypeMap).includes(item.cNumber)) {
		tempObj = TypeMap[item.cNumber];
	}
	return tempObj;
};
// 列表项点击事件
const handleItemClick = (item: any) => {
	selectedItem.value = item;
	showDataList.value.forEach((val) => {
		if (val.cNumber == item.cNumber) {
			val.selected = true;
		} else {
			val.selected = false;
		}
	});
};
// 关闭弹窗
const closeDialog = () => {
	showDataList.value.forEach((val) => {
		val.selected = false;
	});
	selectedItem.value = null;
	emit('update:visible', false);
};

// 下一步
const handleNextAction = () => {
	if (!selectedItem.value) {
		ElMessage.warning('请先选择数据');
		return;
	}
	emit('next', selectedItem.value);
	closeDialog();
};
</script>
<style lang="scss">
// 通用样式
.xirui-custom-common-dialog2 {
	padding: 0 !important;
	margin: 0 !important;
	width: 721px !important;
	min-height: 336px !important;

	.el-dialog__header {
		padding-bottom: 0;
		padding: 13px 16px;
		height: 48px;
		display: flex;
		align-items: center;
		border-bottom: 1px solid #e8eaec;

		.el-dialog__title {
			font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
			font-weight: 400;
			font-size: 16px;
			color: rgba(0, 0, 0, 0.85);
			text-align: left;
		}
	}

	.el-dialog__body {
		box-sizing: border-box;
		padding: 16px 18px;
		flex: 1;
		max-height: 700px;
	}

	.el-dialog__footer {
		height: 64px;
		padding: 0 16px;
		display: flex;
		align-items: center;
		justify-content: flex-end;
	}
}
</style>
<style lang="scss" scoped>
.item-box {
	flex-wrap: wrap;
	margin-right: -17px;
}

.item {
	// width: 159px;
	width: calc(calc(100% - 68px) / 4);
	height: 64px;
	background: #ffffff;
	border-radius: 8px 8px 8px 8px;
	border: 1px solid #e5e6eb;
	margin-right: 17px;
	margin-bottom: 16px;
	// flex-shrink: 0;
	padding: 12px 16px;
	cursor: pointer;

	.img {
		width: 40px;
		height: 40px;
		margin-right: 8px;
	}

	.text {
		font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
		font-weight: 400;
		font-size: 14px;
		color: #1d2129;
		line-height: 14px;
		text-align: left;
		font-style: normal;
		text-transform: none;
	}
}

.item-selected {
	background: #ccdff3;
}
</style>
