import { defaultFormatAll, defaultFormatDate, defaultFormatMonth, defaultText } from '@/config';
import Moment from 'moment';

export function getDataStr(val?: string | Date | number) {
	if (!val) {
		return defaultText;
	}
	return Moment(val).format(defaultFormatDate);
}
export function getDataMonthStr(val?: string | Date | number) {
	if (!val) {
		return defaultText;
	}
	return Moment(val).format(defaultFormatMonth);
}
export function getDataAllStr(val?: string | Date | number) {
	if (!val) {
		return defaultText;
	}
	return Moment(val).format(defaultFormatAll);
}

// 格式化时间显示内容
// 1 分钟内 刚刚
// 1 小时内 n分钟前
// 24 小时内 n小时前
// 24 小时外 mm-dd HH:mm
// 超过 1年 YYYY-MM-DD HH:mm
export function getDataFormat(val?: string | Date | number) {
	if (!val) {
		return defaultText;
	}
	const nowDateDayjs = Moment(new Date());
	if (nowDateDayjs.diff(val, 'second') < 60) {
		return '刚刚';
	}
	const diffMinute = nowDateDayjs.diff(val, 'minute');
	if (diffMinute < 60) {
		return diffMinute + '分钟前';
	}
	const diffHour = nowDateDayjs.diff(val, 'hour');
	if (diffHour < 24) {
		return diffHour + '小时前';
	}
	return getDataFormatYear(val);
}
// 24 小时外 mm-dd HH:mm
// 超过 1年 YYYY-MM-DD HH:mm
export function getDataFormatYear(val?: string | Date | number) {
	if (!val) {
		return defaultText;
	}
	const valDayjs = Moment(val);
	if (Moment(new Date()).isSame(val, 'year')) {
		return valDayjs.format('MM-DD HH:mm');
	} else {
		return valDayjs.format(defaultFormatAll);
	}
}
export function getDataFormatAll(val?: string | Date | number) {
	if (!val) {
		return defaultText;
	}
	const valDayjs = Moment(val);
	return valDayjs.format(defaultFormatAll);
}
export function getDataFormatDayZhCn(val?: string | Date | number) {
	if (!val) {
		return defaultText;
	}
	const valDayjs = Moment(val);
	return valDayjs.format('YYYY年MM月DD日');
}

const mapMonth: any = {
	0: '一',
	1: '二',
	2: '三',
	3: '四',
	4: '五',
	5: '六',
	6: '七',
	7: '八',
	8: '九',
	9: '十',
	10: '十一',
	11: '十二',
};
// 获取中文月份
export function getDataMonth(val: string | Date) {
	if (!val) return '';
	const valDayjs = Moment(val);
	const month = valDayjs.month();
	return mapMonth[month] + '月';
}
// 获取日
export function getDataDay(val: string | Date) {
	if (!val) return '';
	const valDayjs = Moment(val);
	let day = valDayjs.get('D') + '';
	if (day.length === 1) {
		// 只有一位
		day = '0' + day;
	}
	return day;
}
