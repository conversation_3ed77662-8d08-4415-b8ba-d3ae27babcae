<template>
	<svg class="icon-font">
		<title v-if="title">{{ title }}</title>
		<use :xlink:href="`#${type}`"></use>
	</svg>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';

defineProps({
	type: {
		type: String as PropType<string>,
		required: true,
	},
	title: {
		type: String,
	},
});
</script>
<style lang="scss" scoped>
.icon-font {
	font-family: 'iconfont' !important;
	display: inline;
	vertical-align: middle;
	width: 1em;
	height: 1em;
	fill: currentcolor;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	flex-shrink: 0;
	&:focus {
		outline: none;
	}
}
</style>
