<template>
	<el-config-provider :locale="zhCn">
		<router-view v-slot="{ Component }">
			<keep-alive :include="['main']">
				<component :is="Component" />
			</keep-alive>
		</router-view>
	</el-config-provider>
</template>
<script setup lang="ts">
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';

// 新建的vue文件中使用 import 时，会出现没有智能提示的bug，退出vs code可以解决。
// 或者在它vue文件中引用新建的vue文件,等智能提示出来后，在注销掉，如：
// import aaaaa from '@/views/project-edit/index.vue';
</script>
<style lang="scss" scoped></style>
<style lang="scss">
@import '@/styles/common.scss';
</style>
