// 新增/编辑，请求表单数据
export interface CreateOrUpdateTableRequestData {
  id?: string
  username: string
  password?: string
}

// 列表查询参数
export interface TableRequestData {
  /** 当前页码 */
  currentPage: number
  /** 查询条数 */
  size: number
  /** 查询参数：用户名 */
  username?: string
  /** 查询参数：手机号 */
  phone?: string
}

// 列表内单个数据格式
export interface TableData {
  createTime: string
  email: string
  id: string
  phone: string
  roles: string
  status: boolean
  username: string
}

// 列表接口返回值格式
export type TableResponseData = ApiResponseData<{
  list: TableData[]
  total: number
}>
