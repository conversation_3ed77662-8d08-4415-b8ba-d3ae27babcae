<template>
	<div class="container">
		<div
			class="item"
			v-for="(item, index) in textList"
			:key="index"
			:class="item.class"
		>
			{{ item.text }}
		</div>
	</div>
</template>

<script lang="tsx" setup>
const textList = [
	{
		text: '24px 标题一',
		class: 'xirui-h1',
	},
	{
		text: '18px 标题二',
		class: 'xirui-h2',
	},
	{
		text: '16px 标题三',
		class: 'xirui-h3',
	},
	{
		text: '14px 正文',
		class: 'xirui-text',
	},
	{
		text: '12px 弱化内容和弱辅助文案',
		class: 'xirui-tip',
	},
];
</script>
<style lang="scss" scoped>
.container {
	padding: 20px;

	.item {
		margin-bottom: 10px;
	}
}
</style>
