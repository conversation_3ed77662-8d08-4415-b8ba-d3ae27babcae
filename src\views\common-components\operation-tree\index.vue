<template>
	<div class="operation-tree-list">
		<div class="operation-tree-item-box">
			<div class="operation-tree-item-title">树形列表</div>
			<div class="operation-tree-item"><Tree /></div>
		</div>
		<div class="operation-tree-item-box">
			<div class="operation-tree-item-title">树形带切换按钮</div>
			<div class="operation-tree-item"><TreeButtonChange /></div>
		</div>
		<div class="operation-tree-item-box">
			<div class="operation-tree-item-title">默认列表</div>
			<div class="operation-tree-item"><List /></div>
		</div>
		<div class="operation-tree-item-box">
			<div class="operation-tree-item-title">带Icon的列表-使用默认icon</div>
			<div class="operation-tree-item"><CustomIconDefault /></div>
		</div>
		<div class="operation-tree-item-box">
			<div class="operation-tree-item-title">带Icon的列表-全部自定义icon</div>
			<div class="operation-tree-item"><CustomIcon /></div>
		</div>
		<div class="operation-tree-item-box">
			<div class="operation-tree-item-title">带Icon的列表-节点自定义</div>
			<div class="operation-tree-item"><CustomNode /></div>
		</div>
	</div>
</template>

<script setup lang="ts">
import Tree from './views/tree.vue';
import List from './views/list.vue';
import TreeButtonChange from './views/tree-button-change.vue';
import CustomIconDefault from './views/custom-icon-default.vue';
import CustomIcon from './views/custom-icon.vue';
import CustomNode from './views/custom-node.vue';
</script>

<style lang="scss" scoped>
.operation-tree-list {
	width: 100%;
	height: 100%;
	padding: 0 10px;
	display: flex;
	.operation-tree-item-box {
		width: 240px;
		height: 100%;
		margin-left: 10px;
		.operation-tree-item-title {
			height: 40px;
			line-height: 40px;
			text-align: center;
		}
		.operation-tree-item {
			border: 1px solid rgba($color: #000000, $alpha: 0.1);
			width: 100%;
			height: calc(100% - 50px);
		}
	}
}
</style>
