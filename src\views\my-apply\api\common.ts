import typeImage1 from '@/assets/images/my-apply/type1.png';
import typeImage2 from '@/assets/images/my-apply/type2.png';
import typeImage3 from '@/assets/images/my-apply/type3.png';
import typeImage4 from '@/assets/images/my-apply/type4.png';
import typeImage5 from '@/assets/images/my-apply/type5.png';
import typeImage6 from '@/assets/images/my-apply/type6.png';

export const defaultTypImage = typeImage1;

// 申请类型——定义映射项的接口类型
export interface TypeItem {
	imgUrl: string;
	cName: string;
}

// 申请类型——定义映射对象（键为字符串类型，值为TypeItem类型）
export const TypeMap: Record<string, TypeItem> = {
	CB021: {
		imgUrl: typeImage1,
		cName: '合同审批',
	},
	CB024: {
		imgUrl: typeImage2,
		cName: '证件使用',
	},
	CB025: {
		imgUrl: typeImage3,
		cName: '付款申请',
	},
	CB029: {
		imgUrl: typeImage4,
		cName: '招采需求',
	},
	CB031: {
		imgUrl: typeImage5,
		cName: '出差申请',
	},
	CB032: {
		imgUrl: typeImage6,
		cName: '差旅报销',
	},
};

// 额外导出所有可能的键值（可选，用于类型约束）
export type TypeKey = keyof typeof TypeMap;

//处理列表中的催办次数
export function processArray(arr) {
	// 创建一个新数组以避免修改原数组
	const result: any[] = [];
	let monitorCount = 0;
	let date = '';

	for (let i = 0; i < arr.length; i++) {
		const current = arr[i];

		if (current.actionTypeId === '90') {
			// 遇到90，增加计数器并跳过当前元素
			monitorCount++;
			date = current.approvalDate;
			continue;
		}

		// 如果前面有90，给当前元素添加moniterNum字段
		if (monitorCount > 0) {
			// 创建新对象以避免修改原数组中的对象
			const newItem = { ...current, moniterNum: monitorCount, endDate: date };
			result.push(newItem);
			monitorCount = 0; // 重置计数器
		} else {
			// 没有前面的90，直接添加当前元素
			result.push({ ...current });
		}
	}

	// 处理数组末尾连续90的情况（如果有）
	if (monitorCount > 0) {
		// 根据需求决定是否需要在末尾添加一个带有moniterNum的空对象
		// 这里选择忽略，因为后面没有元素可以附加moniterNum
	}

	return result;
}
//判断当前时间是否超过一个时间格式的两个小时，用于催办展示的渲染
export function isTimeDifferenceMoreThanTwoHours(timeString) {
	// 将输入的时间字符串转换为Date对象
	const inputDate = new Date(timeString);

	// 获取当前时间
	const currentDate = new Date();

	// 计算时间差（毫秒）
	const timeDifference = ((currentDate as any) - (inputDate as any)) as any;

	// 两小时的毫秒数 (2 * 60 * 60 * 1000)
	const twoHoursInMs = 2 * 60 * 60 * 1000;

	// 返回比较结果
	return timeDifference > twoHoursInMs;
}
//在数组中第一个 actionTypeId 为 20 的对象上添加
export function markFirstTwenty(arr) {
	let foundFirstTwenty = false;

	// 创建新数组以避免直接修改原数组
	const result = arr.map((item) => {
		// 如果已经找到第一个20或者当前项的actionTypeId不是20，直接返回副本
		if (foundFirstTwenty || item.actionTypeId !== '20') {
			return { ...item };
		}

		// 找到第一个20，标记并设置标志位
		foundFirstTwenty = true;
		return {
			...item,
			isFirstTwenty: true,
		};
	});

	return result;
}