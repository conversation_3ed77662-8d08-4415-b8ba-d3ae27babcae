// 新增/编辑，请求表单数据
export interface CreateOrUpdateTableRequestData {
  id?: string
  username: string
  password?: string
}

// 列表查询参数
export interface TableRequestData {
  /** 当前页码 */
  currentPage: number
  /** 查询条数 */
  size: number
  /** 查询参数：用户名 */
  username?: string
  /** 查询参数：手机号 */
  phone?: string
}

// 列表内单个数据格式
export interface TableData {
  createTime: string
  email: string
  id: string
  phone: string
  roles: string
  status: boolean
  username: string
}

// 列表接口返回值格式
export type TableResponseData = ApiResponseData<{
  list: TableData[]
  total: number
}>

import { Component } from "vue";

// tree列表内单个数据格式
export interface TreeListItem {
    accountCount?: number | null;
    children?: TreeListItem[] | null;
    externalId?: null | string;
    level?: number | null;
    name?: null | string;
    parentId?: null | string;
    type?: number | null;
    userOrOrg?: number | null;
    uuid?: null | string;
}
// 标签列表内单个数据格式
export interface LabelListItem {
    createBy?: null | string;
    description?: null | string;
    id?: number | null;
    name?: null | string;
    organizationNum?: number | null;
    sort?: number | null;
    tenantId?: null | string;
    type?: number | null;
    uuid?: string;
    icon?: string;
    customSlotName?: string;
    customSlotClass?: string;
}
//tree下拉菜单列表单个配置
export interface OperationItem {
    actionCode: string;//下拉菜单标识
    actionLabel: string;//下拉菜单名称
    divided?: boolean;//是否显示分隔符
    disabled?: boolean;//是否禁用
    icon?: string | Component;//自定义图标
}
export interface DefaultProps {
    label?: string;
    children?: string;
}

//按钮组单个数据格式
export interface ButtonItem {
    label?: string; //按钮名称
    key?: string | number; //按钮唯一标识
}

// tree接口返回值格式
export type TreeListResponse = ApiResponseData<TreeListItem[]>;
// 列表接口返回值格式
export type LabelListResponse = ApiResponseData<LabelListItem[]>
