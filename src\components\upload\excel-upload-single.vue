<!--
  功能：excel导入组件-业务数据导入使用
  作者：ligw
  时间：2024年08月08日 16:07:08
  版本：v1.0
  修改记录：
  修改内容：
  修改人员：
  修改时间：
-->
<template>
  <BaseUpload 
    :action="action"
    :show-file-list="false"
    :before-upload="handleBeforeUpload"
    :on-progress="handleProgress"
    :on-success="handleSuccess"
    :on-error="handleError"
    accept=".xlsx"
    v-bind="$attrs"
  >
    <template #default>
      <slot></slot>
      <ElButton
        v-if="!$slots.default" 
        :loading="loading" 
        class="excel-upload-btn"  
        plain
        type="primary"
      >
      {{ title }}
      </ElButton>
    </template>
  </BaseUpload>
  <ImportResult ref="resultRef" :data="result"  />
</template>
<script lang='ts' setup>
import { ref } from 'vue'
import type { UploadProps } from 'element-plus'
import BaseUpload from './base-upload.vue';
import { ElMessage } from 'element-plus';
import { envConfig } from '@/config'
interface Props {
	title?: string ; // 上传文案
  action?: string; // 上传请求路径
  successMessage?: string; // 上传请求路径
}
const props = withDefaults(defineProps<Props>(), {
  title: '导入',
  // action: envConfig.VITE_BASE_FILE_API + '/open-api/v1/file/upload'
});
const resultRef = ref<ImportResult>()
const result = ref<ImportResultResponse>({
  code: 0,
  data: [{
    failedResultList: [],
    totalCount: 0,
    failedCount: 0,
    successCount: 0
  }],
  message:''
})
const emit = defineEmits<{
	(ev: 'complete', response: any): void;
}>();
// 上传按钮加载状态，防止重复点击
const loading = ref(false)
// 上传前按钮设置加载中
const handleBeforeUpload: UploadProps['onProgress'] = () => {
  loading.value = true
}
const handleProgress: UploadProps['onProgress'] = () => {
  // TODO 上传进度
}
// 上传成功后，按钮清除加载中状态；
// 导出成功提示；
// 外发事件用于父页面响应事件，如刷新表格数据等
const handleSuccess: UploadProps['onSuccess'] = (response) => {
  loading.value = false
  const { code, message } = response
  if (code === 0) {
    ElMessage.success(props?.successMessage || message || '导入成功！')
    result.value = response as ImportResultResponse
    resultRef.value?.open()
    emit('complete', response)
  } else {
    ElMessage.error(message || '导入失败！')
  }
}
// 上传失败，按钮清除加载中状态；失败提示；
const handleError: UploadProps['onSuccess'] = (response) => {
  loading.value = false
  ElMessage.error(response?.message || '导入失败！')
}

</script>
<style scoped lang='scss'>

</style>
