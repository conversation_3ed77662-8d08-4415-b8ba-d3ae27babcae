<template>
	<div class="operation-tree">
		<div
			v-if="showHeader"
			class="header-view"
		>
			<el-input
				v-model="filterText"
				class="filterInput"
				placeholder="搜索"
				:prefix-icon="Search"
				clearable
			/>
			<el-tooltip
				class="box-item"
				effect="light"
				:content="headerAddTips"
				placement="top"
			>
				<el-button
					v-if="showAddBtn"
					class="add-btn"
					type="primary"
					:icon="Plus"
					@click="headerAddClick"
				/>
			</el-tooltip>
		</div>
		<div
			v-if="showBtnChange"
			class="class-change-view"
		>
			<slot name="btnTabs"></slot>
		</div>
		<div
			class="tree-view"
			:class="{ 'list-view': treeType === 'list', 'custom-icon-list-view': treeType === 'customIconList' }"
			:style="{ height: treeViewHeight }"
		>
			<el-scrollbar class="custom-tree-box">
				<el-tree
					v-bind="{ ...$attrs }"
					ref="treeRef"
					class="custom-operatiom-tree"
					:data="treeList"
					:props="newDefaultProps"
					:node-key="nodeKey"
					:highlight-current="true"
					:expand-on-click-node="expandOnClickNode"
					:filter-node-method="filterNode"
					:icon="customIcon"
					:style="operationBtnStyleConfig"
					@node-click="nodeClick"
				>
					<template #default="{ node, data }">
						<span
							v-if="!data.customSlotName"
							class="custom-tree-node"
						>
							<span
								v-if="treeType === 'customIconList' && customIconDefault"
								class="custom-list-icon-box"
							>
								<IconFont
									class="custom-list-icon"
									:type="customIconDefault"
								/>
							</span>
							<span
								v-if="treeType === 'customIconList' && !customIconDefault && data.icon"
								class="custom-list-icon-box"
							>
								<IconFont
									class="custom-list-icon"
									:type="data.icon"
								/>
							</span>
							<div
								class="node-label"
								:class="{ 'no-dropdown': !showDropdown || data.disabled || data.disabledDropdown }"
								:title="node.label"
							>
								<div class="node-label-text">{{ node.label }}</div>
								<slot
									name="node-right"
									:node="node"
									:data="data"
								></slot>
							</div>
							<span
								v-if="showDropdown && !data.disabled && !data.disabledDropdown"
								@click.stop
							>
								<slot
									name="dropdown"
									:node="node"
									:data="data"
								></slot>
							</span>
						</span>
						<span
							v-else
							class="custom-tree-node"
							:class="data.customSlotClass"
						>
							<span class="node-label">
								<slot
									:name="data.customSlotName"
									:node="node"
									:data="data"
								></slot>
							</span>
						</span>
					</template>
				</el-tree>
			</el-scrollbar>
		</div>
	</div>
</template>
<script setup lang="ts">
import { ref, watch, computed, Component, nextTick, shallowRef, onMounted } from 'vue';
import { TreeInstance } from 'element-plus';
import { Plus, ArrowRight, Search } from '@element-plus/icons-vue';
import type Node from 'element-plus/es/components/tree/src/model/node';
import { TreeOptionProps } from 'element-plus/es/components/tree/src/tree.type';

export type TreeType = 'tree' | 'list' | 'customIconList'; //tree为树形，list为无左侧icon的列表，customIconList为可以自定义列表左侧icon的列表
interface TreeListItem {
	icon?: string; //TreeType =customIconList时,自定义每一项的icon
	disabled?: boolean; //禁用节点
	node_disabled_selected?: boolean; //节点是否可以选中
	disabledDropdown?: boolean; //禁用节点上的下拉菜单
	customSlotName?: string; //自定义节点的slot名称
	customSlotClass?: string; //自定义节点的class
	[property: string]: any;
}
interface TreeProps {
	expandOnClickNode?: boolean; //是否在点击节点的时候展开或者收缩节点， 默认值为 true，如果为 false，则只有点箭头图标的时候才会展开或者收缩节点。
	headerAddTips?: string; //头部新增按钮tips
	showHeader?: boolean; //头部搜索区域
	showAddBtn?: boolean; //头部新增按钮
	showBtnChange?: boolean; //按钮切换
	showDropdown?: boolean; //列表后三个点操作
	treeList: TreeListItem[]; //树的数据
	props?: TreeOptionProps;
	nodeKey?: string; //树的唯一标识
	treeType?: TreeType;
	customIconDefault?: string; //自定义icon列表的默认icon
}

const customIcon = shallowRef<string | Component>(ArrowRight); //tree类型的时候的icon
const filterText = ref(''); //过滤字段
const treeRef = ref<TreeInstance>(); //tree的实例

const tree_props = withDefaults(defineProps<TreeProps>(), {
	expandOnClickNode: false,
	headerAddTips: '添加',
	showHeader: true,
	showAddBtn: true,
	showBtnChange: true,
	showDropdown: true,
	treeList: () => {
		return [];
	},
	props: () => {
		return {};
	},
	treeType: 'tree',
	customIconDefault: '',
});
const newDefaultProps = computed(() => {
	const classFun = (data: any, node: Node) => {
		if (data.disabled) {
			return 'custom-tree-node--disabled';
		}
		if (data.node_disabled_selected) {
			return 'custom-tree-node--allow_selected';
		}
	};
	return { ...tree_props.props, class: classFun };
});
const treeViewHeight = computed(() => {
	let height = '100%';
	if (tree_props.showHeader || tree_props.showBtnChange) {
		height = 'calc(100% - 40px - 10px)';
	}
	if (!tree_props.showHeader && !tree_props.showBtnChange) {
		height = 'calc(100% - 10px)';
	}
	if (tree_props.showHeader && tree_props.showBtnChange) {
		height = 'calc(100% - 40px - 40px - 10px)';
	}
	return height;
});
watch(filterText, (val) => {
	treeRef.value!.filter(val);
});
type EmitProps = {
	(e: 'node-click', data: any, node: Node): void;
	(e: 'header-add-click'): void;
};
const emits = defineEmits<EmitProps>();
// -------------树组件的事件------------------
//过滤事件
const filterNode = (value: string, data: any, node: Node) => {
	if (!value) return true;
	if (typeof tree_props.props?.label === 'string') {
		return data[tree_props.props?.label] && data[tree_props.props?.label].includes(value);
	} else if (typeof tree_props.props?.label === 'function') {
		const labelFun = tree_props.props.label(data, node);
		return data[labelFun] && data[labelFun].includes(value);
	} else {
		return data.label && data.label.includes(value);
	}
};
//头部新增按钮事件
const headerAddClick = () => {
	emits('header-add-click');
};
//点击节点事件
const nodeClick = (data: any, node: Node) => {
	if (data.disabled || data.node_disabled_selected) {
		return;
	}
	emits('node-click', data, node);
};
// -------------树组件的方法-------------------
//清除搜索过滤
const clearFilterText = () => {
	filterText.value = '';
};
//搜索过滤
const filterTree = () => {
	nextTick(() => {
		filterText.value && treeRef.value!.filter(filterText.value);
	});
};
//获取当前选中的节点的数据
const getCurrentKey = () => {
	return treeRef.value?.getCurrentKey();
};
//获取当前选中的节点node
const getCurrentNode = () => {
	return treeRef.value?.getCurrentNode();
};
//根据唯一标识设置当前选中选
const setCurrentKey = (key: string | number, shouldAutoExpandParent: boolean = true) => {
	treeRef.value?.setCurrentKey(key, shouldAutoExpandParent);
};
//根据节点node设置当前选中选
const setCurrentNode = (node: Node, shouldAutoExpandParent: boolean = true) => {
	treeRef.value?.setCurrentNode(node, shouldAutoExpandParent);
};
//获取组件实例
const getTreeRef = () => {
	return treeRef.value;
};

// ----------
var fixedLeft = ref(0);
onMounted(() => {
	nextTick(() => {
		/**
		 * TODO 此计算暂未适配窗口变化问题（左侧菜单栏折叠）
		 * 计算得出树控件功能菜单的位置
		 * 计算公式：
		 * X = （当前树控件左侧距离屏幕的位置） + （当前树控件的宽度） - （菜单按钮的宽度【当前固化为24px】）
		 */
		const element = treeRef.value;
		if (element) {
			// 获取元素的宽度
			const width = element.el$.offsetWidth;
			// 获取元素距离屏幕的位置
			const positionLeft = element.el$.getBoundingClientRect().left;

			fixedLeft.value = Math.round(positionLeft + width - 24);
		}
	});
});

const operationBtnStyleConfig = computed(() => {
	console.log(fixedLeft);
	return {
		'--fixed-left': fixedLeft.value + 'px',
	};
});

defineExpose({
	clearFilterText,
	filterTree,
	getCurrentKey,
	getCurrentNode,
	setCurrentKey,
	setCurrentNode,
	getTreeRef,
});
</script>
<style lang="scss" scoped>
.operation-tree {
	width: 100%;
	height: 100%;
	.header-view {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 8px 20px 0;
		:deep .add-btn {
			margin-left: 8px;
			border-radius: 2px;
			background-color: #0075c2;
			padding: 8px 7px;
			&:hover {
				background-color: #79bbff;
			}
			.el-icon {
				font-size: 18px;
				font-weight: bold;
				color: #ffffff;
			}
		}
	}
	.class-change-view {
		padding: 8px 20px 0;
	}
	.tree-view {
		padding-top: 8px;
		// height: calc(100% - 50px);
		overflow: hidden;

		:deep .el-tree {
			--el-tree-node-content-height: 40px;
			--el-tree-node-hover-bg-color: #f2f3f5;
			--el-tree-text-color: #1d2129;
			cursor: default;
			height: 100%;

			.el-tree-node__expand-icon {
				color: #86909c;
				font-size: 16px;
				padding: 0;
				margin: 0 8px 0 20px;
			}
			.el-tree-node.is-current > .el-tree-node__content {
				background-color: #f1f9ff;
				.node-label,
				.el-tree-node__expand-icon {
					color: #0075c2;
				}
			}
			.el-tree-node.custom-tree-node--disabled > .el-tree-node__content {
				background-color: #ffffff;
				cursor: not-allowed;
				.node-label {
					color: #c0c4cc;
				}
				.el-tree-node__expand-icon {
					color: #86909c;
				}
			}
			.el-tree-node.custom-tree-node--allow_selected > .el-tree-node__content {
				background-color: #ffffff;
				.node-label {
					color: #1d2129;
				}
				.el-tree-node__expand-icon {
					color: #86909c;
				}
			}
			.el-tree-node__content:hover {
				.node-label {
					width: calc(100% - 30px);
				}
				.no-dropdown {
					width: calc(100% - 20px);
				}
				.el-dropdown {
					display: block;
				}
			}
			.custom-tree-node {
				display: flex;
				flex: 1;
				overflow: hidden;
				align-items: center;
				justify-content: space-between;
				height: 100%;
				// min-width: 0;
				.node-label {
					display: flex;
					align-items: center;
					width: calc(100% - 20px);
					overflow: hidden;
					.node-label-text {
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}
				.el-dropdown {
					display: none;
					background-color: #ffffff;
					// right: 12px;
					// position: absolute;
					position: fixed;
					width: 24px;
					height: 16px;
					left: var(--fixed-left);
					margin-top: -8px;
					.el-dropdown-link {
						display: flex;
						align-items: center;
						justify-content: center;
						cursor: pointer;
						color: #0075c2;
					}
					.el-dropdown__popper.el-popper {
						background: #ffffff;
						box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
						border: 1px solid #e5e6eb;
						.el-dropdown-menu__item {
							font-weight: 400;
							font-size: 14px;
							color: #1d2129;
							line-height: 22px;
							padding: 5px 14px;
							&:hover {
								background: #f2f3f5;
								color: #1d2129;
							}
							&:active,
							&:visited {
								background: #f1f9ff;
								color: #0075c2;
							}
						}
						.el-dropdown-menu__item.is-disabled {
							color: #c0c4cc;
						}
					}
				}
			}
		}
	}
	.list-view {
		:deep .el-tree {
			.el-tree-node__expand-icon {
				display: none;
			}
			.custom-tree-node {
				padding-left: 20px;
				.node-label {
					width: 100%;
					padding-right: 20px;
				}
			}
			.el-tree-node__content:hover {
				.node-label {
					width: 100%;
					padding-right: 30px;
				}
				.no-dropdown {
					padding-right: 20px;
				}
			}
		}
	}
	.custom-icon-list-view {
		:deep .el-tree {
			.el-tree-node__expand-icon {
				display: none;
			}
			.custom-tree-node {
				padding-left: 20px;
				.custom-list-icon-box {
					color: #1d2129;
					font-size: 16px;
					padding: 0;
					margin: 0 8px 0 0;
					display: flex;
					align-items: center;
				}
				.node-label {
					width: 100%;
					padding-right: 20px;
				}
			}

			.el-tree-node__content:hover {
				.node-label {
					width: 100%;
					padding-right: 30px;
				}
				.no-dropdown {
					padding-right: 20px;
				}
			}
			.el-tree-node.is-current > .el-tree-node__content {
				.custom-list-icon-box {
					color: #0075c2;
				}
			}
			.el-tree-node.custom-tree-node--disabled > .el-tree-node__content {
				.custom-list-icon-box {
					color: #86909c;
				}
			}
		}
	}
}
</style>
<style lang="scss">
.custom-operatiom-tree {
	width: 100%;
	overflow-x: auto;

	.el-tree-node__content:hover {
		.el-dropdown {
			background-color: #f2f3f5 !important;
		}
	}

	.el-tree-node.is-current > .el-tree-node__content {
		.el-dropdown {
			background-color: #f1f9ff !important;
		}
	}
}

.custom-operatiom-tree > .el-tree-node {
	display: inline-block;
}
</style>
