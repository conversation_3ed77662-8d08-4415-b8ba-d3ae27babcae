<template>
	<el-popover placement="bottom-end" :width="200" trigger="click">
		<template #reference>
			<el-icon class="t-setting-icon"><CaretBottom /> </el-icon>
		</template>
		<el-collapse v-model="collapseKeys" class="t-collapse">
			<el-collapse-item name="line" title="行高">
				<el-radio-group v-model="tableSize" @change="onChangeSize">
					<el-radio value="small">紧凑</el-radio>
					<el-radio value="default">中等</el-radio>
					<el-radio value="large">宽松</el-radio>
				</el-radio-group>
			</el-collapse-item>
			<el-collapse-item name="column">
				<template #title>
					<div class="t-collapse-column">
						显示列
						<el-button type="primary" v-show="selectColumnKeys.length !== columnOptions.length" link @click.stop="onResetColumn">
							重置
						</el-button>
					</div>
				</template>
				<el-checkbox-group v-model="selectColumnKeys" class="t-collapse-column-box" @change="onChangeColumn">
					<el-checkbox v-for="item in columnOptions" :label="item.label" :value="item.value" />
				</el-checkbox-group>
			</el-collapse-item>
		</el-collapse>
	</el-popover>
</template>

<script lang="tsx" setup>
import { ref, watch, computed } from 'vue';
import { CaretBottom } from '@element-plus/icons-vue';
import { useRoute } from 'vue-router';
interface Props {
	soleKey?: string; // 唯一的key
	size?: 'large' | 'default' | 'small';
	columnOptions: OptionItem[];
}
const props = withDefaults(defineProps<Props>(), {
	size: 'default',
});
const emit = defineEmits<{
	(ev: 'change', newFilters: any): void; //
}>();
const route = useRoute();
const storageKey = computed(() => props.soleKey || `table_key_${route.fullPath}`);
const hideColumnKeys = ref<Array<string | number>>([]); // 隐藏的column的 keys 之所以用隐藏的key 是因为判断起来更稳妥 新增的列也不用重新
const selectColumnKeys = ref<Array<string | number>>([]); // 选中的column的 keys
const collapseKeys = ref(['column']);
const tableSize = ref(props.size); //  'large' | 'default' | 'small'
let allColumnKeys: Array<string | number> = [];
watch(
	() => props.size,
	(val) => {
		tableSize.value = val;
	},
);
watch(
	() => props.columnOptions,
	(val) => {
		allColumnKeys = val.map((item) => item.value);
		if (hideColumnKeys.value.length) {
			selectColumnKeys.value = allColumnKeys.filter((key1) => !hideColumnKeys.value.includes(key1));
		} else {
			selectColumnKeys.value = [...allColumnKeys];
		}
	},
	{
		immediate: true,
	},
);

watch(storageKey, getLocalStorage, { immediate: true });
// 获取本地保存的 数据
function getLocalStorage(key: string | undefined) {
	if (!key) return;
	const objStr = localStorage.getItem(key);
	if (!objStr) return;
	try {
		const obj = JSON.parse(objStr);
		hideColumnKeys.value = obj.hideColumnKeys || [];
		tableSize.value = obj.tableSize || props.size;
		if (obj.hideColumnKeys.length || obj.tableSize) {
			emit('change', obj);
		}
	} catch (error) {
		console.log('111111111111 getLocalStorage', error);
	}
	// 获取完 本地保存数据后，等 selectColumnKeys.value 完成后，再判断一下
	if (hideColumnKeys.value.length) {
		setTimeout(() => {
			selectColumnKeys.value = selectColumnKeys.value.filter((key1) => !hideColumnKeys.value.includes(key1));
		}, 10);
	}
}
function setLocalStorage() {
	const obj = {
		tableSize: tableSize.value,
		hideColumnKeys: hideColumnKeys.value,
	};
	emit('change', obj);
	localStorage.setItem(storageKey.value, JSON.stringify(obj));
}

function onChangeSize() {
	setLocalStorage();
}

function onChangeColumn() {
	hideColumnKeys.value = allColumnKeys.filter((key1) => !selectColumnKeys.value.includes(key1));
	setLocalStorage();
}
function onResetColumn() {
	hideColumnKeys.value = [];
	selectColumnKeys.value = [...allColumnKeys];
	setLocalStorage();
}
</script>
<style lang="scss" scoped>
.t-setting-icon {
	flex-shrink: 0;
	cursor: pointer;
	justify-items: flex-end;
	margin-left: 10px;
	font-size: 12px;
	margin-right: 4px;
}
.t-collapse {
	border: none;
	:deep(.el-collapse-item__header) {
		position: relative;
		padding-left: 22px;
		border: none;
		line-height: 1;
		height: 32px;
		.el-collapse-item__arrow {
			position: absolute;
			width: 13px;
			height: 13px;
			margin: auto;
			top: 0;
			bottom: 2px;
			left: 4px;
		}
	}
	:deep(.el-collapse-item__wrap) {
		border: none;
		.el-collapse-item__content {
			padding: 0px 0px 10px 20px;
		}
	}
	:deep(.el-checkbox-group) {
		> .el-checkbox {
			margin: 0;
			width: 100%;
			.el-checkbox__label {
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}
	:deep(.el-radio-group) {
		> .el-radio {
			margin: 0;
			width: 100%;
			.el-checkbox__label {
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}
	.t-collapse-column {
		display: flex;
		align-items: center;
		justify-content: space-between;
		flex-grow: 1;
	}
	.t-collapse-column-box {
		max-height: 300px;
		overflow: auto;
	}
}
</style>
