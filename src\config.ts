// 一些配置项
const envCfg = import.meta.env;
console.log('项目初始化，加载 config.ts。envCfg：', envCfg);
export const isDev = envCfg.DEV; // 是否是开发环境

// 所有路径 “/”默认在前，如果 BASE_URL 是 “/” 拼接路径时可能会出现两个 “/” 如：'//page'
export const BASE_URL = envCfg.BASE_URL; //  当前环境地址后缀
// let appName = envCfg.VITE_appName;
// export { appName };
export const rootOrigin = location.origin + (BASE_URL === '/' ? '' : BASE_URL);
export const rootUri = location.origin;
export const tokenTimeout = 15; // token过期时间 前台验证 单位 小时
export const defaultText = '-'; // 表格，表单等没有数据时显示的文字
export const defaultFormatMonth = 'YYYY-MM'; // 默认的时间格式 日期
export const defaultFormatDate = 'YYYY-MM-DD'; // 默认的时间格式 日期
export const defaultFormatTime = 'HH:mm:ss'; // 默认的时间格式 时间
export const defaultFormatAll = 'YYYY-MM-DD HH:mm:ss'; // 默认的时间格式 日期带时间
export const maxUploadPartSize = 5; // Mb   文件超过这个大小后，文件就会分片上传
export const maxUploadPartStepSize = 3; // Mb   文件超过这个大小后，分片上传每片大小
export const passWordMaxLength = 24; //    密码最大长度
export const passWordMinLength = 6; //  密码最小长度
export const getSmsCodeInterval = 61; // 获取验证码间隔时间

const systemTheme = 'primary'; // 系统默认主题主题，【default 菜单栏为白色样式】【primary 菜单栏为蓝色样式】
var title = '';
/**
 * 获取系统主题
 * 1. 优先从缓存获取，缓存没有，从配置文件读取
 **/
export function getSystemTheme() {
	var theme = localStorage.getItem('systemTheme');
	if (!theme) {
		theme = systemTheme;
	}
	return theme;
}

// 设置系统主题
export function setSystemTheme(val) {
	localStorage.setItem('systemTheme', val);
}

export function setTitle(val) {
	title = val;
}
export function getTitle() {
	return title
}

export const envConfig = {} as EnvConfig;
