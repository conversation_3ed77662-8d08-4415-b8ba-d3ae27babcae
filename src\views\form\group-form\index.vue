<template>
	<div class="h-full">
		<aside-main
			v-if="!showDetail"
			:titleText="viewTitle"
		>
			<template #aside>
				<tree-navigation :listData="listData">
					<template #tree-node-right="node">
						<el-icon><CollectionTag /></el-icon>
					</template>
				</tree-navigation>
			</template>
			<template #main>
				<common-table
					:createButtonText="createButtonText"
					:columns="columns"
					:fill-screen="true"
					:data="tableData"
					:pagination="true"
					v-bind="pageInfo"
					@onClickCreateBtn="createButtonClick"
					@page-current-change="onPageCurrentChange"
				>
				</common-table>
			</template>
		</aside-main>
		<header-main
			viewTitle="创建"
			@close="closeDetailPage"
			:showCloseBtn="true"
			v-else
		>
			<template #main>
				<EditForm />
			</template>
		</header-main>
	</div>
</template>
<script setup lang="tsx">
import EditForm from './components/edit-form.vue';
import { ref, reactive } from 'vue';
interface TreeDataItem {
	label: string; // 标题名称
	isCollect?: boolean; // 是否为收藏
	num?: string;
	children?: TreeDataItem[]; // 子级
}

interface DataItem {
	id: string;
	key1: string;
	key2: string;
	key3: string;
	key4: string;
	key5: string;
	key6: string;
	key7: string;
	key8: string;
	key9: string;
	key10: string;
	key11: string;
	key12: string;
}

const createButtonText = ref('创建任务');

// 左侧菜单数据
const listData: TreeDataItem[] = [
	{
		label: '示例种类1',
		isCollect: false,
		children: [
			{
				label: '示例种类1-1',
				isCollect: false,
			},
			{
				label: '示例种类1-1',
				isCollect: false,
			},
			{
				label: '示例种类1-1',
				isCollect: false,
			},
			{
				label: '示例种类1-1',
				isCollect: false,
			},
			{
				label: '示例种类1-1',
				isCollect: false,
			},
			{
				label: '示例种类1-1',
				isCollect: false,
			},
			{
				label: '示例种类1-1',
				isCollect: false,
			},
		],
	},
	{
		label: '示例种类2',
		isCollect: false,
	},
	{
		label: '示例种类3',
		isCollect: false,
	},
];

// 页面标题
const viewTitle = listData[0].label;

// 右侧表格数据
const tableData: DataItem[] = [
	{
		id: '1',
		key1: 'NJ0000001',
		key2: '轮式拖拉机轮式拖拉机轮式拖拉机轮式拖拉机轮式拖拉机',
		key3: '琼 03.D3585',
		key4: '张三',
		key5: '18509360198',
		key6: '14,941',
		key7: '正常',
		key8: '琼 03.D3585',
		key9: '张三',
		key10: '18509360198',
		key11: '14,941',
		key12: '正常',
	},
	{
		id: '2',
		key1: 'NJ0000002',
		key2: '插秧机',
		key3: '琼 03.D1234',
		key4: '李四',
		key5: '17741453682',
		key6: '11,892',
		key7: '正常',
		key8: '琼 03.D3585',
		key9: '张三',
		key10: '18509360198',
		key11: '14,941',
		key12: '正常',
	},
	{
		id: '3',
		key1: 'NJ0000003',
		key2: '插秧机',
		key3: '琼 03.D4234',
		key4: '王五',
		key5: '13523825258',
		key6: '16,086',
		key7: '正常',
		key8: '琼 03.D3585',
		key9: '张三',
		key10: '18509360198',
		key11: '14,941',
		key12: '正常',
	},
];
// 表格列名
const columns: CommonTableColumn<DataItem>[] = [
	{
		label: '农机编号',
		prop: 'key1',
		searchType: 'input',
		fixed: true,
		width: 200,
	},
	{
		label: '农机品目',
		prop: 'key2',
		searchType: 'inputAndSort',
		fixed: true,
		width: 200,
	},
	{
		label: '车牌号码',
		prop: 'key3',
		searchType: 'select',
		width: 200,
	},
	{
		label: '驾驶员',
		prop: 'key4',
		searchType: 'selectAndSort',
		width: 200,
	},
	{
		label: '联系方式',
		prop: 'key5',
		searchType: 'sort',
		width: 200,
	},
	{
		label: '终端号',
		prop: 'key6',
		searchType: 'input',
		width: 200,
	},
	{
		label: '状态',
		prop: 'key7',
		searchType: 'select',
		width: 200,
	},
	{
		label: '自定义列1',
		prop: 'key8',
		width: 200,
	},
	{
		label: '自定义列2',
		prop: 'key9',
		width: 200,
	},
	{
		label: '自定义列3',
		prop: 'key10',
		width: 200,
	},
	{
		label: '自定义列4',
		prop: 'key11',
		width: 200,
	},
	{
		label: '自定义列5',
		prop: 'key12',
		width: 200,
	},
	{
		label: '操作',
		width: 200,
		fixed: 'right',
		formatter: (row, column, cellValue, index) => {
			const btns: OptionBtn[] = [
				{
					label: '查看',
					auth: '',
					onClick() {
						console.log('点击了按钮', row);
					},
				},
				{
					label: '编辑',
					auth: '',
					onClick() {
						console.log('点击了按钮', row);
					},
				},
				{
					label: '删除',
					auth: '',
					tips: '确定删除吗？',
					placement: 'left-start',
					more: true,
					onClick() {
						console.log('点击了按钮', row);
					},
				},
			];

			return <i-btns btns={btns}></i-btns>;
		},
	},
];
// 分页数据
const pageInfo = reactive<PageInfo>({
	total: 3,
	pageNumber: 1,
	pageSize: 20,
});

// 展示详细页面
var showDetail = ref(false);

function onPageCurrentChange(val: number) {
	// getList()
	pageInfo.pageNumber = val;
	console.log('1111111111 onPageCurrentChange', val);
}

function createButtonClick(type) {
	showDetail.value = !showDetail.value;
}

// 关闭详情页面
function closeDetailPage() {
	showDetail.value = !showDetail.value;
}
</script>
<style lang="scss" scoped></style>
