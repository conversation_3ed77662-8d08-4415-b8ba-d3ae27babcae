<template>
	<header-main :viewTitle="showTitle" @close="close" :showCloseBtn="true">
		<template #main>
			<div class="edit-form">
				<div class="main">
					<el-form ref="formRef" :model="ruleForm" :rules="rules" class="form-view" label-width="108px"
						label-position="left">
						<el-form-item label="申请单位" prop="companyNumber">
							<el-select v-model="ruleForm.companyNumber" placeholder="请选择申请单位"
								@change="unitChangeAction">
								<el-option v-for="item in companyList" :key="item.uuid" :label="item.name"
									:value="item.uuid" />
							</el-select>
						</el-form-item>
						<el-form-item label="申请部门" prop="deptNumber">
							<el-select ref="deptRef" v-model="ruleForm.deptNumber" placeholder="请选择申请部门"
								:loading="deptLoading" @change="deptChangeAction"
								@click.native.prevent="handleDeptClick">
								<el-option v-for="item in deptList" :key="item.uuid" :label="item.name"
									:value="item.uuid" />
							</el-select>
						</el-form-item>
						<el-form-item label="合同名称" prop="title">
							<el-input v-model="ruleForm.title" placeholder="请输入合同名称" clearable />
						</el-form-item>
						<el-form-item label="合同编号" prop="bDescription">
							<el-input v-model="ruleForm.bDescription" placeholder="请输入合同编号" clearable />
						</el-form-item>
						<el-form-item label="承办人联系方式" prop="phone">
							<el-input v-model="ruleForm.phone" placeholder="请输入承办人联系方式" maxlength="11" clearable />
						</el-form-item>

						<el-form-item label="其它" prop="extendField2">
							<el-checkbox v-model="ruleForm.extendField2" true-value="1"
								false-value="0">需要招采部审核</el-checkbox>
						</el-form-item>

						<el-form-item label="备注" prop="extendField1">
							<el-input rows="4" resize="vertical" maxlength="80" show-word-limit
								v-model="ruleForm.extendField1" placeholder="请输入备注" type="textarea" />
						</el-form-item>
						<el-form-item label="附件" prop="fileList">
							<el-upload v-model:file-list="fileList" class="upload-file"
								action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15" multiple
								:on-preview="handlePreview" :on-remove="handleRemove" :before-remove="beforeRemove"
								:limit="3" accept="pdf、jpg、png、gif、doc、docx、xls、xlsx">
								<div class="xirui-flex-row-center">
									<el-button>上传文件</el-button>
									<div class="file-tip">支持扩展名：pdf、jpg、png、gif、doc、docx、xls、xlsx</div>
								</div>
								<!-- <template #tip>
							<div class="el-upload__tip">jpg/png files with a size less than 500KB.</div>
						</template> -->
							</el-upload>
						</el-form-item>
					</el-form>
				</div>
				<div class="footer">
					<div class="footer-view">
						<el-button type="primary" :loading="submitLoading" @click="submitAction">提交</el-button>
						<template v-if="isEdit">
							<el-button :loading="saveLoading" @click="saveAction(false)">保存</el-button>
							<el-button type="danger" plain :loading="deleteLoading" @click="deleteAction">删除</el-button>
						</template>
						<template v-else>
							<el-button :loading="saveLoading" @click="saveAction(false)">保存草稿</el-button>
							<el-button @click="cancelAction">取消</el-button>
						</template>
					</div>
				</div>
			</div>
		</template>
	</header-main>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules, UploadProps, UploadUserFile } from 'element-plus';
import { api_getUnitsList, api_getDepartsList, api_postAddSave, api_postEditSave, api_postAddOrEditSubmit, api_getApprovalDetail, api_postApprovalDelete } from '../api';
import { ResponseUnitInfo, ResponseDepartInfo } from '../api/types';

interface Props {
	title: string; // 标题
	type: string; // 类型  add:新增 edit:编辑
	rowID: string; // 表单id
}
const props = withDefaults(defineProps<Props>(), {
	title: '合同审批',
	type: 'add',
	rowID: ''
});

const emit = defineEmits<{
	(e: 'close'): void;
	(e: 'success'): void;
}>();

// 监听 type 和 rowID 变化，自动请求详情
watch(
	() => [props.type, props.rowID], // 监听的依赖数组
	([newType, newId]) => {
		if (newType === 'edit' && newId) {
			// 当类型为编辑且 id 存在时请求详情
			getDetail(newId)
		} else {
			// 非编辑状态时清空详情数据
		}
	},
	{ immediate: true } // 组件初始化时立即执行一次
)

// 计算属性
const isEdit = computed(() => props.type === 'edit');
const showTitle = computed(() => {
	let tempTitle = isEdit.value ? '修改' : '增加';
	return tempTitle + props.title;
});

const companyList = ref<ResponseUnitInfo[]>([]); // 单位列表
const deptList = ref<ResponseDepartInfo[]>([]); // 部门列表
const deptRef = ref(null);

const deptLoading = ref(false); // 部门列表加载状态
const saveLoading = ref(false); // 保存按钮加载状态
const submitLoading = ref(false); // 提交按钮加载状态
const deleteLoading = ref(false); // 删除按钮加载状态

interface RuleForm {
	extendField1: string; //备注
	title: string; //合同名称
	bDescription: string; //合同编号
	companyNumber: string;
	companyName: string;
	deptNumber: string;
	deptName: string;
	extendField2: string;
	phone: string;
	// fileList: any[];
	extra_files: any[];
	cNumber: string;
	projectName: string;
	bFormFlag: number;
	rowID: string;
	subProjectID: string;
	billDetails: any[];
}
const formRef = ref<FormInstance>();
const ruleForm = reactive<RuleForm>({
	extendField1: '', //备注
	title: '', //合同名称
	bDescription: '', //合同编号
	companyNumber: '',
	companyName: '',
	deptNumber: '',
	deptName: '',
	extendField2: '1',
	phone: '',
	// fileList: [],
	extra_files: [],
	cNumber: 'CB021',
	projectName: '',
	bFormFlag: 21,
	rowID: '',
	subProjectID: '',
	billDetails: [],
});
const rules = reactive<FormRules<RuleForm>>({
	companyNumber: [
		{
			required: true,
			message: '请选择申请单位',
			trigger: 'change',
		},
	],
	deptNumber: [
		{
			required: true,
			message: '请选择申请部门',
			trigger: 'change',
		},
	],
	title: [
		{
			required: true,
			message: '请输入合同名称',
			trigger: 'blur',
		},
	],
	bDescription: [
		{
			required: true,
			message: '请输入合同编号',
			trigger: 'blur',
		},
	],
	phone: [
		{
			required: true,
			message: '请输入承办人联系方式',
			trigger: 'blur',
		},
		{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
	],
	extendField2: [
		{
			required: true,
			message: '',
			trigger: 'change',
		},
	],
	extendField1: [
		{
			required: false,
			message: '请输入备注内容',
			trigger: 'blur',
		},
	],
	fileList: [{ required: false, message: '请上传附件', trigger: 'blur' }],
});

// 单位切换
function unitChangeAction(newValue: any) {
	// 根据新value匹配对应的选项对象
	const selectedOption = companyList.value.find((item) => item.uuid === newValue) || {};
	ruleForm.companyName = selectedOption.name || '';
	// 重置部门数据
	ruleForm.deptNumber = '';
	ruleForm.deptName = '';
	// 获取部门数据
	getDepartsList(ruleForm.companyNumber);
}
// 部门切换
function deptChangeAction(newValue: any) {
	const selectedOption = deptList.value.find((item) => item.uuid === newValue) || {};
	ruleForm.deptName = selectedOption.name || '';
}

const resetForm = () => {
	formRef.value?.resetFields();
	Object.assign(ruleForm, {
		extendField1: '', //备注
		title: '', //合同名称
		bDescription: '', //合同编号
		companyNumber: '',
		companyName: '',
		deptNumber: '',
		deptName: '',
		extendField2: '1',
		phone: '',
		// fileList: [],
		extra_files: [],
		cNumber: 'CB021',
		projectName: '',
		bFormFlag: 21,
		rowID: '',
		subProjectID: '',
		billDetails: [],
	});
};
// 保存/保存草稿
function saveAction(isSubmit = false) {
	formRef.value.validate((valid) => {
		if (valid) {
			// 校验通过，执行提交逻辑
			console.log('提交的数据:', ruleForm);
			saveForm(isSubmit); // 提交时，先保存，再提交 
		} else {
			// 校验失败
			ElMessage.error('表单校验失败，请检查输入');
			console.log('提交的数据:', ruleForm);
		}
	});
}
// 提交
function submitAction() {
	saveAction(true);
}

// 删除
function deleteAction() {
	ElMessageBox.confirm('确定要删除吗？', '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
	})
		.then(() => {
			deleteForm();
		})
		.catch(() => {
		})
}
// 取消
function cancelAction() {
	close();
}

// 关闭页面
function close() {
	resetForm();
	emit('close');
}

// 操作成功：保存/保存草稿/提交/删除
function optionSuccess() {
	// ElMessage.success('操作成功');
	emit('success');
}

function handleDeptClick() {
	// 判断其他变量是否有值
	if (!ruleForm.companyNumber) {
		// 无值：提示并不展开
		ElMessage.warning('请先选择申请单位');
		return;
	}

	// 有值：手动触发下拉框展开
	// el-select 内置方法 togglePopup() 可切换展开/收起状态
	if (deptRef.value) {
		deptRef.value.togglePopup();
	}
}



const fileList = ref<UploadUserFile[]>([
	// {
	// 	name: 'element-plus-logo.svg',
	// 	url: 'https://element-plus.org/images/element-plus-logo.svg',
	// },
	// {
	// 	name: 'element-plus-logo2.svg',
	// 	url: 'https://element-plus.org/images/element-plus-logo.svg',
	// },
]);

const handleRemove: UploadProps['onRemove'] = (file, uploadFiles) => {
	console.log(file, uploadFiles);
};

const handlePreview: UploadProps['onPreview'] = (uploadFile) => {
	console.log(uploadFile);
};

const handleExceed: UploadProps['onExceed'] = (files, uploadFiles) => {
	ElMessage.warning(
		`The limit is 3, you selected ${files.length} files this time, add up to ${files.length + uploadFiles.length} totally`,
	);
};

const beforeRemove: UploadProps['beforeRemove'] = (uploadFile, uploadFiles) => {
	return ElMessageBox.confirm(`Cancel the transfer of ${uploadFile.name} ?`).then(
		() => true,
		() => false,
	);
};

onMounted(() => {
	getUnitsList();
});

// 获取详情
function getDetail(rowID: string) {
	api_getApprovalDetail(rowID).then((res) => {
		console.log('获取详情成功', res);
		// companyList.value = res;
		const data = res[0];
		ruleForm.rowID = rowID;
		ruleForm.extendField1 = data.extendField1;
		ruleForm.title = data.title;
		ruleForm.extendField2 = data.extendField2;
		ruleForm.phone = data.phone;
		ruleForm.bDescription = data.bDescription;
		ruleForm.extra_files = data.extra_files;
		ruleForm.companyName = data.companyName;
		ruleForm.companyNumber = data.companyNumber;
		ruleForm.deptNumber = data.deptNumber;
		ruleForm.deptName = data.deptName;
		getDepartsList(ruleForm.companyNumber);
		// createName = data.creatorName
	});
}

// 获取单位列表
function getUnitsList() {
	api_getUnitsList().then((res) => {
		companyList.value = res;
	});
}

// 获取部门列表
function getDepartsList(unitId = '') {
	deptLoading.value = true;
	api_getDepartsList(unitId).then((res) => {
		deptList.value = res;
	}).finally(() => {
		deptLoading.value = false;
	});
}

// 保存/保存草稿表单
function saveForm(isSubmit = false) {
	if (!isSubmit) {
		saveLoading.value = true;
	}
	let url = api_postAddSave;
	if (isEdit.value) {
		url = api_postEditSave;
	}
	url(ruleForm, !isSubmit).then((res: any) => {
		console.log('保存成功:', res);
		if (isSubmit) {
			// 提交
			ruleForm.rowID = res[0].rowID;
			submitForm();
		} else {
			// 保存成功
			// ElMessage.success('保存成功');
			optionSuccess();
		}

	}).finally(() => {
		saveLoading.value = false;
	});
}

// 提交表单
function submitForm() {
	submitLoading.value = true;
	api_postAddOrEditSubmit(ruleForm.rowID).then((res: any) => {
		optionSuccess();
	}).finally(() => {
		submitLoading.value = false;
	});
}
// 删除表单
function deleteForm() {
	deleteLoading.value = true;
	api_postApprovalDelete(ruleForm.rowID).then((res: any) => {
		optionSuccess();
	}).finally(() => {
		deleteLoading.value = false;
	});
}
</script>
<style lang="scss" scoped>
.edit-form {
	display: flex;
	flex-direction: column;
	height: 100%;

	.main {
		flex: 1;
		min-height: 0;
		overflow: auto;

		.form-view {
			width: 777px;
			margin: 20px auto;
			padding-right: 150px;
		}

		.upload-file {
			width: 100%;

			.file-tip {
				margin-left: 15px;
				font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
				font-weight: 400;
				font-size: 14px;
				color: #86909c;
				line-height: 14px;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}
		}
	}

	.footer {
		border-top: 1px solid #e5e6eb;
		height: 64px;
		display: flex;
		align-items: center;

		.footer-view {
			width: 777px;
			margin: 0 auto;
		}
	}
}
</style>
