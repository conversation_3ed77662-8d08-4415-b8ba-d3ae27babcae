<template>
	<el-sub-menu
		v-if="hasChildrenMenu(itemObj.children)"
		:index="String(itemObj.name)"
		:key="itemObj.name"
		:disabled="!!itemObj.meta?.disabled"
	>
		<template #title>
			<menu-icon-font
				v-if="judgeShowIcon(itemObj)"
				:type="formatMenuIcon(itemObj)"
			/>
			<span
				class="t-text"
				:title="title"
				>{{ title }}</span
			>
		</template>
		<template v-for="item_ in itemObj.children">
			<menu-item
				:systemTheme="systemTheme"
				v-if="!item_.meta?.hidden"
				:key="item_.name"
				:isFirst="false"
				:itemObj="item_"
			/>
		</template>
	</el-sub-menu>
	<el-menu-item
		v-else-if="onlyOneChildren(itemObj.children)"
		:index="String(itemObj.children && itemObj.children[0].name)"
		:disabled="itemObj.children && !!itemObj.children[0].meta?.disabled"
	>
		<menu-icon-font
			v-if="judgeShowIcon(itemObj)"
			:type="formatMenuIcon(itemObj)"
		/>
		<span
			class="t-text"
			:title="childrenTitle"
			>{{ childrenTitle }}</span
		>
	</el-menu-item>
	<el-menu-item
		v-else
		:index="String(itemObj.name)"
		:disabled="!!itemObj.meta?.disabled"
	>
		<menu-icon-font
			v-if="judgeShowIcon(itemObj)"
			:type="formatMenuIcon(itemObj)"
		/>
		<span
			class="t-text"
			:title="title"
			>{{ title }}</span
		>
	</el-menu-item>
</template>

<script lang="ts" setup>
import { RouteRecordRaw } from 'vue-router';
import { isArray } from '@/utils/is';
import { defaultText } from '@/config';
import { computed, PropType } from 'vue';
interface Props {
	itemObj: RouteRecordRaw;
	isFirst?: boolean;
	systemTheme: string;
}
const props = withDefaults(defineProps<Props>(), {
	isFirst: true,
});

const title = computed(() => (props.itemObj.meta?.title as string) || defaultText);
const childrenTitle = computed(() => (props.itemObj.children && (props.itemObj.children[0].meta?.title as string)) || defaultText);

//子菜单至少有一个需要展示的时候
const hasChildrenMenu = (arr?: RouteRecordRaw[]) => {
	const hasChildren = isArray(arr) && arr.length >= 1;
	return hasChildren && arr!.some((item) => !item.meta?.hidden);
};
//子菜单只有一个且不需要展示的时候
const onlyOneChildren = (arr?: RouteRecordRaw[]) => {
	const isOneChildren = isArray(arr) && arr.length === 1;
	return isOneChildren && arr[0].meta?.hidden;
};

// 判断是否展示图标
function judgeShowIcon(itemObj) {
	if (props.isFirst || itemObj.meta?.icon || itemObj.meta?.primaryIcon) {
		return true;
	} else {
		return false;
	}
}

// 设置图标
function formatMenuIcon(itemObj) {
	if (props.systemTheme === 'primary') {
		return itemObj.meta.primaryIcon;
	} else {
		return itemObj.meta.icon;
	}
}
</script>
<style lang="scss" scoped></style>
