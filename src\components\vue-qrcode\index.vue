<template>
	<div class="t-text-box" ref="refDomBox">
		<vueQr :text="text" :size="size_"></vueQr>
	</div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';
import vueQr from 'vue-qr/src/packages/vue-qr.vue';
interface Props {
	text: string;
	size?: number;
}
const props = withDefaults(defineProps<Props>(), {});
const refDomBox = ref<HTMLDivElement>();
onMounted(setSize);
watch(() => props.size, setSize);
const size_ = ref(props.size);
function setSize() {
	size_.value = props.size || refDomBox.value?.offsetWidth;
}
</script>
<style lang="scss" scoped>
.t-text-box {
	text-align: center;
}
</style>
