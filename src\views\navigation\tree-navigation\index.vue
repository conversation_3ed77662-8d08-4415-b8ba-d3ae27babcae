<template>
	<div class="navigation-list-template">
		<div class="demo-t">
			<div class="header-r">默认导航样式</div>

			<div class="main-r">
				<tree-navigation :listData="data"></tree-navigation>
			</div>
		</div>
	</div>
</template>
<script setup lang="tsx">
import { ref, reactive } from 'vue';
interface DataItem {
	label: string; // 标题名称
	num?: number; // 角标数量
	children?: DataItem[]; // 子级
}
const data: DataItem[] = [
	{
		label: '示例种类1',
		num: 12,
		children: [
			{
				label: '示例种类1-1',
				num: 12,
			},
			{
				label: '示例种类1-1',
				num: 12,
			},
			{
				label: '示例种类1-1',
				num: 12,
			},
			{
				label: '示例种类1-1',
				num: 12,
			},
			{
				label: '示例种类1-1',
				num: 12,
			},
			{
				label: '示例种类1-1',
				num: 12,
			},
			{
				label: '示例种类1-1',
				num: 12,
			},
		],
	},
	{
		label: '示例种类2',
		num: 102,
	},
	{
		label: '示例种类3',
		num: 0,
	},
];
</script>
<style lang="scss" scoped>
.navigation-list-template {
	width: 100%;
	height: 100%;
	padding: 8px;

	display: flex;

	.demo-t {
		width: 240px;
		height: calc(100% - 16px);

		flex-shrink: 0;

		display: flex;
		flex-direction: column;
		margin-right: 20px;

		& > .header-r {
			padding: 0 20px;
		}

		& > .main-r {
			margin-top: 8px;
			border: 1px solid rgba($color: #000000, $alpha: 0.1);
			height: 0;
			flex-grow: 1;
		}
	}
}
</style>
