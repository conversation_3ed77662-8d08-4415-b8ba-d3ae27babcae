<template>
	<div
		class="menu-container"
		:class="systemTheme + '-theme'"
	>
		<div class="menu-content">
			<!-- <div class="menu-header">
				<img class="logo-img" :src="imgUrl" alt="" />
				<img v-if="!collapsed" class="logo-title" :src="titleImgUrl" alt="" />
			</div> -->
			<el-menu
				class="menu-main"
				:collapse="collapsed"
				:unique-opened="true"
				:default-active="selectMenu"
				@select="onSelect"
				@open="handleOpen"
				@close="handleClose"
			>
				<template v-for="item in (storeMenus as unknown as RouteRecordRaw[])">
					<MenuItem
						v-if="!item.meta?.hidden"
						:systemTheme="systemTheme"
						isFirst
						:itemObj="item"
					/>
				</template>
			</el-menu>
		</div>
		<div class="menu-footer">
			<ul
				class="el-menu el-menu--vertical menu-main v-enter-to"
				:class="{ 'el-menu--collapse': collapsed }"
			>
				<!-- <app-center :systemTheme="systemTheme" :appList="appList" />
				<message-center :systemTheme="systemTheme" />
				<system-config :systemTheme="systemTheme" />
				<user-center :systemTheme="systemTheme" /> -->
				<li
					class="el-menu-item custom-menu-item"
					@click="handleChangeCollapsed('change-collapse')"
				>
					<div class="collapsed-box">
						<icon-font
							v-if="!collapsed"
							type="icon-menu-fold-fill"
						></icon-font>
						<icon-font
							v-else
							type="icon-menu-unfold-fill"
						></icon-font>
					</div>
					<span
						class="t-text"
						title="折叠侧边栏"
						>折叠侧边栏</span
					>
				</li>
			</ul>
		</div>
	</div>
</template>

<script lang="tsx" setup>
import { RouteRecordRaw, useRoute } from 'vue-router';
import { routerInstance } from '@/router';
import MenuItem from './menu-item.vue';
import AppCenter from './components/AppCenter.vue';
import UserCenter from './components/UserCenter.vue';
import SystemConfig from './components/SystemConfig.vue';
import MessageCenter from './components/MessageCenter.vue';
import { computed, onMounted, PropType, ref, watch, nextTick } from 'vue';
import { useStoreActiveRoutes } from '@/store/modules/active-routes';
import { setStoreCommon, storeCommon, storeMenus } from '@/store';
import { getSystemTheme, envConfig, isDev } from '@/config';
import { sendOpenNewWindowTagMsg } from '@/utils/message';
interface Props {
	collapsed?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
	collapsed: false,
});
const emit = defineEmits<{
	(ev: 'changeCollapsed'): void;
}>();
const storeActiveRoutes = useStoreActiveRoutes();
const currentRoute = useRoute(); // 当前路由
var systemTheme = ref(getSystemTheme());
const imgUrl = ref(new URL(`/src/assets/images/theme/${systemTheme.value}-logo.png`, import.meta.url).href);
const titleImgUrl = ref(new URL(`/src/assets/images/theme/${systemTheme.value}-title.png`, import.meta.url).href);
const selectMenu = ref<string>('');
watch(
	() => currentRoute,
	(val) => {
		if (currentRoute.meta && currentRoute.meta.activeMenu) {
			selectMenu.value = currentRoute.meta.activeMenu as string;
		} else {
			selectMenu.value = currentRoute.name as string;
		}
	},
	{
		deep: true,
		immediate: true,
	},
);
const appList: AppDataItem[] = [
	{
		name: '工业互联',
		children: [
			{
				icon: new URL(`/src/assets/images/app-center/app1.png`, import.meta.url).href,
				name: '设备远程运维',
				url: 'https://www.baidu.com/',
			},
			{
				icon: new URL(`/src/assets/images/app-center/app2.png`, import.meta.url).href,
				name: '设备远程运维',
			},
			{
				icon: new URL(`/src/assets/images/app-center/app3.png`, import.meta.url).href,
				name: '设备远程运维',
			},
		],
	},
	{
		name: '集团安全生产管理',
		children: [
			{
				icon: new URL(`/src/assets/images/app-center/app1.png`, import.meta.url).href,
				name: '设备远程运维',
			},
			{
				icon: new URL(`/src/assets/images/app-center/app2.png`, import.meta.url).href,
				name: '设备远程运维',
			},
			{
				icon: new URL(`/src/assets/images/app-center/app3.png`, import.meta.url).href,
				name: '设备远程运维',
			},
			{
				icon: new URL(`/src/assets/images/app-center/app4.png`, import.meta.url).href,
				name: '设备远程运维',
			},
			{
				icon: new URL(`/src/assets/images/app-center/app1.png`, import.meta.url).href,
				name: '设备远程运维',
			},
			{
				icon: new URL(`/src/assets/images/app-center/app2.png`, import.meta.url).href,
				name: '设备远程运维',
			},
			{
				icon: new URL(`/src/assets/images/app-center/app3.png`, import.meta.url).href,
				name: '设备远程运维',
			},
			{
				icon: new URL(`/src/assets/images/app-center/app4.png`, import.meta.url).href,
				name: '设备远程运维',
			},
		],
	},
];

const onSelect = (key: string, keyPath: string[]) => {
	const routers = routerInstance.getRoutes();
	const findRouteItem = routers.find((item) => item.name === key);

	if (currentRoute.name === key) {
		// 路由重复，调用刷新路由方法
		setStoreCommon({
			refresh: storeCommon.refresh + 1,
		});
		return;
	}
	if (findRouteItem?.meta.linkUrl) {
		//设置菜单选中之前的路由
		selectMenu.value = '';
		nextTick(() => {
			selectMenu.value = currentRoute.name as string;
		});
		if (isDev) {
			window.open(findRouteItem?.meta.linkUrl as string);
		} else {
			sendOpenNewWindowTagMsg(envConfig.VITE_APP_NAME, findRouteItem?.meta.linkUrl as string, {});
		}
	} else {
		//点击左侧菜单栏时先清除缓存页面后再跳转
		storeActiveRoutes.clearCacheRoutesItem(key);
		routerInstance.push({
			name: key,
		});
	}
};
const handleOpen = (key: string, keyPath: string[]) => {
	console.log(key, keyPath);
};
const handleClose = (key: string, keyPath: string[]) => {
	console.log(key, keyPath);
};

// 点击底部菜单
const handleChangeCollapsed = (key: string) => {
	emit('changeCollapsed');
};
</script>
<style lang="scss" scoped>
$menu-item-active-color: rgba(255, 255, 255, 0.2); // 移入、选中效果
::v-deep(.icon-font) {
	font-size: 18px;
}

::v-deep.primary-theme {
	background-image: url('@/assets/images/menu/blue-bc.png');
	.el-menu {
		background-color: transparent;
		.el-menu-item,
		.el-sub-menu__title {
			color: var(--el-color-white);
			border-left: 3px solid transparent;
			padding-left: 12px;
			height: 40px;
			line-height: 40px;
			.t-text {
				margin-left: 15px;
			}
		}

		.el-menu-item.is-active {
			background-color: $menu-item-active-color;
			border-left: 3px solid #ffffff;
		}
		.el-menu-item:hover {
			background-color: $menu-item-active-color;
		}
		.el-sub-menu__title.is-active {
			background-color: $menu-item-active-color;
			border-left: 3px solid #ffffff;
		}
		.el-sub-menu__title:hover {
			background-color: $menu-item-active-color;
		}

		.el-sub-menu {
			.el-menu--inline {
				.t-text {
					margin-left: 33px;
				}
			}
		}
	}
}
::v-deep.default-theme {
	background-color: #ffffff;
	.el-menu {
		background-color: transparent;
		.el-menu-item,
		.el-sub-menu__title {
			color: #1d2129;
			border-left: 3px solid transparent;
			padding-left: 12px;
			height: 40px;
			line-height: 40px;
			.t-text {
				margin-left: 15px;
			}
		}

		.el-menu-item.is-active {
			background-color: #f1f9ff;
			border-left: 3px solid var(--el-color-primary);
			color: var(--el-color-primary);
		}
		.el-menu-item:hover {
			background-color: #f2f3f5;
		}
		.el-sub-menu__title.is-active {
			background-color: #f1f9ff;
			border-left: 3px solid var(--el-color-primary);
		}
		.el-sub-menu__title:hover {
			background-color: #f2f3f5;
		}

		.el-sub-menu {
			.el-menu--inline {
				.t-text {
					margin-left: 33px;
				}
			}
		}
	}

	.title {
		color: #1d2129 !important;
	}
}

.menu-container {
	height: 100vh;
	background-size: cover;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	.menu-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		min-height: 0;
		.menu-header {
			height: 70px;
			display: flex;
			padding: 11px 15px;
			box-sizing: border-box;
			align-items: center;
			border-bottom: 1px solid rgba($color: #000000, $alpha: 0.15);

			.logo-img {
				width: 48px;
				height: 48px;
			}

			.logo-title {
				width: 102px;
				height: 48px;
				margin-left: 12px;
			}

			.title {
				margin-left: 12px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 16px;
				color: #ffffff;
				line-height: 20px;
				letter-spacing: 1px;
				text-align: left;
				font-style: normal;
			}
		}
	}

	.menu-main {
		border-right: none;
		flex: 1;
		overflow: auto;
	}
}

// 关闭侧边栏
.hide-sidebar {
	.menu-header {
		padding: 11px 5px !important;
	}

	.el-menu--collapse {
		width: 58px !important;
	}
}

.collapsed-img {
	width: 16px;
	height: 16px;
}

.t-text {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

::-webkit-scrollbar {
	width: 0px; /* 滚动条宽度 */
	background-color: #909399; /* 滚动条背景色 */
}

/* 自定义滚动条滑块样式 */
::-webkit-scrollbar-thumb {
	background-color: #909399; /* 滑块背景色 */
	border-radius: 0px; /* 滑块圆角 */
}

/* 鼠标悬停时滑块样式 */
::-webkit-scrollbar-thumb:hover {
	background-color: #757575;
}

.collapsed-box {
	display: flex;
	width: 24px;
	height: 24px;
	border-radius: 2px;
	line-height: normal;
	align-items: center;
	justify-content: center;
	&:hover {
		background: rgba($color: #ffffff, $alpha: 0.15);
	}
}

.custom-menu-item {
	padding-left: 8px !important;

	.t-text {
		margin-left: 12px !important;
	}
}
</style>
