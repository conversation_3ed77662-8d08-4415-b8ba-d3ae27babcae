<template>
	<el-form-item :rules="rules_" v-bind="$attrs" :disabled="disabled" ref="refDom">
		<el-input-number
			:step="0.01"
			v-model="value_"
			:min="min"
			:disabled="disabled"
			:max="max"
			:precision="precision"
			controls-position="right"
			:controls="false"
			placeholder="请输入"
		/>
	</el-form-item>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { rule_true_change } from '@/utils/validator';

interface Props {
	modelValue: number;
	rules?: [Object, Array<any>, Boolean];
	required?: boolean;
	disabled?: boolean;
	placeholder?: string;
	min?: number;
	max?: number;
	precision?: number;
	maxLength?: number;
}
const props = withDefaults(defineProps<Props>(), {
	placeholder: '请输入',
	min: 0,
	max: 9999999999999999.99, // decimal(18,2) 数字最大为 18位  包含小数点后的
	precision: 2,
});

const emit = defineEmits<{
	(ev: 'update:modelValue', value: number): void;
}>();
const refDom = ref();
const rules_ = computed<any>(() => {
	if ((props?.rules as unknown) === false) return undefined;
	if (!props.rules) {
		if (props.required) return rule_true_change;
		return undefined;
	}
	return props.rules;
});

const value_ = computed({
	get() {
		return props.modelValue;
	},
	set(value) {
		// if(String(value).length)
		// console.log(value);
		emit('update:modelValue', value);
	},
});
</script>
<style lang="scss" scoped></style>
