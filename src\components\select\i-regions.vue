<template>
	<el-form class="g-form-box el-form-item t-regions" ref="refForm" :disabled="disabled" :model="formState" label-position="top">
		<div class="t-item" v-for="(item, index) in formState.regions">
			<IRegion
				:label="index === 0 ? label : ''"
				v-model:province-code="item.provinceCode"
				v-model:province-name="item.provinceName"
				v-model:city-code="item.cityCode"
				v-model:city-name="item.cityName"
				v-model:region-code="item.regionCode"
				v-model:region-name="item.regionName"
				:provinceCodeProp="`regions.${index}.provinceCode`"
				:cityCodeProp="`regions.${index}.cityCode`"
				:regionCodeProp="`regions.${index}.regionCode`"
				@change="onChange"
			></IRegion>

			<div class="t-btns" v-if="!disabled">
				<el-button :icon="Plus" circle title="新增" v-if="index === formState.regions.length - 1" @click="addItem" />
				<el-button
					:icon="Delete"
					type="danger"
					v-if="formState.regions.length > 1"
					title="移除"
					plain
					circle
					@click="delItem(index)"
				/>
			</div>
		</div>
	</el-form>
</template>
<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import IRegion, { Props as PropsItem } from './i-region.vue';
import { Plus, Delete } from '@element-plus/icons-vue';
interface Props {
	provinceCodes: string; // 省
	provinceNames: string; // 省 名字
	cityCodes: string; // 市
	cityNames: string; // 市 名字
	regionCodes: string; // 区
	regionNames: string; // 区 名字
	label?: string;
	disabled?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
	label: '作业区域',
});
let placeholder = '-';
const emit = defineEmits<{
	(ev: 'update:provinceCodes', value: string): void;
	(ev: 'update:provinceNames', value: string): void;
	(ev: 'update:cityCodes', value: string): void;
	(ev: 'update:cityNames', value: string): void;
	(ev: 'update:regionCodes', value: string): void;
	(ev: 'update:regionNames', value: string): void;
}>();
const refForm = ref<ElFormInstance>();
const defaultForm: PropsItem = {
	provinceCode: '', // 省
	provinceName: '', // 省 名字
	cityCode: '',
	cityName: '',
	regionCode: '',
	regionName: '',
};
const formState = ref({
	regions: [] as PropsItem[],
});

const provinceCodes_ = computed({
	get() {
		return props.provinceCodes || '';
	},
	set(value) {
		emit('update:provinceCodes', value);
	},
});
const provinceNames_ = computed({
	get() {
		return props.provinceNames || '';
	},
	set(value) {
		emit('update:provinceNames', value);
	},
});
const cityCodes_ = computed({
	get() {
		return props.cityCodes || '';
	},
	set(value) {
		emit('update:cityCodes', value);
	},
});
const cityNames_ = computed({
	get() {
		return props.cityNames || '';
	},
	set(value) {
		emit('update:cityNames', value);
	},
});
const regionCodes_ = computed({
	get() {
		return props.regionCodes || '';
	},
	set(value) {
		emit('update:regionCodes', value);
	},
});
const regionNames_ = computed({
	get() {
		return props.regionNames || '';
	},
	set(value) {
		emit('update:regionNames', value);
	},
});

let timer1;
function onChange() {
	clearTimeout(timer1);
	timer1 = setTimeout(() => {
		console.log('1111111111 触发变化了');
		let codes1: string[] = [];
		let names1: string[] = [];
		let codes2: string[] = [];
		let names2: string[] = [];
		let codes3: string[] = [];
		let names3: string[] = [];
		formState.value.regions.forEach((item, index) => {
			codes1.push(item.provinceCode || placeholder);
			names1.push(item.provinceName || placeholder);
			codes2.push(item.cityCode || placeholder);
			names2.push(item.cityName || placeholder);
			codes3.push(item.regionCode || placeholder);
			names3.push(item.regionName || placeholder);
		});
		provinceCodes_.value = codes1.join(',');
		provinceNames_.value = names1.join(',');
		cityCodes_.value = codes2.join(',');
		cityNames_.value = names2.join(',');
		regionCodes_.value = codes3.join(',');
		regionNames_.value = names3.join(',');
	}, 10);
}
let timer2;
watch(
	[() => props.provinceCodes, () => props.cityCodes, () => props.regionCodes],
	([val1, val2, val3]) => {
		clearTimeout(timer2);
		timer2 = setTimeout(() => {
			console.log('111111111() => props.provinceCodes, () => props.cityCodes, () => props.regionCodes', val1, val2, val3);
			let codes1 = provinceCodes_.value.split(',');
			let names1 = provinceNames_.value.split(',');
			let codes2 = cityCodes_.value.split(',');
			let names2 = cityNames_.value.split(',');
			let codes3 = regionCodes_.value.split(',');
			let names3 = regionNames_.value.split(',');
			formState.value.regions = codes1.map((item, index) => {
				return {
					provinceCode: codes1[index] === placeholder ? '' : codes1[index], // 省
					provinceName: names1[index] === placeholder ? '' : names1[index], // 省 名字
					cityCode: codes2[index] === placeholder ? '' : codes2[index], // 市
					cityName: names2[index] === placeholder ? '' : names2[index], // 市 名字
					regionCode: codes3[index] === placeholder ? '' : codes3[index], // 区
					regionName: names3[index] === placeholder ? '' : names3[index], // 区 名字
				};
			});
		}, 10);
	},
	{
		immediate: true,
	},
);
function addItem() {
	formState.value.regions.push({ ...defaultForm });
	onChange();
}
function delItem(index) {
	formState.value.regions.splice(index, 1);
	// formState.value.regions.push({ ...defaultForm });
	onChange();
}

// 通过 handle 方法 调用 el-table上的方法，
function handle() {
	if (refForm.value) {
		const [type, ...other] = arguments;
		if (typeof refForm.value[type] === 'function') {
			return refForm.value[type](...other);
		} else {
			throw new TypeError(`el-table不存在 ${type} 方法`);
		}
	} else {
		throw new TypeError(`el-table不存在`);
	}
}

defineExpose({
	handle: handle as Function,
	validate() {
		return refForm.value
			?.validate(...arguments)
			.then((mas) => {
				if (!mas) return Promise.reject(mas);
				let arr: string[] = [];
				for (let i = 0; i < formState.value.regions.length; i++) {
					const item = formState.value.regions[i];
					const lastCode = item.regionCode || item.cityCode || item.provinceCode;
					if (!lastCode) return Promise.reject('请完善区域信息！');
					if (arr.includes(lastCode)) return Promise.reject('区域重复！');
					arr.push(lastCode);
				}
				return Promise.resolve(mas);
			})
			.catch((err) => {
				if (typeof err === 'string') {
					ElMessage.error(err);
				}
				return Promise.reject(err);
			});
	},
	clearValidate() {
		return refForm.value?.clearValidate(...arguments);
	},
	resetFields() {
		return refForm.value?.resetFields(...arguments);
	},
	fields() {
		return refForm.value?.fields;
	},
});
</script>
<style lang="scss" scoped>
.t-regions {
	width: 100%;
	display: flex;
	.t-item {
		width: 100%;
		display: flex;
		align-items: flex-end;
		.t-btns {
			flex-grow: 0;
			flex-shrink: 0;
			margin-bottom: 20px;
			margin-left: 12px;
			width: 77px;
		}
	}
}
</style>
