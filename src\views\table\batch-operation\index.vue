<template>
	<header-main>
		<template #main>
			<common-table
				v-bind="pageInfo"
				v-model:showCheckBox="showCheckBox"
				:loading="loading"
				:createButtonText="createButtonText"
				:pagination="true"
				:columns="tableColumns"
				:fill-screen="true"
				:batchOperationSwitch="batchOperationSwitch"
				:batchOperationOptions="batchOperationOptions"
				:data="tableData"
				rowKey="id"
				@pageChange="onPageChange"
				@onClickCreateBtn="onClickCreateBtn"
				@cell-click="handleCellClick"
				@onSubmitBatchSelectionList="onSubmitBatchSelectionList"
			>
			</common-table>

			<user-manage-dialog
				:initFormData="initFormData"
				v-model:visible="disalogVisible"
				:viewType="dialogViewType"
				@refreshList="refreshList"
			/>
		</template>
	</header-main>
</template>

<script lang="tsx" setup>
import { onMounted, ref, reactive } from 'vue';
import userManageDialog from './views/user-manage-dialog.vue';
import { cloneDeep } from 'lodash-es';
import { ElMessage } from 'element-plus';
import { type TableData } from './utils/types';
// import { getTableDataApi } from './utils/mock';
import { deleteTableDataApi, getTableDataApi } from './utils/api';
// #region 表格参数定义
// 表格loading
const loading = ref(false);

// 表格分页查询条件
const pageInfo = reactive<PageInfo>({
	total: 0,
	pageNumber: 1,
	pageSize: 20,
});

// 表格数据
const tableData = ref<TableData[]>([]);

// 表格列定义
const tableColumns: CommonTableColumn<TableData>[] = [
	{
		label: '用户名',
		prop: 'username',
		searchType: 'input',
		hoverHighLight: true,
	},
	{
		label: '手机号',
		prop: 'phone',
		searchType: 'input',
		hoverHighLight: true,
	},
	{
		label: '角色',
		prop: 'roles',
		searchType: 'select',
	},

	{
		label: '邮箱',
		prop: 'email',
		searchType: 'input',
	},
	{
		label: '创建时间',
		prop: 'createTime',
		searchType: 'sort',
	},
	{
		label: '操作',
		width: 120,
		formatter: (row, column, cellValue, index) => {
			const btns: OptionBtn[] = [
				{
					label: '编辑',
					onClick() {
						initFormData.value = cloneDeep(row);
						dialogViewType.value = 'edit';
						disalogVisible.value = true;
					},
				},
				{
					label: '删除',
					tips: '确定删除吗？',
					placement: 'left-start',
					onClick() {
						deleteTableDataApi(row.id).then(() => {
							ElMessage.success('删除成功');
							getList();
						});
					},
				},
			];

			return <i-btns btns={btns}></i-btns>;
		},
	},
];
// #endregion

// 初始化表格数据
onMounted(() => {
	getList();
});

// #region 列表交互逻辑
const getList = () => {
	loading.value = true;
	getTableDataApi({
		currentPage: pageInfo.pageNumber,
		size: pageInfo.pageSize,
	})
		.then((res) => {
			tableData.value = res.data.list;
			pageInfo.total = res.data.total;
		})
		.finally(() => {
			loading.value = false;
		});
};

const handleCellClick = (data) => {
	initFormData.value = cloneDeep(data);
	dialogViewType.value = 'view';
	disalogVisible.value = true;
};

// 分页器改变
const onPageChange = (pageNumber, pageSize) => {
	pageInfo.pageNumber = pageNumber;
	pageInfo.pageSize = pageSize;
	getList();
};

const refreshList = () => {
	pageInfo.pageNumber = 1;
	getList();
};
// #endregion

// #region 用户新增、修改逻辑
// 弹窗类型
const dialogViewType = ref<string>('');

// 弹窗显隐
const disalogVisible = ref<boolean>(false);

// 创建按钮文本
const createButtonText = ref('创建用户');

// 表单初始内容
const initFormData = ref<any>({});

const onClickCreateBtn = () => {
	dialogViewType.value = 'add';
	disalogVisible.value = true;
};
// #endregion

// #region 批量操作逻辑 

// DOIGN 如需保证分页切换时也能保留选中数据，注意传入rowKey字段，默认字段值为uuid

// 批量操作开关
const batchOperationSwitch = ref<boolean>(true);

// 批量操作下拉列表
const batchOperationOptions = ref<CommonTableOptions[]>([
	{
		label: '批量删除',
		value: 'batchDelete',
	},
	{
		label: '批量移动',
		value: 'batchMove',
	},
	{
		label: '批量复制',
		value: 'batchCopy',
	},
]);

// 控制表格展示勾选框
const showCheckBox = ref<boolean>(false);

// 获取提交的批量选择列表
const onSubmitBatchSelectionList = (type: string, list:any) => {
	console.log(type);
	console.log(list);
};
// #endregion
</script>
<style lang="scss" scoped></style>
