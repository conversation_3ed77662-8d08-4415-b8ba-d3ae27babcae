import 'vue-router';

declare module 'vue-router' {
	interface RouteMeta {
		title?: string; // 页面标题，
		titleLock?: boolean; // 锁定 页面标题，强制使用前台设置的title 。 默认使用后台传入的title
		icon?: string; // 菜单图标，白色主题下使用
		primaryIcon?: string; // 菜单图标，蓝色主题下使用
		authCode?: string[] | string; // 配置菜单的权限
		hide?: boolean; // 二级页面我们并不想在菜单中显示
		pass?: boolean; // 不需要登录就能进入 默认为 false
		isDev?: boolean; // 是否是开发中的路由菜单，仅在开发环境下使用，如果是开发中的路由，会直接出现在菜单中，不需要权限校验
	}
}
