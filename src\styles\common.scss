@import './transition.scss';
html,
body {
	margin: 0;
	border: 0;
	padding: 0;
	background: #f7f8fa;
	box-sizing: border-box;
	* {
		box-sizing: border-box;
	}
}

/*滚动条凹槽的颜色，还可以设置边框属性 */
*::-webkit-scrollbar-track-piece {
	background-color: #f6f6f6;
	-webkit-border-radius: 2em;
	-moz-border-radius: 2em;
	border-radius: 2em;
}
/*滚动条的宽度*/
*::-webkit-scrollbar {
	width: 9px;
	height: 9px;
}
/*滚动条的设置*/
*::-webkit-scrollbar-thumb {
	background-color: #e7e7e7;
	background-clip: padding-box;
	-webkit-border-radius: 2em;
	-moz-border-radius: 2em;
	border-radius: 2em;
}
/*滚动条鼠标移上去*/
*::-webkit-scrollbar-thumb:hover {
	background-color: #bbb;
}
*:focus-visible {
	outline: unset;
}
// @import './reset.less';
// 全局共用类
// i开头的表示某个组件中使用的类， 不是公共的
// g开头的表示公共全局类，
.g-icon-text {
	line-height: 1;
	display: inline-flex;
	align-items: center;
	> .el-icon {
		margin-right: 4px;
	}
}
// 单行，超出显示省略号
.g-ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
// 强制断行
.g-break-all {
	white-space: normal;
	word-wrap: break-word;
	word-break: break-all;
}
.g-click {
	color: var(--el-color-primary);
	cursor: pointer;
}

body {
	font-size: 16px;
	color: var(--el-text-color-primary);
}
// 一些宽度
.g-col-25 {
	width: 25% !important;
}
.g-col-33 {
	width: 33.33% !important;
}
.g-col-50 {
	width: 50% !important;
}
.g-col-66 {
	width: 66.66% !important;
}
.g-col-75 {
	width: 75% !important;
}
.g-col-100 {
	width: 100% !important;
}

.g-theme-text-color {
	color: var(--el-color-primary);
}

.w-full {
	width: 100%;
}

.h-full {
	height: 100%;
}

.add-cursor-pointer {
	cursor: pointer;
}

.table-total {
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: #86909c;
	line-height: 22px;
	margin-left: 24px;
}

.tip-text {
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: #86909c;
	line-height: 22px;
}

// 重置 element-plus的全局css变量
html {
	.el-table {
		--el-table-header-bg-color: #e5e6eb;
		.el-table__body {
			.el-table__cell {
				&.el-table__expanded-cell {
					padding-left: 10px;
					padding-right: 10px;
				}
				.cell {
					&:empty::after {
						content: '-';
					}
					overflow: visible;
					&.el-tooltip {
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}
			}
		}
		> .el-popper {
			max-width: 500px;
			white-space: normal;
			word-wrap: break-word;
			word-break: break-all;
		}
	}
	.el-form {
		.el-input-number,
		.el-date-editor {
			width: 100%;
		}
		.el-input__inner {
			text-align: left;
		}
	}
	.el-tabs {
		&.g-tabs {
			.el-tabs__header {
				margin-bottom: 0;
			}
		}
	}
	.el-button {
		&:focus-visible {
			outline: unset;
		}
	}
	.el-dialog__body {
		max-height: calc(100vh - 128px);
		overflow: auto;
	}
	.el-message-box__message {
		overflow: auto;
	}
	// 全局表单的样式
	.g-form-box {
		position: relative;
		display: flex;
		flex-wrap: wrap;
		align-content: flex-start;
		> .el-form-item {
			width: 100%;
			flex-grow: 1;
			padding: 0 20px;
			padding-bottom: 0;
			margin-bottom: 20px;
			box-sizing: border-box;
		}
	}
	&:root {
		--el-color-primary: #0075c2;
		--el-text-color-primary: #1d2129;
		--el-menu-hover-bg-color: #f2f3f5;
		--el-menu-item-height: 46px;
	}
}

.xirui-custom-collapse {
	.el-collapse-item {
		position: relative;

		.el-collapse-item__header {
			background: #f2f3f5;
			border: 1px solid #e5e6eb;
			padding-left: 45px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			color: #1d2129;
			line-height: 22px;

			.el-collapse-item__arrow {
				width: 24px;
				height: 24px;
				background: #ffffff;
				border-radius: 3px;
				border: 1px solid #e5e6eb;
				position: absolute;
				left: 8px;
			}
		}

		.el-collapse-item__wrap {
			border-bottom: 0 !important;

			.el-collapse-item__content {
				padding-top: 25px;
			}
		}
	}
}

.el-select {
	.el-select__wrapper {
		padding: 8px !important;
	}

	.el-select__placeholder {
		display: flex;
		align-items: center;
		font-family: MicrosoftYaHei;
		font-size: 14px;
		color: #1d2129;

		.icon-font {
			font-size: 16px;
			margin-right: 4px;
		}
	}
}

.text-center {
	text-align: center;
}

.hover-high-light-cell {
	.cell:hover {
		color: var(--el-color-primary);
	}
}

// 导入、导出按钮样式
.excel-upload-btn {
	background-color: rgba(0, 0, 0, 0) !important;
	&:hover {
		background-color: var(--el-color-primary) !important;
	}
}

// #region通用弹窗尺寸封装
.el-overlay-dialog {
	display: flex;
	align-items: center;
	justify-content: center;
}

// 通用样式
.xirui-custom-common-dialog {
	padding: 0 !important;
	margin: 0 !important;

	.el-dialog__header {
		padding-bottom: 0;
		padding: 0 20px;
		height: 48px;
		display: flex;
		align-items: center;
		border-bottom: 1px solid #e5e6eb;
		.el-dialog__title {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			color: #1d2129;
			line-height: 24px;
		}
	}

	.el-dialog__body {
		box-sizing: border-box;
		padding: 8px 20px;
	}

	.el-dialog__footer {
		height: 62px;
		padding: 0 20px;
		display: flex;
		align-items: center;
	}
}

.xirui-mini-dialog {
	width: 320px !important;

	.el-dialog__body {
		max-height: 210px;
		overflow: auto;
	}
}

.xirui-small-dialog {
	width: 560px !important;

	.el-dialog__body {
		max-height: 690px;
		overflow: auto;
	}
}

.xirui-medium-dialog {
	width: 720px !important;

	.el-dialog__body {
		max-height: 690px;
		overflow: auto;
	}
}

.xirui-large-dialog {
	width: 960px !important;

	.el-dialog__body {
		max-height: 690px;
		overflow: auto;
	}
}

// #endregion

// #region 文字规范
.xirui-h1 {
	font-family: PingFangSC, PingFang SC;
	font-weight: 500;
	font-size: 24px;
	color: #1f2329;
	line-height: 32px;
}

.xirui-h2 {
	font-family: PingFangSC, PingFang SC;
	font-weight: 500;
	font-size: 18px;
	color: #333333;
	line-height: 26px;
}

.xirui-h3 {
	font-family: PingFangSC, PingFang SC;
	font-weight: 500;
	font-size: 16px;
	color: #333333;
	line-height: 24px;
}

.xirui-text {
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: #333333;
	line-height: 22px;
}

.xirui-tip {
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	font-size: 12px;
	color: #333333;
	line-height: 20px;
}
// #endregion

// #region 消息弹出框
.xirui-operate-confirm {
	padding: 0 !important;
	width: 320px;
	.el-message-box__header {
		// 	display: none;
		padding: 0 20px;
		height: 48px;
		box-sizing: border-box;
		display: flex;
		align-items: center;
	}

	.el-message-box__content {
		// min-height: 50px;
		padding: 8px 20px;
		box-sizing: border-box;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #1d2129;
		line-height: 22px;
	}

	.el-message-box__btns {
		height: 48px;
		padding: 10px 20px;
		box-sizing: border-box;
		justify-content: flex-start;

		.el-button {
			height: 28px;

			// 调整取消按钮位置
			order: 2;
			margin-left: 12px;
		}

		.el-button--primary {
			order: 1;
			margin-left: 0;
		}
	}
}

// 删除确认弹窗，调整确认按钮样式
.xirui-delete-confirm {
	.el-message-box__btns {
		// 覆盖确定按钮样式
		.el-button--primary {
			--el-button-text-color: var(--el-color-white);
			--el-button-bg-color: var(--el-color-danger);
			--el-button-border-color: var(--el-color-danger);
			--el-button-outline-color: var(--el-color-danger-light-5);
			--el-button-active-color: var(--el-color-danger-dark-2);
			--el-button-hover-text-color: var(--el-color-white);
			--el-button-hover-link-text-color: var(--el-color-danger-light-5);
			--el-button-hover-bg-color: var(--el-color-danger-light-3);
			--el-button-hover-border-color: var(--el-color-danger-light-3);
			--el-button-active-bg-color: var(--el-color-danger-dark-2);
			--el-button-active-border-color: var(--el-color-danger-dark-2);
			--el-button-disabled-text-color: var(--el-color-white);
			--el-button-disabled-bg-color: var(--el-color-danger-light-5);
			--el-button-disabled-border-color: var(--el-color-danger-light-5);
		}
	}
}

// 通用提示样式
.xirui-common-tip {
	border-radius: 4px !important;
	padding: 10px 20px !important;
	box-sizing: border-box !important;

	.el-message__content {
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		line-height: 20px;
	}
}

// 成功提示
.xirui-success-tip {
	background: #ebf9f4 !important;
	border: 1px solid rgba(0, 174, 131, 0.27) !important;

	.el-message__icon {
		color: #00ae83 !important;
	}

	.el-message__content {
		color: #00ae83 !important;
	}
}

// 失败提示
.xirui-error-tip {
	background: #fef2f5 !important;
	border: 1px solid rgba(230, 66, 100, 0.25) !important;

	.el-message__icon {
		color: #e64264 !important;
	}

	.el-message__content {
		color: #e64264 !important;
	}
}
// #endregion

// #region tag标签
.xirui-primary-tag {
	background: #edf1ff !important;
	border-radius: 4px !important;
	border: 1px solid #648cfe !important;
	.el-tag__content {
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 12px;
		color: #648cfe;
		line-height: 20px;
	}
}

.xirui-success-tag {
	background: #ebf9f4 !important;
	border-radius: 4px !important;
	border: 1px solid #00ae83 !important;
	.el-tag__content {
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 12px;
		color: #00ae83;
		line-height: 20px;
	}
}

.xirui-info-tag {
	background: #f2f3f5 !important;
	border-radius: 4px !important;
	border: 1px solid #86909c !important;
	.el-tag__content {
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 12px;
		color: #86909c;
		line-height: 20px;
	}
}

.xirui-warning-tag {
	background: #fff6e4 !important;
	border-radius: 4px !important;
	border: 1px solid #ffb643 !important;
	.el-tag__content {
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 12px;
		color: #ffb643;
		line-height: 20px;
	}
}

.xirui-danger-tag {
	background: #fef2f5 !important;
	border-radius: 4px !important;
	border: 1px solid #e64264 !important;
	.el-tag__content {
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 12px;
		color: #e64264;
		line-height: 20px;
	}
}

.xirui-other-tag {
	background: #f0f2f7 !important;
	border-radius: 4px !important;
	border: 1px solid #1c4576 !important;
	.el-tag__content {
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 12px;
		color: #1c4576;
		line-height: 20px;
	}
}
// #endregion

// #region 覆盖表单样式
// 覆盖表单样式
.el-form {
	.el-form-item {
		.el-form-item__label {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 14px;
			color: #1d2129;
			max-width: 108px !important;
			display: flex;
			align-items: center;
			line-height: normal;
		}
		.el-form-item__label:before {
			content: '';
			width: 6px;
			margin-right: 4px;
		}
	}
}
// #endregion

// #region Descriptions 描述列表
.el-descriptions {
	.el-descriptions__cell {
		display: flex;

		.el-descriptions__label {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 14px;
			color: #1d2129;
			max-width: 108px !important;
			display: flex;
			align-items: center;
			line-height: normal;
			margin-right: 20px;
		}

		.el-descriptions__content {
			flex: 1;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 14px;
			color: #1d2129;
			line-height: 22px;
		}
	}
}
// #endregion

.custom-tree-box {
	.el-scrollbar__wrap {
		.el-scrollbar__view {
			height: 100%;
		}
	}
}

.xirui-flex-row {
	display: flex;
	flex-direction: row;
}
.xirui-flex-row-center {
	display: flex;
	flex-direction: row;
	align-items: center;
}
.xirui-flex-col {
	display: flex;
	flex-direction: column;
}
.xirui-flex-col-center {
	display: flex;
	flex-direction: column;
	align-items: center;
}
