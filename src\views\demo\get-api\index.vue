<template>
	<header-main
	>
		<template #main>
			<common-table
				v-bind="pageInfo"
				:loading="loading"
				:createButtonText="createButtonText"
				:pagination="true"
				:columns="tableColumns"
				:fill-screen="true"
				:data="tableData"
				@pageChange="onPageChange"
				@onClickCreateBtn="onClickCreateBtn"
				@cell-click="handleCellClick"
			>
			</common-table>

			<user-manage-dialog
				:initFormData="initFormData"
				v-model:visible="disalogVisible"
				:viewType="dialogViewType"
				@refreshList="refreshList"
			/>
		</template>
	</header-main>
</template>

<script lang="tsx" setup>
import { onMounted, ref, reactive } from 'vue';
import userManageDialog from './views/user-manage-dialog.vue';
import { cloneDeep } from 'lodash-es';
import { ElMessage } from 'element-plus';
import { type TableData } from './utils/types';
// import { getTableDataApi } from './utils/mock';
import { deleteTableData<PERSON>pi, getTableDataApi } from './utils/api';
// #region 表格参数定义
// 表格loading
const loading = ref(false);

// 表格分页查询条件
const pageInfo = reactive<PageInfo>({
	total: 0,
	pageNumber: 1,
	pageSize: 20,
});

// 表格数据
const tableData = ref<TableData[]>([]);

// 表格列定义
const tableColumns: CommonTableColumn<TableData>[] = [
	{
		label: '用户名',
		prop: 'username',
		searchType: 'input',
		hoverHighLight: true,
	},
	{
		label: '手机号',
		prop: 'phone',
		searchType: 'input',
		hoverHighLight: true,
	},
	{
		label: '角色',
		prop: 'roles',
		searchType: 'select',
	},

	{
		label: '邮箱',
		prop: 'email',
		searchType: 'input',
	},
	{
		label: '创建时间',
		prop: 'createTime',
		searchType: 'sort',
	},
	{
		label: '操作',
		width: 120,
		formatter: (row, column, cellValue, index) => {
			const btns: OptionBtn[] = [
				{
					label: '编辑',
					onClick() {
						initFormData.value = cloneDeep(row);
						dialogViewType.value = 'edit';
						disalogVisible.value = true;
					},
				},
				{
					label: '删除',
					tips: '确定删除吗？',
					placement: 'left-start',
					onClick() {
						deleteTableDataApi(row.id).then(() => {
							ElMessage.success('删除成功');
							getList();
						});
					},
				},
			];

			return <i-btns btns={btns}></i-btns>;
		},
	},
];
// #endregion

// 初始化表格数据
onMounted(() => {
	getList();
});

// #region 列表交互逻辑
const getList = () => {
	loading.value = true;
	getTableDataApi({
		currentPage: pageInfo.pageNumber,
		size: pageInfo.pageSize,
	})
		.then((res) => {
			tableData.value = res.data.list;
			pageInfo.total = res.data.total;
		})
		.finally(() => {
			loading.value = false;
		});
};

const handleCellClick = (data) => {
	initFormData.value = cloneDeep(data);
	dialogViewType.value = 'view';
	disalogVisible.value = true;
};

// 分页器改变
const onPageChange = (pageNumber, pageSize) => {
	pageInfo.pageNumber = pageNumber;
	pageInfo.pageSize = pageSize;
	getList();
};

const refreshList = () => {
	pageInfo.pageNumber = 1;
	getList();
};
// #endregion

// #region 用户新增、修改逻辑
// 弹窗类型
const dialogViewType = ref<string>('');

// 弹窗显隐
const disalogVisible = ref<boolean>(false);

// 创建按钮文本
const createButtonText = ref('创建用户');

// 表单初始内容
const initFormData = ref<any>({});

const onClickCreateBtn = () => {
	dialogViewType.value = 'add';
	disalogVisible.value = true;
};
// #endregion
</script>
<style lang="scss" scoped></style>
