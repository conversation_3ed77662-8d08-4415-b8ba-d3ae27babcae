<!--
  功能：图片上传组件
  作者：ligw
  时间：2024年08月08日 15:48:51
  版本：v1.0
  修改记录：
  修改内容：
  修改人员：
  修改时间：
-->
<template>
  <base-upload
    class="video-upload-container"
    v-model:file-list="fileList"
    list-type="picture-card"
    drag
    :limit="limit"
    :accept="accept"
    :on-progress="handleProgress"
    :before-upload="handleBeforeUpload"
    :on-preview="handlePictureCardPreview"
    :before-remove="handleBeforeRemove"
    :on-remove="handleRemove"
    :on-success="handleSuccess"
    v-bind="$attrs"
  >
  <template #file="{ file }">
    <div  class="video-container">
      <video ref="currentVideo" class="preview-video video-player" controls>
        <source :src="file.url" :type="file.fileType" />
      </video>
      <label class="el-upload-list__item-status-label"><i
          class="el-icon el-icon--upload-success el-icon--check"><svg xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 1024 1024">
            <path fill="currentColor"
              d="M406.656 706.944 195.84 496.256a32 32 0 1 0-45.248 45.248l256 256 512-512a32 32 0 0 0-45.248-45.248L406.592 706.944z">
            </path>
          </svg></i></label>
      <i class="el-icon el-icon--close"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
          <path fill="currentColor"
            d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z">
          </path>
        </svg></i>
      <span class="el-upload-list__item-actions">
        <span class="el-upload-list__item-preview" @click="playVideo(file)"><i class="el-icon el-icon--zoom-in">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
              <path fill="currentColor"
                d="m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704m-32-384v-96a32 32 0 0 1 64 0v96h96a32 32 0 0 1 0 64h-96v96a32 32 0 0 1-64 0v-96h-96a32 32 0 0 1 0-64z">
              </path>
            </svg></i>
        </span>
        <span class="el-upload-list__item-delete" @click="deleteMove(file)"><i class="el-icon el-icon--delete">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
              <path fill="currentColor"
                d="M160 256H96a32 32 0 0 1 0-64h256V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64h-64v672a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32zm448-64v-64H416v64zM224 896h576V256H224zm192-128a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32m192 0a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32">
              </path>
            </svg></i>
        </span>
      </span>
    </div>
  </template>
    <img src="./images/play.png" width="50px" height="53px" />
    <div class="video-upload-text"><el-icon><Plus /></el-icon>&nbsp;添加视频文件</div>
  </base-upload>
  <el-dialog v-model="dialogVisible">
    <video w-full :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
  <preview ref="previewRef" :files="model"/>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue'
import { MessageBox, Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BaseUpload from './base-upload.vue';
import preview from './preview.vue';

import type { UploadProps, UploadUserFile, UploadFile, UploadFiles } from 'element-plus'
import Moment from 'moment';
import { deleteFile } from '@/api/file'
import { envConfig } from '@/config'
import type {FileItem, FileResponse} from './image-upload.vue'

const model = defineModel()

interface Props {
  // files?: FileItem[]; //初始文件列表渲染
  accept?: string; // 图片类型，多个用英文,分隔
  limit?: number; //最大允许上传文件数量
  size?: number; //单文件最大限制，单位MB
}

const props = withDefaults(defineProps<Props>(), {
  files: () => [
  
],
  limit: 1,
  accept: '.mp4,.ogg,.webm'
})

const previewRef = ref<PreviewInstance>()
onMounted(() => {
  initData()
})
const initData = () => {
  const data = model.value 
  if (Array.isArray(data)) {
    fileList.value = data.map((item,index) => {
      const {fileName, fileAccessPath,uid} = item
      return {
        name: fileName,
        status: 'success',
        uid:  uid || Moment().valueOf(),
        url: envConfig.VITE_BASE_FILE_SATIC_PATH + fileAccessPath,
        response: {
          code: 0,
          data: [{ ...item }],
          message:''
        },
      }
    })
  } else {
    fileList.value = []
  } 
}

const fileList = ref<UploadFile[]>([])

const dialogImageUrl = ref('')
const dialogVisible = ref(false)


const playVideo = (file: any) => {
  const index = model.value?.findIndex(item => item?.uid === file.uid)
  previewRef?.value?.show(index)
}
// 删除视频
const deleteMove = async (file: any) => {
  
  const response = file.response as FileResponse
  if (response?.code === 0) {
    const result:any = await deleteFile(response?.data[0]?.fileUuid)
    if (result?.code === 0) {
      ElMessage.success('删除成功！')
      fileList.value = fileList.value.filter(item => item.uid !== file.uid)
      setModel()
    } else {
      ElMessage.error(result.message)
    }
  }
}

/**
 * 设置model数据
 */
const setModel = () => {
  model.value = fileList.value.map(item => {
    const response = item.response as FileResponse
    const result = response.data[0]
    return {
      ...result,
      uid: item.raw?.uid || Moment().valueOf(),
      fileType: item.raw?.type || `video/${result.fileType}`
    }
  })
  
}

/**
 * 上传成功更新model
 * @param response 
 * @param uploadFile 
 * @param uploadFiles 
 */
const handleSuccess: UploadProps['onSuccess'] = (response: FileResponse, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  if (response?.code === 0) {
  setModel()
  } else {
    ElMessage.error(response?.message || '上传失败！')
    fileList.value?.pop()
  }
}

// 上传进度
const handleProgress = (event: ProgressEvent, file: File) => {
  const percentage = ((event.loaded / event.total) * 100).toFixed(2);
}


/**
 * 上传前校验文件类型、大小
 * @param file 
 */
const handleBeforeUpload: UploadProps['beforeUpload'] = (file) => {
  const imgType = file.type.split('/')[1]
  if (!props.accept.toLocaleLowerCase().includes(imgType.toLocaleLowerCase())) {
    ElMessage.warning(`请上传后缀为${props.accept}类型图片！`)
    return false
  }
  if (props.size) {
    const limitSize = file.size / 1024 / 1024
    if (limitSize>props.size) {
      ElMessage.warning(`上传图片不能超过${props.size}MB!`)
      return false
    }
  }
  return true
}

/**
 * 删除文件
 * @param file 
 * @param uploadFiles 
 */
const handleRemove: UploadProps['onRemove'] = (file, uploadFiles) => {
  // fileList.value = fileList.value.filter(item=>item.uid !== file.uid)
  setModel()
}

/**
 * 删除文件前确认操作，确认后调用远程服务删除已上传文件
 * @param uploadFile 
 * @param uploadFiles 
 */
const  handleBeforeRemove: UploadProps['beforeRemove'] = (uploadFile: UploadFile, uploadFiles) => {
  if (uploadFile && uploadFile.status == 'success') { 
    return ElMessageBox.confirm('确定要删除吗？', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
    })
      .then(async () => {
        const response = uploadFile.response as FileResponse
        if (response?.code === 0) { 
          const result = (await deleteFile(response?.data[0]?.fileUuid)) as FileResponse
          if (result?.code === 0) {
            ElMessage.success('删除成功！')
          }
          return result?.code === 0
        } else {
          return true
        }
      })
      .catch(() => false);
  } else {
    return false
  }
}

const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
  
}
</script>
<style lang="scss" scoped>
  .video-upload-container{
    :deep(.el-upload-list__item-status-label),:deep(.el-icon--close-tip){
      display: none !important;
    }
    :deep(.el-upload-list__item),:deep(.el-upload--picture-card){
      width: 418px;
      height: 234px;
    }
    :deep(.el-upload-list__item-actions span+span){
      margin-left: 10px;
    }
    :deep(.el-upload-dragger){
      padding: 62px 10px;
    }
    .video-upload-text{
      width: 100%;
      height: 22px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0,0,0,0.4);
      line-height: 22px;
      text-align: center;
      font-style: normal;
      margin-top: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .preview-video{
      width: 100%;
      height: auto;
      object-fit: cover;
    }
  }
</style>

