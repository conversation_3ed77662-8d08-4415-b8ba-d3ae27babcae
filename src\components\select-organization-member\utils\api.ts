import { request } from "@/utils/service"
import type * as SelectTypes from "./types"

/** 获取标签列表 */
export function getLabelListApi(params: SelectTypes.LabelListRequest) {
  return request<SelectTypes.LabelListResponse>({
    url: "/v1/manager/label/list",
    method: "get",
    params,
    serviceName: 'VITE_SELECT_ORGANIZATION_MEMBER'
  })
}
export function filterOrganizationListApi(params: SelectTypes.FilterOrganizationListRequest) {
  return request<SelectTypes.FilterOrganizationListResponse>({
    url: "/v1/manager/organization/queryOrganizationByName",
    method: "get",
    params,
    serviceName: 'VITE_SELECT_ORGANIZATION_MEMBER'
  })
}
/** 根据标签uuid查询组织列表 */
export function getOrganizationListApi(params: SelectTypes.OrganizationListRequest) {
  return request<SelectTypes.OrganizationListResponse>({
    url: "/v1/manager/organization/getListByLabelUuid",
    method: "get",
    params,
    serviceName: 'VITE_SELECT_ORGANIZATION_MEMBER'
  })
}
/** 根据组织uuid查询用户列表 */
export function getUserListApi(params: SelectTypes.UserListRequest) {
  return request<SelectTypes.UserListResponse>({
    url: "/v1/manager/user/basic/list",
    method: "get",
    params,
    serviceName: 'VITE_SELECT_ORGANIZATION_MEMBER'
  })
}
