<template>
	<div class="t-div" ref="refDom">
		<img src="@/assets/images/403.png" alt="" />
		<div class="right">
			<div class="main-title">当前账号权限不足</div>
			<div class="sub-title">看起来您需要更高级别的权限才能进入这个页面。请联系管理员获取帮助。</div>
			<div class="btn-area">
				<!-- <ElButton type="primary" @click="back">返回上级</ElButton> -->
				<ElButton @click="goHome">前往主页</ElButton>
			</div>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { ElButton } from 'element-plus';
import { routerInstance } from '@/router';
import { sendChangeOtherAppPageMsg } from '@/utils/message';
const refDom = ref<HTMLDivElement>();
onMounted(() => {
	// 有时候页面会瞬间跳走，这里加个延迟，不让页面显示。
	setTimeout(() => {
		refDom.value!.style.opacity = '1';
	}, 500);
});
const back = () => {
	routerInstance.back()
}
const goHome = () => {
	// routerInstance.push('index')
  sendChangeOtherAppPageMsg('home')
}
</script>
<style lang="scss" scoped>
.t-div {
	background-color: #fff;
	min-height: var(--root-vh100);
	box-sizing: border-box;

	opacity: 0;
	transition: all 0.5s;
	display: flex;
	align-items: center;
	justify-content: center;
	>img{
		width: 320px;
		height: 320px;
		margin-right: 32px;
	}
	.right {
		.main-title{
			height: 46px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 32px;
			color: #26447E;
			line-height: 46px;
			text-align: left;
			font-style: normal;
			margin-bottom: 20px;
		}
		.sub-title{
			height: 22px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 14px;
			color: #1D2129;
			line-height: 22px;
			text-align: left;
			font-style: normal;
			margin-bottom: 72px;
		}
	}
}
</style>
