<template>
	<div class="n-scroll-box" ref="refDomListBox" @scroll="onScroll">
		<slot> </slot>
		<div v-show="loadedVal" class="i-t-loading-all">{{ loadedText }}</div>
	</div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref, watch } from 'vue';

interface Props {
	distance?: number; // 触发加载的距离阈值，单位为px
	refresh?: number; //  当值发生变化时，重置滚动条
	getList?: Function;
	loadedText?: string;
	loaded?: boolean; // 是否加载完全部数据
}
const props = withDefaults(defineProps<Props>(), {
	distance: 3,
	loadedText: '已经到底啦',
});
const emit = defineEmits<{
	(ev: 'load'): void;
}>();

const loadedVal = ref(false); // 是否加载完全部
const refDomListBox = ref<HTMLDivElement>();

// 创建一个观察器实例，并将回调函数传递给它
const observer = new MutationObserver(onScroll);

onMounted(() => {
	// 启动观察器
	observer.observe(refDomListBox.value!, { childList: true });
});
onUnmounted(() => {
	observer.disconnect();
});

watch(
	() => props.refresh,
	(val) => {
		if (val && refDomListBox.value) {
			refDomListBox.value!.scrollTop = 0;
		}
	},
);
watch(
	() => props.loaded,
	(val) => {
		loadedVal.value = val;
	},
);

let timer: any;
function onScroll() {
	// 页面滚动触发 滚动区域发生变化时也会触发
	clearTimeout(timer);
	timer = setTimeout(scrollGetList, 250);
}
function scrollGetList() {
	if (!refDomListBox.value) return;
	const h1 = refDomListBox.value.offsetHeight;
	const st = refDomListBox.value.scrollTop;
	const sh = refDomListBox.value.scrollHeight;
	if (sh < h1) return;
	if (sh - st - h1 > props.distance) return;
	// 没有滚动条 未加载满  或者 有滚动条，且滚动条滚动到页面底部
	if (props.getList) {
		loadedVal.value = props.getList();
	}
	emit('load');
}
// 在外部滚动内容区域
function setScroll(val: number) {
	if (!refDomListBox.value) return;
	// refDomListBox.value.scrollTo({
	// 	top: refDomListBox.value.scrollTop + val,
	// 	behavior: 'smooth',
	// });
	refDomListBox.value.scrollTop = refDomListBox.value.scrollTop + val;
}
defineExpose({
	setScroll: setScroll,
});
</script>

<style scoped>
.n-scroll-box {
	overflow: auto;
}
.i-t-loading-all {
	width: 100%;
	padding: 30px 0 20px;
	flex-shrink: 0;
	text-align: center;
	color: rgb(118, 118, 118);
}
</style>
