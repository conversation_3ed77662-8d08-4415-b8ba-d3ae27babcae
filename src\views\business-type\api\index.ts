import type * as BusinessType from './types';
import http, { resolveFunc, rejectFunc, EnumAim, EnumContentType } from '@/utils/axios';
const aim = EnumAim.idaas;

// 获取关联流程选项
export function getProcessOptionsApi() {
	return http.get('/open-api/workflow/v1/v2/pds', null, { useIDAAS: true, aim, ignoreGlobalAim: true, errorTips: false });
}

// 获取审批类别列表（实际接口）
export function getApprovalCategoryListApi(params) {
	return http.get('/open-api/approval-category/v1/list', params, { loading: true });
}

// 新增审批类别（实际接口）
export function addApprovalCategoryApi(CbBcategoryDto: any) {
	return http.post('/open-api/approval-category/v1/add', CbBcategoryDto, { loading: true, successTips: true, originData: true });
}

// 更新审批类别（实际接口）
export function updateApprovalCategoryApi(dto: any) {
	return http.post('/open-api/approval-category/v1/update', dto, { loading: true, successTips: true, originData: true });
}

// 删除审批类别（实际接口）
export function deleteApprovalCategoryApi(cNumber: any) {
	return http.post('/open-api/approval-category/v1/delete?cNumber=' + cNumber, null, { loading: true, successTips: true });
}

// 原有的模拟数据接口（保持兼容性）
export function getBusinessTypeDataListApi() {
	return new Promise((resolve) => {
		const list = [
			{
				id: '510000198810193120',
				username: '11',
				phone: '***********',
				email: '<EMAIL>',
				roles: 'editor',
				status: false,
				createTime: '1977-06-07 14:55:04',
			},
			{
				id: '620000202006013146',
				username: '2',
				phone: '***********',
				email: '<EMAIL>',
				roles: 'editor',
				status: false,
				createTime: '1989-10-20 12:35:23',
			},
		];
		resolve(list);
	});
}
