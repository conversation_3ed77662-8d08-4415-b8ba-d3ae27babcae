<template>
	<el-select class="collapse-subitem-select xirui-custom-select" v-model="value" placeholder="请选择">
		<template #label="{ label, value }">
			<IconFont type="icon-filter-line" />
			<span>{{ label }}</span>
		</template>
		<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
	</el-select>
</template>

<script lang="tsx" setup>
import { ref } from 'vue';
const value = ref<string>('all');
const options = [
	{
		value: 'all',
		label: '不分组',
	},
	{
		value: 'a1',
		label: '任务状态',
	},
	{
		value: 'a2',
		label: '创建人',
	}
];
</script>
<style lang="scss" scoped>
.collapse-subitem-select {
  width: 116px;
}
</style>
