/// <reference types="vite/client" />

declare module '*.vue' {
	import { DefineComponent } from 'vue';
	// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
	const component: DefineComponent<{}, {}, any>;
	export default component;
}

// src/types/env.d.ts
interface ImportMetaEnv {
	/**
	 * API基础路径(反向代理)
	 */
	VITE_BASE_API: string;
	/**
	 * idaas  API基础路径(反向代理)
	 */
	VITE_BASE_API2: string;
}

interface ImportMeta {
	readonly env: ImportMetaEnv;
}

declare module 'js-md5' {
	export default any;
}
