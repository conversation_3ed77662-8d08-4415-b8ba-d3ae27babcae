<template>
	<el-popover placement="right" trigger="click" popper-class="custom-app-center-popover">
		<template #reference>
			<li class="el-menu-item">
				<img :src="imgUrl" />
				<span class="t-text" title="应用中心">应用中心</span>
			</li>
		</template>
		<div class="app-list-content">
			<div
				class="app-cell-box"
				v-for="(item, index) in appCellList"
				:key="index"
				:class="{ 'app-type-cell': item.type == 'title' }"
			>
				<div v-if="item.type == 'title'" class="app-type-title">{{ item.name }}</div>
				<div v-else class="app-cell" @click="onOpenUrl(item.url)">
					<!-- <icon-font class="app-icon" type="icon-all" /> -->
					<img :src="item.icon" class="app-icon" />
					<div class="app-name">{{ item.name }}</div>
				</div>
			</div>
		</div>
	</el-popover>
</template>
<script lang="tsx" setup>
import { PropType, ref, watch } from 'vue';
let appCellList: AppDataCell[] = [];
const props = defineProps({
	systemTheme: {
		type: String,
	},
	appList: {
		type: Array as PropType<Array<AppDataItem>>,
	},
});

var imgUrl = ref('');

// DOING，受前端css布局限制，原本应用数据为类型+列表的层级结构，再此处统一转为列表平铺展示
watch(
	() => props.appList,
	(val) => {
		// 构造应用列表
		appCellList = [];
		val?.forEach((item) => {
			// 将应用分类名称添加到数组
			appCellList.push({
				name: item.name,
				type: 'title',
			});
			// 将应用添加到数组
			if (item.children) {
				item.children.forEach((element) => {
					appCellList.push(element);
				});
			}
		});
	},
	{
		immediate: true,
	},
);
// 设置菜单图标
watch(
	() => props.systemTheme,
	(val) => {
		imgUrl.value = new URL(`/src/assets/images/menu/${val}-app-center.png`, import.meta.url).href;
	},
	{
		immediate: true,
	},
);

function onOpenUrl(url) {
	if (url) {
		window.open(url, '_blank');
	}
}
</script>
<style lang="scss" scoped>
.app-list-content {
	max-width: 740px;
	max-height: 360px;
	overflow: auto;
	display: flex;
	flex-wrap: wrap;
	flex-direction: column;
	.app-cell-box {
		width: 160px;

		.app-type-title {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 14px;
			color: #86909c;
			line-height: 20px;
			border-bottom: 1px solid #e5e6eb;
			padding: 13px 0 13px 12px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.app-cell {
			display: flex;
			padding: 13px 0 13px 12px;
			align-items: center;
			.app-icon {
				// font-size: 16px;
				width: 16px;
				height: 16px;
			}
			.app-name {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #1d2129;
				line-height: 20px;
				margin-left: 12px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}

		.app-cell:hover {
			background: #f2f3f5;
			border-radius: 2px;
			cursor: pointer;
		}
	}

	.app-type-cell {
		margin-bottom: 10px;
	}

	.app-type-cell:not(:first-child) {
		margin-top: 10px;
	}
}
</style>
<style lang="scss">
.custom-app-center-popover {
	width: auto !important;
	padding: 7px 20px !important;
}
</style>
