<!--
  功能：导入结果页
  作者：ligw
  时间：2024年08月20日 11:20:43
  版本：v1.0
  修改记录：
  修改内容：
  修改人员：
  修改时间：
-->
<template>
  <ElDialog 
    v-model="visible" 
    :title="title"
    destroy-on-close
    class="import-result-dialog"
    :close-on-click-modal="false"
    width="560px"
  >
    <div class="import-result-content">
      <div class="image"></div>
      <div class="desc">导入完成！</div>
      <div class="desc result">共导入<span class="normal">{{result.totalCount}}</span>条，成功<span class="sucess">{{result.successCount}}</span>条，失败<span class="error">{{result.failedCount}}</span>条</div>
      <div class="desc reason">失败原因：
        <span @click="downloadTxt" class="normal">{{randomTitle()}}</span></div>
    </div>
    <div class="footer">
      <ElButton 
        @click="handleConfirm"
        type="primary"
      >确定</ElButton>
    </div>
  </ElDialog>
</template>
<script lang='ts' setup>
import { ref, computed, defineExpose } from 'vue'
import Moment from 'moment';
import { downloadByData } from '@/utils/file'
import { isArray } from '@/utils/is';
interface Props{
  data: ImportResultResponse;
  title?: string;
}
const props = withDefaults(defineProps<Props>(), {
  title: '导入结果'
})
const visible = ref<boolean>(false)
const result = computed(() => {
  const obj = props.data.data[0]
  return {
    ...obj,
    successCount: obj?.successCount || (obj?.totalCount - obj?.failedCount)
  }
})
const handleConfirm = () => {
  visible.value = false
}
const open = () => {
  visible.value = true
}
const downloadTxt = () => {
  const txt = result.value?.failedResultList
  downloadByData(isArray(txt) ?JSON.stringify(txt) : txt, randomTitle(), 'text/plain')
}
const randomTitle = () => {
  return `导入错误报告${Moment().format('YYYY-MM-DD')}.txt`
}
defineExpose({
  open
})
</script>
<style  lang='scss'>
.import-result-dialog{
  padding: 0;
  .el-dialog__header{
    padding: 12px 20px;
    border-bottom: 1px solid #E5E6EB;
  }
  .el-dialog__body{
    padding: 10px 20px 16px 20px;
  }
  .footer{
    width: 100%;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
.import-result-content{
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 23px;
  .image{
    width: 120px;
    height: 120px;
    background: url('./images/import.png') no-repeat 100% center;
    background-size: 100% 100%;
  }
  .desc{
    text-align: center;
    height: 22px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1D2129;
    line-height: 22px;
    font-style: normal;
    .normal{
      color: rgba(0, 117, 194, 1);
    }
    .sucess{
      color: rgba(0, 174, 131, 1);
    }
    .error{
      color: rgba(230, 66, 100, 1);
    }
  }
  .result{
    margin: 12px 0;
    
  }
  .reason{
    .normal:hover{
      text-decoration: underline;
      cursor: pointer;
    }
  }

}
</style>
