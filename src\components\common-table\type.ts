import CommonTable from './index.vue';
import { ElTableColumn } from 'element-plus';
export type TypeStatesColor = 'red' | 'blue' | 'yellow' | 'green' | 'gray' | string;
export interface StatesItem {
	value: string | number;
	label: string;
	color?: TypeStatesColor;
}

declare global {
	type CommonTableInstance = InstanceType<typeof CommonTable>;
	type ElTableColumnProps = InstanceType<typeof ElTableColumn>['$props'];
	interface CommonTableColumn<RecordType = Record<string, any>> extends Omit<ElTableColumnProps, 'prop' | 'formatter' | 'filters'> {
		key?: string; // 唯一 key
		key_?: string; // 组件自动生成的key 不是参数，不用传入
		slotName?: string; // 自定义内容插槽名
		slotHeaderName?: string; // 自定义header插槽名
		prop?: keyof RecordType; //  如果没有指定 columnKey 则使用prop作为 columnKey
		hide?: boolean | (() => boolean); // 是否隐藏
		hoverHighLight?: boolean;  // 移入高亮
		formatter?: (row: RecordType, column: CommonTableColumn, cellValue: any, index: number) => any;
		
		// searchable?: boolean | 'custom'; // 是否支持搜索，  custom 时，前端将不再进行排序，使用后台排序
	
		states?: StatesItem[]; // 状态列表 可以快捷显示状态 如果存在此项时，会自动展示成状态列

		// dateFormat 是否是日期格式， true是 组件将转换成默认日期格式（YYYY-MM-DD），string 是转换成用户传入的格式
		dateFormat?: boolean | 'month' | 'date' | 'time' | 'all' | string;

		// ============ 排序逻辑参数 start ============
		openSort?: boolean;  // 是否开启排序
		defaultSortChecked?: boolean;  // 是否默认选中。搭配高级排序配合使用
		defaultSortRule?: string;  // 默认排序规则
		// ============ 排序逻辑参数 end ============


		// ============ 搜索逻辑参数 start ============
		searchType?: string; // 搜索状态字段 【input 输入框】【select 下拉框】【date 日期框】
		selectType?: 'checkbox' | 'radio'; // 选择类型 radio单选，checkbox多选
		searchRule?: boolean | string; // 搜索匹配规则 【true 支持手动选择】【false 不可选择，默认值】【enum 使用传入的枚举值，不可变更】
		filters?: {   // 下拉框选项列表
			label: string;
			value: string | number;
		}[];
		// ============ 搜索逻辑参数 start ============
	}
	interface CommonTableParams extends CommonPageListParam {
		// search?: Record<string, any>;
		// filter?: Record<string, any>;
		// sort?: Record<string, any>;
		[key: string]: any;
	}

	// 表格-操作选项列
	interface CommonTableOptions {
		label?: string;
		value?: string;
	}
}
