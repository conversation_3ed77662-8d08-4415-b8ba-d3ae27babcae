<template>
	<div>
		<el-select
			class="sort-select"
			popper-class="hide-option"
			ref="buttonRef"
			v-click-outside="onClickOutside"
			v-model="value"
			placeholder="请选择"
		>
			<template #label="{ label, value }">
				<IconFont type="icon-filter-line" />
				<span>{{ formatSelectValue }}</span>
			</template>
		</el-select>

		<el-popover
			@hide="showOrHidePopover"
			@show="showOrHidePopover"
			width="375"
			ref="popoverRef"
			:virtual-ref="buttonRef"
			trigger="click"
			virtual-triggering
		>
			<el-form ref="formRef" :model="dynamicValidateForm" label-width="auto" class="demo-dynamic">
				<VueDraggable :animation="150" v-model="dynamicValidateForm.domains" @end="onDragEnd">
					<el-form-item
						class="custom-form-item"
						v-for="(domain, index) in dynamicValidateForm.domains"
						:key="'item' + domain.time + index"
					>
						<IconFont @click.prevent="removeDomain(domain)" class="delete-icon" type="icon-indeterminate-circle-line" />
						<el-select
							:teleported="false"
							class="label-select"
							v-model="domain.label"
							placeholder="请选择"
							@change="onLabelChange($event, index)"
						>
							<el-option
								v-for="item in options"
								v-show="judgeShowOption(item)"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
						<el-select
							@change="onValueChange($event, index)"
							:teleported="false"
							class="value-select"
							v-model="domain.value"
							placeholder="请选择"
						>
							<el-option v-for="item in sortOptions" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
						<IconFont class="operation-icon" type="icon-more-2-fill" />
						<!-- <el-input v-model="domain.value" /> -->
						<!-- <el-button class="mt-2" > Delete </el-button> -->
					</el-form-item>
				</VueDraggable>
				<el-form-item class="custom-add-form-item" v-if="options.length !== dynamicValidateForm.domains.length">
					<IconFont @click="addDomain" class="add-icon" type="icon-indeterminate-circle-line" />
					<span @click="addDomain" class="add-text">添加条件</span>
					<!-- <el-button text>New domain</el-button> -->
				</el-form-item>
			</el-form>
		</el-popover>
	</div>
</template>

<script lang="tsx" setup>
import { ref, reactive, unref, watch, computed, onMounted } from 'vue';
import { ClickOutside as vClickOutside } from 'element-plus';
import type { FormInstance } from 'element-plus';
import Moment from 'moment';
import { type UseDraggableReturn, VueDraggable } from 'vue-draggable-plus';
interface Props {
	sortGroupList: any; // 排序条件组
	columns: any; // 表格列
}

const props = withDefaults(defineProps<Props>(), {
	sortGroupList: {},
	columns: [],
});

const emit = defineEmits<{
	(ev: 'update:sortGroupList', value: any): void;
}>();

const value = ref('请选择排序');
// 声明元素，点击下拉框打开popover控件
const buttonRef = ref();
const popoverRef = ref();
const onClickOutside = () => {
	unref(popoverRef).popperRef?.delayHide?.();
};

// =============== 选项表单相关参数 ===============
const formRef = ref<FormInstance>();
const dynamicValidateForm = reactive<{
	domains: DomainItem[];
}>({
	domains: [],
});

let checkedList = ref<any[]>([]); // 已勾选列表

const options = ref<any[]>([]);

const sortOptions = [
	{
		value: 'desc',
		label: '倒序',
	},
	{
		value: 'asc',
		label: '正序',
	},
];

interface DomainItem {
	time: string;
	label: string;
	value: string;
}

// watch(
// 	() => props.sortGroupList,
// 	(val) => {},
// 	{
// 		immediate: true,
// 	},
// );

watch(
	() => props.columns,
	(val) => {
		let arr = [] as any;
		val.forEach((item) => {
			if (item.openSort) {
				arr.push({
					label: item.label,
					value: item.prop,
				});
			}
		});
		options.value = arr;
	},
	{
		immediate: true,
	},
);

const formatSelectValue = computed(() => {
	if (dynamicValidateForm.domains.length == 0) {
		return '请选择';
	} else if (dynamicValidateForm.domains.length == 1) {
		let text = '';
		options.value.forEach((item) => {
			if (item.value == dynamicValidateForm.domains[0].label) {
				text = item.label;
			}
		});
		return text;
	} else {
		return dynamicValidateForm.domains.length + '个筛选条件';
	}
});

onMounted(() => {
	initFormData();
	console.log('onMounted');
});

// 弹出窗展示、隐藏，为了避免再次打开时，效果不佳。在关闭时再次触发表单数据赋值
function showOrHidePopover() {
	initFormData();
}

// 初始化表单数据
function initFormData() {
	// 1. 获取有排序值的内容，从对象结构转为数组
	let arr = [] as any;
	let valueArr = [] as any;
	for (let key of Object.keys(props.sortGroupList)) {
		if (props.sortGroupList[key].value) {
			arr.push(props.sortGroupList[key]);
			valueArr.push(props.sortGroupList[key].label);
		}
	}

	// 2. 根据操作时间排序
	arr.sort((a, b) => {
		return (new Date(a.time) as any) - (new Date(b.time) as any);
	});

	// 3. 赋值给表单
	dynamicValidateForm.domains = arr;

	console.log('===初始化表单数据===');
	console.log(arr);

	initCheckedList();
}

const removeDomain = (item: DomainItem) => {
	// 删除表单中的某一项
	const index = dynamicValidateForm.domains.indexOf(item);
	if (index !== -1) {
		dynamicValidateForm.domains.splice(index, 1);
	}

	// 已勾选列表中移除
	// if (item.label) {
	// 	const indexToRemove = checkedList.value.indexOf(item.label);
	// 	checkedList.value.splice(indexToRemove, 1);
	// }
	initCheckedList();
};

const addDomain = () => {
	dynamicValidateForm.domains.push({
		time: Moment().format('YYYY-MM-DD HH:mm:ss'),
		label: '',
		value: '',
	});
};

// 判断是否显示下拉选项
function judgeShowOption(item) {
	if (checkedList.value.includes(item.value)) {
		return false;
	}
	return true;
}

// 构建已选择列名集合
function initCheckedList() {
	checkedList.value = dynamicValidateForm.domains.map((item) => {
		return item.label;
	});
}

// 表格列名选择器，下拉选择
function onLabelChange(item, index) {
	// checkedList.value.push(item);
	initCheckedList();
	console.log('onLabelChange', item);
	judgeItemValid(index);
}

// 排序条件选择器，下拉选择
function onValueChange(item, index) {
	console.log('onValueChange');
	console.log(item);
	judgeItemValid(index);
}

// 判断选择的这项数据是否有效（列名、排序规则都选中就是有效）
function judgeItemValid(index) {
	// 列名、排序规则都选中，触发数据提交
	if (dynamicValidateForm.domains[index].label && dynamicValidateForm.domains[index].value) {
		dynamicValidateForm.domains[index].time = Moment().format('YYYY-MM-DD HH:mm:ss');
		handleSubmitFormData();
	}
}

// 提交当前表单数据
function handleSubmitFormData() {
	// 1. 抽离当前表单中，列名、排序规则都选择的项
	let arr = dynamicValidateForm.domains.filter((item) => {
		return item.label && item.value && item.time;
	});

	// 2. 根据列名下拉框选项，构造一个空白的排序查询集合
	let obj = {};
	options.value.forEach((item) => {
		obj[item.value] = {
			label: item.value,
			time: '',
			value: '',
		};
	});

	// 3. 依据表单有效数据为准，将勾选规则的项进行赋值
	arr.forEach((item) => {
		if (obj[item.label]) {
			obj[item.label] = item;
		}
	});

	// 4. 更新
	emit('update:sortGroupList', obj);
}

function onDragEnd(event) {
	// 拖拽结束后的处理逻辑
	console.log('Drag end', event);
	console.log(dynamicValidateForm);
}
</script>
<style lang="scss" scoped>
.sort-select {
	width: 144px;
}

.custom-form-item {
	margin-bottom: 8px !important;

	.delete-icon {
		font-size: 16px;
		margin-right: 8px;
		cursor: pointer;
	}

	.label-select {
		width: 144px;
		margin-right: 10px;
	}

	.value-select {
		width: 144px;
		margin-right: 8px;
	}

	.operation-icon {
		font-size: 18px;
		cursor: pointer;
	}
}

.custom-add-form-item {
	margin-bottom: 0 !important;
	.add-icon {
		font-size: 16px;
		margin-right: 8px;
		cursor: pointer;
	}

	.add-text {
		cursor: pointer;
		font-family: MicrosoftYaHei;
		font-size: 14px;
		color: var(--el-color-primary);
		line-height: 22px;
	}
}
</style>
<style lang="scss">
.hide-option {
	display: none;
}
</style>
