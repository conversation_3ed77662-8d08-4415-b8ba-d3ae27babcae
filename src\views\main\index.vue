<template>
	<el-container class="t-layout-main t-layout">
		<el-aside
			v-model:collapsed="collapsed"
			:trigger="null"
			collapsible
			class="layout-sider"
			:class="{ 'hide-sidebar': collapsed }"
		>
			<ISideMenu
				:collapsed="collapsed"
				@changeCollapsed="collapsed = !collapsed"
			/>
		</el-aside>
		<el-main class="t-layout-content">
			<NavBar></NavBar>
			<div class="t-big-content">
				<div
					ref="refContentBox"
					class="t-big-content-box"
				>
					<router-view v-slot="{ Component, route }">
						<transition
							name="fade-transform"
							mode="out-in"
						>
							<keep-alive :include="cacheRouteNames">
								<component
									:is="Component"
									:key="route.name"
								/>
							</keep-alive>
						</transition>
					</router-view>
				</div>
			</div>
		</el-main>
	</el-container>
</template>

<script lang="ts" setup name="main">
import { envConfig } from '@/config';
import { watch, computed, nextTick, ref, onMounted } from 'vue';
import ISideMenu from './menu/index.vue';
import NavBar from './menu/nav-bar.vue';
import { storeCommon } from '@/store';
import { useRoute, useRouter } from 'vue-router';
import { useStoreActiveRoutes } from '@/store/modules/active-routes';
import { initEventListener } from '@/utils/message';

let cacheRouteNames = ref<string[]>([]); //被缓存的页面
const storeActiveRoutes = useStoreActiveRoutes();
const collapsed = ref(false); // 左侧导航栏闭合状态
const currentRoute = useRoute(); // 当前路由
interface Props {}
const props = withDefaults(defineProps<Props>(), {});
const router = useRouter();

onMounted(() => {
	window.addEventListener('message', (event) => {
		// 隔离无效、无意义消息
		let response = event.data;
		if (response && response.appCode === envConfig.VITE_APP_NAME) {
			switch (response.msgCode) {
				// 打开指定页面
				case 'OTHER_SPECIFY_PAGE':
					openSpecifyPage(response.msgData);
					break;
			}
		}
	});
});

const key = computed(() => {
	return currentRoute.fullPath + storeCommon.refresh + '';
});
const refContentBox = ref<HTMLDivElement>();
const isContainer = ref(false);
watch(
	() => storeActiveRoutes.getCacheRouteNames,
	(val) => {
		cacheRouteNames.value = JSON.parse(JSON.stringify(val));
	},
	{
		immediate: true,
		deep: true,
	},
);
watch(
	key,
	(val) => {
		nextTick(() => {
			if (!refContentBox.value) return;
			// 如果子路由中没有调用 container-lcr 组件 添加一个类控制样式 这么做是为了减少dom层级
			isContainer.value = !refContentBox.value.getElementsByClassName('i-container-lcr-box').length;
		});
	},
	{
		immediate: true,
	},
);

// 打开特定页面
const openSpecifyPage = (data) => {
	router.push({ path: data.routerUrl, query: data.routerParams });
};
</script>

<style lang="scss" scoped>
::v-deep.hide-sidebar {
	.el-menu-item,
	.el-sub-menu__title {
		padding-left: 16px !important;
	}

	.custom-menu-item {
		padding-left: 12px !important;
		padding-right: 0;
	}
}

.t-layout {
	overflow: hidden;
	height: 100vh;
	.t-layout-main {
		flex-grow: 1;
		flex-shrink: 1;
		overflow: hidden;
	}
	.layout-sider {
		overflow: hidden;
		width: 208px;
		transition: width 0.3s ease;
	}

	.hide-sidebar {
		width: 58px;
	}

	.t-layout-content {
		border-radius: 2px;
		display: flex;
		flex-direction: column;
		padding: 0;
		background-color: #e3eefb;
		.t-big-content {
			flex-grow: 1;
			// background-color: #fff;
			overflow: auto;
			padding: 0 8px 8px 8px;
		}
		.t-big-content-box {
			min-width: 1180px;
			width: 100%;
			height: 100%;
			box-sizing: border-box;
			overflow: hidden;
			position: relative;
			background: #ffffff;
			border-radius: 0px 4px 4px 4px;
			border: 1px solid #e5e6eb;
			border-top: 0;
		}
	}
}
</style>
