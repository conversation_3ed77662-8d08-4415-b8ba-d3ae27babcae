import { passWordMaxLength, passWordMinLength } from '@/config';
import { FormItemRule } from 'element-plus';
import { isArray, isObject, isString } from './is';

/**
 * 判断一个变量是否为真 空数组 空对象返回为false , 0 返回为true
 * @param val
 */
export function has<T = any>(val: unknown): val is T {
	if (val === null || val === undefined || (typeof val === 'number' && isNaN(val))) {
		return false;
	} else if (isArray(val)) {
		return val.length !== 0;
	} else if (isObject(val)) {
		return Object.keys(val).length !== 0;
	} else if (isString(val)) {
		return !!(val as string).trim();
	} else {
		return true;
	}
}
// 各种表单校验规则  有数组和对象两种格式 注意区分， rule_ 开头的是可以直接做规则。

// 不能为空的规则
export const rule_true: FormItemRule = {
	required: true,
	validator(rule: any, value: any, callback: Function) {
		console.log('1111111rule_true', value, has(value));
		if (has(value)) {
			callback();
		} else {
			callback(new Error('不能为空'));
		}
	},
	trigger: 'blur',
	message: '不能为空',
	type: 'any',
};
export function getRuleTrue(label: string, trigger = ['blur', 'change']) {
	return {
		required: true,
		validator(rule: any, value: any, callback: Function) {
			console.log('1111111rule_true', value, has(value));
			if (has(value)) {
				callback();
			} else {
				callback(new Error(label));
			}
		},
		trigger: trigger,
		message: label,
	};
}

// 需要勾选的规则
export const rule_true_check: FormItemRule = {
	required: true,
	validator(rule: any, value: any, callback: Function) {
		console.log('rule_true_check', value, value === true);
		if (value === true) {
			callback();
		} else {
			callback(new Error('请勾选'));
		}
	},
	trigger: 'change',
	message: '请勾选',
	type: 'any',
};

// 触发时机为 change
export const rule_true_change = {
	...rule_true,
	trigger: 'change',
};
// 触发时机为 change + blur
export const rule_true_all = {
	...rule_true,
	trigger: ['change', 'blur'],
};

// 不能为空的规则 数字，且不能为0
export const rule_number: FormItemRule = {
	required: true,
	validator(rule: any, value: number | null | undefined, callback: Function) {
		console.log('rule_number', value);
		if (value === 0 || Number.isNaN(value) || !has(value)) {
			callback(new Error('不能为空'));
		} else {
			callback();
		}
	},
	trigger: 'blur',
	type: 'any',
};

// 办公电话，
export const rule_tel: FormItemRule = {
	trigger: 'blur',
	type: 'string',
	validator(rule: any, value: string, callback: Function) {
		console.log(value);
		const reg = /^[0-9,-]*$/;
		if (!value || reg.test(value)) {
			callback();
		} else {
			callback(new Error('格式不正确'));
		}
	},
};
export function validatorLink(value: string) {
	if (!value) return false;
	const reg = /(http:\/\/|https:\/\/|www.)((\w|\:|\%|\#|=|\?|\.|\/|&|-)+)/g;
	return reg.test(value);
}
//  网址连接
export const rule_link: FormItemRule = {
	trigger: 'blur',
	type: 'string',
	validator(rule: any, value: string, callback: Function) {
		console.log('rule_link', value);
		// const reg = new RegExp(regLinkStr);
		const reg = /(http:\/\/|https:\/\/|www.)((\w|\:|\%|\#|=|\?|\.|\/|&|-)+)/g;
		if (!value || reg.test(value)) {
			callback();
		} else {
			callback(new Error('格式不正确'));
		}
	},
};

/**
 * 校验密码 注意，参数与其它参数不一致，
 * @param password 密码的值
 * @param mark 是否激活最小长度校验，如果用户第一次输入密码时，先不校验最小长度，只有用户输入密码超过最小长度后再删除时，才进行最小长度校验
 * @returns 校验错误信息
 */
export const validatePW = (password: string, mark: boolean = false): Promise<string> => {
	return new Promise((resolve, reject) => {
		console.log('密码：', password, mark);
		if (!has<string>(password)) {
			reject('不能为空');
		} else if (password.length > passWordMaxLength) {
			reject(`长度不能超过 ${passWordMaxLength}`);
		} else if (password.length < passWordMinLength && mark) {
			reject(`长度不能小于 ${passWordMinLength}`);
		} else {
			resolve('密码校验通过');
		}
	});
};

// 数组不能为空
export const rule_array = {
	validator: (rule: any, value: string | any[], callback: Function) => {
		console.log('数组不能为空:', value);
		if (isArray(value) && value.length) {
			callback();
		} else {
			callback(new Error('不能为空'));
		}
	},
	required: true,
	trigger: 'change',
	type: Array,
};

// 对象不能为空
export const rule_obj = {
	validator: (rule: any, value: any, callback: Function) => {
		const value_ = value;
		if (isObject(value_) && Object.values(value_).length) {
			callback();
		} else {
			callback(new Error('不能为空'));
		}
	},
	required: true,
	trigger: 'change',
	type: Object,
};
// 日期不能为空
export const rule_date = {
	validator: (rule: any, value: any, callback: Function) => {
		const value_ = value;
		if (value_ !== undefined && value_ !== null && value_ !== '') {
			callback();
		} else {
			callback(new Error('不能为空'));
		}
	},
	required: true,
	trigger: 'change',
	type: 'any',
};

// 邮箱
export const rule_email = { trigger: 'blur', type: 'email', message: '格式错误' };
export const rule_email_true = [rule_true, rule_email];
export const rule_phone = {
	trigger: 'blur',
	validator: (rule: any, value: string, callback: Function) => {
		if (!value) {
			callback();
		} else {
			const reg = /^1[1|2|3|4|5|6|7|8|9][0-9]{9}$/;
			if (reg.test(value)) {
				callback();
			} else {
				callback(new Error('手机号格式不正确！'));
			}
		}
	},
};

export const rule_phone_true = [rule_true, rule_phone];

export const rule_bankCard = {
	trigger: 'blur',
	validator: (rule: any, value: string, callback: Function) => {
		if (!value) {
			callback();
		} else {
			const reg = /^\d{12,19}$/;
			if (reg.test(value)) {
				callback();
			} else {
				callback(new Error('格式不正确'));
			}
		}
	},
};
export const rule_bankCard_true = [rule_true, rule_bankCard];
export const rule_credit_fn = (rule: any, value: string, callback: Function) => {
	const reg = /^([0-9ABCDEFGHJKLMNPQRTUWXY]{2})(\d{6})([0-9ABCDEFGHJKLMNPQRTUWXY]{9})([0-9ABCDEFGHJKLMNPQRTUWXY])$/;
	if (!value || reg.test(value)) {
		callback();
	} else {
		callback(new Error('格式不正确'));
	}
};
// 统一信用代码
export const rule_credit = {
	trigger: 'blur',
	validator: rule_credit_fn,
};
// 统一信用代码
export const rule_credit_true = [rule_true, rule_credit];

export function validatorIdCard(rule: any, value: string, callback: Function) {
	console.log('validatorIdCard', value);
	const reg =
		/(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)/;

	// const reg2 =
	// 	/(^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/;

	if (!value || reg.test(value)) {
		callback();
	} else {
		callback(new Error('格式不正确'));
	}
}
// 身份证
export const rule_idCard = {
	trigger: ['blur'],
	validator: validatorIdCard,
};
// 身份证
export const rule_idCard_true = [
	{
		...rule_true,
		trigger: ['change', 'blur'],
	},
	rule_idCard,
];
