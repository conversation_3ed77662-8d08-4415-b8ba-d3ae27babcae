<template>
	<div class="container">
		<div class="container-box">
			<el-row>
				<el-col :span="24">
					<el-card class="card-view">
						<template #header>
							<div class="card-header">
								<span>columns方式</span>
							</div>
						</template>

						<common-table
							:columns="allTableColumns"
							:fill-screen="false"
							:data="data"
						></common-table>
					</el-card>
				</el-col>
				<el-col :span="24">
					<el-card class="card-view">
						<template #header>
							<div class="card-header">
								<span>columns方式+插槽方式</span>
							</div>
						</template>
						<common-table
							:columns="slotTableColumns"
							:data="data"
							:fill-screen="false"
						>
							<template #slotUsername="{ column, $index }">
								<span style="color: red">{{ column.label }} </span>
							</template>
							<template #slotPhone="{ row, column, $index }">
								<span style="color: red"> {{ row.phone }}</span>
							</template>
							<template #action="scope">
								<el-button
									link
									type="primary"
									@click.stop.prevent="handleCommand('upload', scope.row)"
									>上传图纸</el-button
								>
								<el-button
									link
									type="primary"
									@click.stop.prevent="handleCommand('view', scope.row)"
									>查看图纸</el-button
								>
							</template>
						</common-table>
					</el-card>
				</el-col>
				<el-col :span="24">
					<el-card class="card-view">
						<template #header>
							<div class="card-header">
								<span>columns方式+快捷按钮组</span>
							</div>
						</template>
						<common-table
							:columns="getBtnsTableColumns"
							:fill-screen="false"
							:data="data"
							:getBtns="getBtns"
						></common-table>
					</el-card>
				</el-col>
				<el-col :span="24">
					<el-card class="card-view">
						<template #header>
							<div class="card-header">
								<span>官方示例</span>
							</div>
						</template>
						<common-table
							:data="data"
							:fill-screen="false"
						>
							<el-table-column
								prop="username"
								label="用户名"
							>
							</el-table-column>
							<el-table-column
								prop="phone"
								label="手机号"
							>
							</el-table-column>
							<el-table-column label="操作">
								<template #default="{ row, column, $index }">
									<i-btns
										:btns="btns"
										:more-number="1"
										:data="row"
									></i-btns>
								</template>
							</el-table-column>
						</common-table>
					</el-card>
				</el-col>
			</el-row>
		</div>
	</div>
</template>
<script lang="tsx" setup>
interface DataItem {
	createTime: string;
	email: string;
	id: string;
	phone: string;
	roles: string;
	status: boolean;
	username: string;
}

const data: DataItem[] = [
	{
		id: '710000200703152523',
		username: 'Ruth Gonzalez',
		phone: '15675276513',
		email: '<EMAIL>',
		roles: 'editor',
		status: true,
		createTime: '2010-05-24 04:10:31',
	},
	{
		id: '710000197209169085',
		username: 'Frank Hernandez',
		phone: '15552849626',
		email: '<EMAIL>',
		roles: 'admin',
		status: false,
		createTime: '2007-09-30 06:47:25',
	},
];

// #region columns方式
const allTableColumns: CommonTableColumn<DataItem>[] = [
	{
		label: '用户名',
		prop: 'username',
		hoverHighLight: true,
	},
	{
		label: '手机号',
		prop: 'phone',
		hoverHighLight: true,
	},
	{
		label: '角色',
		prop: 'roles',
	},
	{
		label: '邮箱',
		prop: 'email',
	},
	{
		label: '创建时间',
		prop: 'createTime',
	},
	{
		label: '操作',
		width: 120,
		formatter: (row, column, cellValue, index) => {
			const btns: OptionBtn[] = [
				{
					label: '编辑',
					onClick() {},
				},
				{
					label: '删除',
					tips: '确定删除吗？',
					placement: 'left-start',
					onClick() {},
				},
			];
			return <i-btns btns={btns}></i-btns>;
		},
	},
];
// #endregion

// #region 插槽表格
const slotTableColumns: CommonTableColumn[] = [
	{
		label: '用户名',
		prop: 'username',
		slotHeaderName: 'slotUsername', // 自定义header
	},
	{
		label: '手机号',
		prop: 'phone',
		slotName: 'slotPhone', // 自定义渲染内容
	},
	{
		label: '操作',
		slotName: 'action', // 自定义渲染内容
	},
];

// 点击操作
const handleCommand = (type: string, data: any) => {
	console.log('handleCommand', type, data);
};
// #endregion

// #region 按钮组表格
const getBtnsTableColumns: CommonTableColumn<DataItem>[] = [
	{
		label: '用户名',
		prop: 'username',
		hoverHighLight: true,
	},
	{
		label: '手机号',
		prop: 'phone',
		hoverHighLight: true,
	},
	{
		label: '角色',
		prop: 'roles',
	},
	{
		label: '邮箱',
		prop: 'email',
	},
	{
		label: '创建时间',
		prop: 'createTime',
	},
];

const btns: OptionBtn<DataItem>[] = [
	{
		label: '查看',
		auth: '',
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
	{
		label: '隐藏的按钮1',
		auth: '',
		hide: true,
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
	{
		label: '隐藏的按钮2',
		auth: '',
		hide: () => true,
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
	{
		label: '禁用按钮',
		auth: '',
		disabled: () => true,
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
	{
		label: '删除',
		auth: '',
		tips: '确定删除吗？',
		placement: 'left-start',
		more: true,
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
	{
		label: '编辑',
		auth: '',
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
];
function getBtns(row: DataItem, column: CommonTableColumn<DataItem>) {
	return btns;
}
// #endregion
</script>
<style lang="scss" scoped>
.container {
	padding: 20px;
	height: 100%;

	.container-box {
		overflow: auto;
		height: 100%;
	}

	.card-view {
		margin-bottom: 20px;
	}
}
</style>
