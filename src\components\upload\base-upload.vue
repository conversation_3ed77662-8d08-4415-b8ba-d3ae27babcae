<!--
  功能：基础文件上传组件-基于el-upload增加基础设置
  作者：ligw
  时间：2024年08月08日 16:09:22
  版本：v1.0
  修改记录：
  修改内容：
  修改人员：
  修改时间：
-->
<template>
	<el-upload
		ref="uploadRef"
		:action="url"
		:headers="headers"
		:on-exceed="handleExceed"
		v-bind="$attrs"
	>
		<template #default><slot></slot></template>
		<!-- <template #trigger><slot name="trigger"></slot></template> -->
		<template #tip>
			<div
				v-if="tip"
				class="el-upload__tip"
			>
				{{ tip }}
			</div>
		</template>
		<template #file="{ file }"
			><slot
				name="file"
				:file="file"
			></slot
		></template>
	</el-upload>
</template>
<script lang="ts" setup>
import { getLocalToken } from '@/utils/token';
import { ref, defineExpose, computed, useAttrs } from 'vue';
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus';
import { envConfig, isDev } from '@/config';
import { Awaitable } from '@vueuse/core';
import { setUrlParam } from '@/utils/index';
interface Props {
	tip?: string; // 提示说明文字
	action?: string; // 上传请求路径
	params?: Record<string, any> | Awaitable<Record<string, any>> | ((rawFile: UploadRawFile) => Awaitable<Record<string, any>>);
	type: string;
}
const props = withDefaults(defineProps<Props>(), {
	tip: '',
	type: '0400',
	// /open-api/v1/file/part/upload
	action: '',
});
const uploadRef = ref<UploadInstance>();
const attrs = useAttrs();
const url = computed(() => {
	const action =
		(isDev ? location.origin : envConfig.VITE_BASE_FILE_API) + (isDev ? '/idaas_api' : '') + `/oss/uploadByCategoryId/` + props.type;
	return props.action || action + setUrlParam(props.params);
});
const handleExceed: UploadProps['onExceed'] = () => {
	ElMessage.warning(`当前设置最大上传数量为${attrs.limit}，请删除后再进行操作。`);
};
const headers = ref<Headers | Record<string, string | number | null | undefined>>({
	Authorization: getLocalToken(),
});
defineExpose({
	uploadRef,
});
</script>
<style scoped lang="scss"></style>
