<template>
	<el-dialog v-model="modelValue_" title="二维码" append-to-body align-center width="400">
		<VueQrcode v-if="modelValue_" :text="text" :size="200" />
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="modelValue_ = false">关闭</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
interface Props {
	text: string;
	modelValue: boolean;
}
const props = withDefaults(defineProps<Props>(), {});
const emit = defineEmits<{
	(ev: 'update:modelValue', value: boolean): void;
}>();
const modelValue_ = computed({
	get() {
		return props.modelValue;
	},
	set(value) {
		emit('update:modelValue', value);
	},
});
</script>
<style lang="scss" scoped></style>
