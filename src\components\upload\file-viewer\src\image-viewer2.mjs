import { defineComponent, markRaw, ref, effectScope, shallowRef, computed, watch, nextTick, onMounted, openBlock, createBlock, Teleport, createVNode, Transition, withCtx, createElementVNode, normalizeClass, unref, normalizeStyle, withModifiers, createCommentVNode, createElementBlock, Fragment, resolveDynamicComponent, renderList, withDirectives, vShow, renderSlot } from 'vue';
import { useEventListener } from '@vueuse/core';
import { throttle } from 'lodash-es';
import 'element-plus/es/hooks/index.mjs';
import 'element-plus/es/constants/index.mjs';
import 'element-plus/es/utils/index.mjs';
import { ElIcon } from 'element-plus/es/components/icon/index.mjs';
import { ElTooltip } from 'element-plus/es/components/index.mjs';
import {
  FullScreen, ScaleToOriginal, Close, ArrowLeft, ArrowRight, ZoomOut,
  ZoomIn, RefreshLeft, RefreshRight, Download, Warning
} from '@element-plus/icons-vue';
import { imageViewerProps, imageViewerEmits } from './image-viewer.mjs';
import _export_sfc from 'element-plus/es/_virtual/plugin-vue_export-helper.mjs';
import { useLocale } from 'element-plus/es/hooks/use-locale/index.mjs';
import { useNamespace } from 'element-plus/es/hooks/use-namespace/index.mjs';
import { useZIndex } from 'element-plus/es/hooks/use-z-index/index.mjs';
import { EVENT_CODE } from 'element-plus/es/constants/aria.mjs';
import { keysOf } from 'element-plus/es/utils/objects.mjs';
import 'element-plus/es/components/image/style/css';
import { PatchFlags } from '@vue/shared';
import {downloadImage, parseFileSize, judgeFileType} from '@/utils/file.ts'

const rotate_left = /* @__PURE__ */ defineComponent({
  name: "RotateLeft",
  __name: "rotateLeft",
  setup(__props) {
    return (_ctx, _cache) => (openBlock(), createElementBlock("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      createElementVNode("path", {
        fill: "currentColor",
        d: "M672 418H144c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32z m-44 402H188V494h440v326z"
      }),
      createElementVNode("path", {
        fill: "currentColor",
        d: "M819.3 328.5c-78.8-100.7-196-153.6-314.6-154.2l-0.2-64c0-6.5-7.6-10.1-12.6-6.1l-128 101c-4 3.1-3.9 9.1 0 12.3L492 318.6c5.1 4 12.7 0.4 12.6-6.1v-63.9c12.9 0.1 25.9 0.9 38.8 2.5 42.1 5.2 82.1 18.2 119 38.7 38.1 21.2 71.2 49.7 98.4 84.3 27.1 34.7 46.7 73.7 58.1 115.8 11 40.7 14 82.7 8.9 124.8-0.7 5.4-1.4 10.8-2.4 16.1h74.9c14.8-103.6-11.3-213-81-302.3z"
      }),
    ]));
  }
});
const rotate_right = /* @__PURE__ */ defineComponent({
  name: "RotateRight",
  __name: "rotateRight",
  setup(__props) {
    return (_ctx, _cache) => (openBlock(), createElementBlock("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      createElementVNode("path", {
        fill: "currentColor",
        d: "M480.5 251.2c13-1.6 25.9-2.4 38.8-2.5v63.9c0 6.5 7.5 10.1 12.6 6.1L660 217.6c4-3.2 4-9.2 0-12.3l-128-101c-5.1-4-12.6-0.4-12.6 6.1l-0.2 64c-118.6 0.5-235.8 53.4-314.6 154.2-69.6 89.2-95.7 198.6-81.1 302.4h74.9c-0.9-5.3-1.7-10.7-2.4-16.1-5.1-42.1-2.1-84.1 8.9-124.8 11.4-42.2 31-81.1 58.1-115.8 27.2-34.7 60.3-63.2 98.4-84.3 37-20.6 76.9-33.6 119.1-38.8z"
      }),
      createElementVNode("path", {
        fill: "currentColor",
        d: "M880 418H352c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32z m-44 402H396V494h440v326z"
      }),
    ]));
  }
});


const _hoisted_1 = ["src", "crossorigin"];
const __default__ = defineComponent({
  name: "FileViewer"
});
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...__default__,
  props: imageViewerProps,
  emits: imageViewerEmits,
  setup(__props, { expose, emit }) {
    var _a;
    const props = __props;
    const modes = {
      CONTAIN: {
        name: "contain",
        icon: markRaw(FullScreen)
      },
      ORIGINAL: {
        name: "original",
        icon: markRaw(ScaleToOriginal)
      }
    };
    const { t } = useLocale();
    const ns = useNamespace("image-viewer");
    const { nextZIndex } = useZIndex();
    const wrapper = ref();
    const imgRefs = ref([]);
    const scopeEventListener = effectScope();
    const loading = ref(true);
    const activeIndex = ref(props.initialIndex);
    const mode = shallowRef(modes.CONTAIN);
    const transform = ref({
      scale: 1,
      deg: 0,
      offsetX: 0,
      offsetY: 0,
      enableTransition: false
    });
    const zIndex = ref((_a = props.zIndex) != null ? _a : nextZIndex());
    const isSingle = computed(() => {
      const { urlList } = props;
      return urlList.length <= 1;
    });
    const isFirst = computed(() => {
      return activeIndex.value === 0;
    });
    const isLast = computed(() => {
      return activeIndex.value === props.urlList.length - 1;
    });
    const currentImg = computed(() => {
      return props.urlList[activeIndex.value];
    });
    const currentFileType = computed(() => {
      const { fileType } = currentImg.value
      return judgeFileType(fileType)
    })
    const arrowPrevKls = computed(() => [
      ns.e("btn"),
      ns.e("prev"),
      ns.is("disabled", !props.infinite && isFirst.value)
    ]);
    const arrowNextKls = computed(() => [
      ns.e("btn"),
      ns.e("next"),
      ns.is("disabled", !props.infinite && isLast.value)
    ]);
    const imgStyle = computed(() => {
      const { scale, deg, offsetX, offsetY, enableTransition } = transform.value;
      let translateX = offsetX / scale;
      let translateY = offsetY / scale;
      switch (deg % 360) {
        case 90:
        case -270:
          ;
          [translateX, translateY] = [translateY, -translateX];
          break;
        case 180:
        case -180:
          ;
          [translateX, translateY] = [-translateX, -translateY];
          break;
        case 270:
        case -90:
          ;
          [translateX, translateY] = [-translateY, translateX];
          break;
      }
      const style = {
        transform: `scale(${scale}) rotate(${deg}deg) translate(${translateX}px, ${translateY}px)`,
        transition: enableTransition ? "transform .3s" : ""
      };
      if (mode.value.name === modes.CONTAIN.name) {
        style.maxWidth = style.maxHeight = "100%";
      }
      return style;
    });
    function hide() {
      unregisterEventListener();
      emit("close");
    }
    function registerEventListener() {
      const keydownHandler = throttle((e) => {
        switch (e.code) {
          case EVENT_CODE.esc:
            props.closeOnPressEscape && hide();
            break;
          case EVENT_CODE.space:
            toggleMode();
            break;
          case EVENT_CODE.left:
            prev();
            break;
          case EVENT_CODE.up:
            handleActions("zoomIn");
            break;
          case EVENT_CODE.right:
            next();
            break;
          case EVENT_CODE.down:
            handleActions("zoomOut");
            break;
        }
      });
      const mousewheelHandler = throttle((e) => {
        const delta = e.deltaY || e.deltaX;
        handleActions(delta < 0 ? "zoomIn" : "zoomOut", {
          zoomRate: props.zoomRate,
          enableTransition: false
        });
      });
      scopeEventListener.run(() => {
        useEventListener(document, "keydown", keydownHandler);
        useEventListener(document, "wheel", mousewheelHandler);
      });
    }
    function unregisterEventListener() {
      scopeEventListener.stop();
    }
    function handleImgLoad() {
      loading.value = false;
    }
    function handleImgError(e) {
      loading.value = false;
      e.target.alt = t("el.image.error");
    }
    function handleMouseDown(e) {
      if (loading.value || e.button !== 0 || !wrapper.value)
        return;
      transform.value.enableTransition = false;
      const { offsetX, offsetY } = transform.value;
      const startX = e.pageX;
      const startY = e.pageY;
      const dragHandler = throttle((ev) => {
        transform.value = {
          ...transform.value,
          offsetX: offsetX + ev.pageX - startX,
          offsetY: offsetY + ev.pageY - startY
        };
      });
      const removeMousemove = useEventListener(document, "mousemove", dragHandler);
      useEventListener(document, "mouseup", () => {
        removeMousemove();
      });
      e.preventDefault();
    }
    function reset() {
      transform.value = {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false
      };
    }
    function toggleMode() {
      if (loading.value)
        return;
      const modeNames = keysOf(modes);
      const modeValues = Object.values(modes);
      const currentMode = mode.value.name;
      const index = modeValues.findIndex((i) => i.name === currentMode);
      const nextIndex = (index + 1) % modeNames.length;
      mode.value = modes[modeNames[nextIndex]];
      reset();
    }
    function setActiveItem(index) {
      const len = props.urlList.length;
      activeIndex.value = (index + len) % len;
    }
    function prev() {
      if (isFirst.value && !props.infinite)
        return;
      setActiveItem(activeIndex.value - 1);
    }
    function next() {
      if (isLast.value && !props.infinite)
        return;
      setActiveItem(activeIndex.value + 1);
    }
    function handleActions(action, options = {}) {
      if (loading.value)
        return;
      const { minScale, maxScale } = props;
      const { zoomRate, rotateDeg, enableTransition } = {
        zoomRate: props.zoomRate,
        rotateDeg: 90,
        enableTransition: true,
        ...options
      };
      switch (action) {
        case "zoomOut":
          if (transform.value.scale > minScale) {
            transform.value.scale = Number.parseFloat((transform.value.scale / zoomRate).toFixed(3));
          }
          break;
        case "zoomIn":
          if (transform.value.scale < maxScale) {
            transform.value.scale = Number.parseFloat((transform.value.scale * zoomRate).toFixed(3));
          }
          break;
        case "clockwise":
          transform.value.deg += rotateDeg;
          emit("rotate", transform.value.deg);
          break;
        case "anticlockwise":
          transform.value.deg -= rotateDeg;
          emit("rotate", transform.value.deg);
          break;
        case "download":
          const { fileAccessPath, fileName, fileType } = currentImg.value
          const name = fileName + '.' + (fileType.split('/')[1] || fileType)
          downloadImage(fileAccessPath, name, fileType)
          break;
      }
      transform.value.enableTransition = enableTransition;
    }
    watch(currentImg, () => {
      nextTick(() => {
        const $img = imgRefs.value[0];
        if (!($img == null ? void 0 : $img.complete)) {
          loading.value = true;
        }
      });
    });
    watch(activeIndex, (val) => {
      reset();
      emit("switch", val);
    });
    onMounted(() => {
      var _a2, _b;
      registerEventListener();
      (_b = (_a2 = wrapper.value) == null ? void 0 : _a2.focus) == null ? void 0 : _b.call(_a2);
    });
    expose({
      setActiveItem
    });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(Teleport, {
        to: "body",
        disabled: !_ctx.teleported
      }, [
        createVNode(Transition, {
          name: "viewer-fade",
          appear: ""
        }, {
          default: withCtx(() => [
            createElementVNode("div", {
              ref_key: "wrapper",
              ref: wrapper,
              tabindex: -1,
              class: normalizeClass(unref(ns).e("wrapper")),
              style: normalizeStyle({ zIndex: zIndex.value })
            }, [
              createElementVNode("div", {
                class: normalizeClass(unref(ns).e("mask")),
                onClick: _cache[0] || (_cache[0] = withModifiers(($event) => _ctx.hideOnClickModal && hide(), ["self"]))
              }, null, 2),
              createCommentVNode(" CLOSE "),
              createElementVNode("span", {
                class: normalizeClass([unref(ns).e("btn"), unref(ns).e("close")]),
                onClick: hide
              }, [
                createVNode(unref(ElIcon), null, {
                  default: withCtx(() => [
                    createVNode(unref(Close))
                  ]),
                  _: 1
                })
              ], 2),
              createCommentVNode(" ARROW "),
              !unref(isSingle) ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
                createElementVNode("span", {
                  class: normalizeClass(unref(arrowPrevKls)),
                  onClick: prev
                }, [
                  createVNode(unref(ElIcon), null, {
                    default: withCtx(() => [
                      createVNode(unref(ArrowLeft))
                    ]),
                    _: 1
                  })
                ], 2),
                createElementVNode("span", {
                  class: normalizeClass(unref(arrowNextKls)),
                  onClick: next
                }, [
                  createVNode(unref(ElIcon), null, {
                    default: withCtx(() => [
                      createVNode(unref(ArrowRight))
                    ]),
                    _: 1
                  })
                ], 2)
              ], 64)) : createCommentVNode("v-if", true),
              createCommentVNode(" ACTIONS "),
              currentFileType.value === 'image' ? createElementVNode("div", {
                class: normalizeClass([unref(ns).e("btn"), unref(ns).e("actions")])
              }, [
                createElementVNode("div", {
                  class: normalizeClass(unref(ns).e("actions__inner"))
                }, [
                  createVNode(unref(ElTooltip), {
                    placement: 'top',
                    content: '缩小'
                  }, ()=>[
                    createVNode(unref(ElIcon), {
                      onClick: _cache[1] || (_cache[1] = ($event) => handleActions("zoomOut"))
                    }, {
                      default: withCtx(() => [
                        createVNode(unref(ZoomOut))
                      ]),
                      _: 1
                    })
                  ]),
                  createVNode(unref(ElTooltip), {
                    placement: 'top',
                    content: '放大'
                  }, ()=>[
                    createVNode(unref(ElIcon), {
                      onClick: _cache[2] || (_cache[2] = ($event) => handleActions("zoomIn"))
                    }, {
                      default: withCtx(() => [
                        createVNode(unref(ZoomIn))
                      ]),
                      _: 1
                    })
                  ]),
                  // createElementVNode("i", {
                  //   class: normalizeClass(unref(ns).e("actions__divider"))
                  // }, null, 2),
                  createVNode(unref(ElTooltip), {
                    placement: 'top',
                    content: mode.value.name === modes.CONTAIN.name ? '全屏' : '还原'
                  }, ()=>[
                    createVNode(unref(ElIcon), { onClick: toggleMode }, {
                      default: withCtx(() => [
                        (openBlock(), createBlock(resolveDynamicComponent(unref(mode).icon)))
                      ]),
                      _: 1
                    })
                  ]),
                  // createElementVNode("i", {
                  //   class: normalizeClass(unref(ns).e("actions__divider"))
                  // }, null, 2),
                  createVNode(unref(ElTooltip), {
                    placement: 'top',
                    content: '向左旋转'
                  }, ()=>[
                    createVNode(unref(ElIcon), {
                      onClick: _cache[3] || (_cache[3] = ($event) => handleActions("anticlockwise"))
                    }, {
                      default: withCtx(() => [
                        // createVNode(unref(RefreshLeft))
                        createVNode(unref(rotate_left))
                      ]),
                      _: 1
                    })
                  ]),
                  createVNode(unref(ElTooltip), {
                    placement: 'top',
                    content: '向右旋转'
                  }, ()=>[
                    createVNode(unref(ElIcon), {
                      onClick: _cache[4] || (_cache[4] = ($event) => handleActions("clockwise"))
                    }, {
                      default: withCtx(() => [
                        // createVNode(unref(RefreshRight))
                        createVNode(unref(rotate_right))
                      ]),
                      _: 1
                    })
                  ]),
                  createVNode(unref(ElTooltip), {
                    placement: 'top',
                    content: '下载图片'
                  }, ()=>[
                    createVNode(unref(ElIcon), {
                      onClick: _cache[5] || (_cache[5] = ($event) => handleActions("download"))
                    }, {
                      default: withCtx(() => [
                        createVNode(unref(Download))
                      ]),
                      _: 1
                    }),
                  ]),
                  createVNode(unref(ElTooltip), {
                    placement: 'top',
                  }, {
                    default: withCtx(() => [
                      createVNode(unref(ElIcon), {
                      }, {
                        default: withCtx(() => [
                          createVNode(unref(Warning), {
                            style: 'transform: rotate(180deg)'
                          })
                        ]),
                        _: 1
                      })
                    ]),
                    content: withCtx(() => [
                      createElementVNode('div', {
                      }, `图片名称：${currentImg.value?.fileName || '-'}`),
                      createElementVNode('div', {
                      }, `上传人：${currentImg.value?.createUser || '-'}`),
                      createElementVNode('div', {
                      }, `日期：${currentImg.value?.fileCreateTime || '-'}`),
                      createElementVNode('div', {
                      }, `大小：${parseFileSize(currentImg.value?.fileSize) || '-'}`)
                    
                    ])
                  })
                ], 2)
              ], 2): createCommentVNode("v-if", true),
              createCommentVNode(" CANVAS "),
              createElementVNode("div", {
                class: normalizeClass(unref(ns).e("canvas"))
              }, [
                (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.urlList, (url, i) => {
                  return withDirectives((openBlock(),
                  currentFileType.value === 'image' ? createElementBlock("img", {
                    ref_for: true,
                    ref: (el) => imgRefs.value[i] = el,
                    key: url,
                    src: url?.fileAccessPath || url,
                    style: normalizeStyle(unref(imgStyle)),
                    class: normalizeClass(unref(ns).e("img")),
                    crossorigin: _ctx.crossorigin,
                    onLoad: handleImgLoad,
                    onError: handleImgError,
                    onMousedown: handleMouseDown
                  }, null, 46, _hoisted_1) :
                  createElementBlock("video", {
                    ref_for: true,
                    ref: (el) => imgRefs.value[i] = el,
                    key: url,
                    src: url?.fileAccessPath || url,
                    style: normalizeStyle(unref(imgStyle)),
                    class: normalizeClass(unref(ns).e("img")),
                    crossorigin: _ctx.crossorigin,
                    controls: true,
                    autoplay: false,
                    muted: true,
                    loop: true,
                    onLoad: handleImgLoad,
                    onError: handleImgError,
                    onMousedown: handleMouseDown
                  }, null, 46, _hoisted_1)), [
                    [vShow, i === activeIndex.value]
                  ]);
                }), 128))
              ], 2),
              renderSlot(_ctx.$slots, "default")
            ], 6)
          ]),
          _: 3
        })
      ], 8, ["disabled"]);
    };
  }
});
var ImageViewer = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "image-viewer.vue"]]);

export { ImageViewer as default };
//# sourceMappingURL=image-viewer2.mjs.map
