<template>
	<div
		class="list-navigation"
		:class="{ 'non-search-s': !isShowSearch }"
	>
		<div
			class="header-view"
			:class="{ 'action-s': isShowTopAction }"
			v-if="isShowSearch"
		>
			<el-input
				class="input-t"
				v-model="searchText"
				placeholder="搜索"
				:prefix-icon="Search"
				:clearable="true"
			/>
			<slot>
				<el-button
					icon="Plus"
					circle
					v-if="isShowTopAction"
					@click="emit('headerClick')"
				/>
			</slot>
		</div>
		<div
			class="main-view"
			:class="{ 'non-search-s': !isShowSearch }"
			v-if="renderList.length > 0"
		>
			<div
				class="item-cell"
				v-for="(item, index) in renderList"
				:key="index"
				:class="{ 'is-active': activedKey == item.key }"
				@click="handleClick(item.key)"
				@mouseover="item['is-show-operate-inner'] = true"
				@mouseout="item['is-show-operate-inner'] = false"
			>
				<div class="left-content">
					<icon-font :type="item.icon"></icon-font>
					<div class="title-text">{{ item.title }}</div>
				</div>
				<slot
					name="node-right"
					v-bind="item || {}"
				>
					<div
						v-if="isShowBadge && (isShowAllBadge || item.num)"
						class="right-badge"
					>
						{{ item.num > 100 ? '99+' : item.num }}
					</div>

					<el-dropdown
						v-if="isShowOperation"
						v-show="isPernantShow || item['is-show-operate-inner']"
						:trigger="activeTrigger"
						:teleported="false"
					>
						<span class="el-dropdown-link">
							<el-icon
								class="el-icon--right"
								@click="item['is-operate-active'] = true"
							>
								<MoreFilled />
							</el-icon>
						</span>
						<template #dropdown>
							<el-dropdown-menu v-if="item.operationList && item.operationList.length !== 0">
								<el-dropdown-item
									v-for="(operation, index) in item.operationList"
									:divided="operation.isDivided"
									:disabled="operation.isDisabled"
									@click.stop="
										operation.func
											? operation.func({ ...item })
											: emit('action', {
													code: operation.actionCode,
													source: {
														...item,
													},
											  })
									"
									>{{ operation.actionLabel }}</el-dropdown-item
								>
							</el-dropdown-menu>

							<div
								class="empty-s"
								v-else
							>
								暂无操作
							</div>
						</template>
					</el-dropdown>
				</slot>
			</div>
		</div>

		<div
			class="main-view empty-s"
			v-else
		>
			-暂无数据-
		</div>
	</div>
</template>
<script setup lang="tsx">
import { ref, reactive, watch, onMounted, onBeforeMount, nextTick } from 'vue';
import { Menu, Search } from '@element-plus/icons-vue';

/** 初始化定义 */
type BooleanFunc = (data: any) => boolean;

export interface OperationItem {
	actionCode: string; // 操作code
	actionLabel: string; // 操作标签
	isDivided: boolean; // 是否分割
	isDisabled: boolean | BooleanFunc; // 是否禁用
	func?: () => any;
	icon?: string; // 操作标签
}

export interface ListItem {
	key: any;
	icon?: string;
	title?: string;
	num?: number;
	operationList?: OperationItem[]; // 单个选项的操作项
}

interface Props {
	listData: ListItem[];
	// Search
	isShowSearch?: boolean; // 是否显示search
	// bagde相关
	isShowBadge?: boolean; // 是否显示
	isShowAllBadge?: boolean; // 是否在为 0 的时候显示 badge
	// operation相关
	isShowOperation?: boolean;
	showTrigger?: string; // normal: 始终显示, hover: 浮入显示; 默认hover
	activeTrigger?: string; // click: 点击； hover: 浮入； 默认click
	operationList?: OperationItem[]; // 全部列表的操作项
	// top-action
	isShowTopAction?: boolean; // 是否显示顶部按钮
}

const props = withDefaults(defineProps<Props>(), {
	showAllBadge: true,
	listData: [],
	isShowBadge: false,
	isShowOperation: false,
	isShowAction: false,
	isShowSearch: true,
	showTrigger: 'hover',
	activeTrigger: 'hover',
	operationList: [],
});

const emit = defineEmits<{
	// (ev: 'update:value', value: string): void;
	(ev: 'action', value: object): void;
	(ev: 'headerClick', value: void): void;
}>();

/** 索引选择相关 */
const activedKey = defineModel();
function handleClick(key) {
	activedKey.value = key;
}

/** 顶部serach相关逻辑 */
const renderList = ref([]);
const searchText = ref('');
watch(
	searchText,
	(val) => {
		if (val) {
			renderList.value = renderList.value.filter((t) => {
				return t.title.indexOf(val) !== -1;
			});
		} else {
			renderList.value = preparedData;
		}
	},
	{ immediate: false },
);

/** 表格溢出判断相关(未完成) */
function isOverflow(element) {
	return element.scrollWidth > element.clientWidth;
}

/** 表格操作功能相关逻辑 */
const isPernantShow = props.showTrigger === 'normal';
let preparedData = [];
function init() {
	props.listData.forEach((prop) => {
		const t = Object.assign({}, prop);
		t['is-show-operate-inner'] = false;
		t['is-operate-active'] = false;
		// 处理operationList
		if (!props.isShowOperation) {
			preparedData.push(t);
			return;
		}

		if (!t.operationList) {
			t.operationList = [];
			props.operationList.forEach((oper) => {
				t.operationList.push(Object.assign({}, oper));
			});
		}

		t.operationList.forEach((oper) => {
			if (oper.isDisabled && typeof oper.isDisabled === 'function') {
				oper.isDisabled = oper.isDisabled({ ...t });
			}
		});

		// console.log(t)

		preparedData.push(t);
	});

	renderList.value = preparedData;
}

init();
</script>
<style lang="scss" scoped>
.list-navigation {
	&.non-search-s {
		padding: 0 !important;
	}
	padding: 8px 0;
	.header-view {
		margin-bottom: 8px;
		padding: 0 20px;

		&.action-s {
			display: flex;
			& > .input-t {
				margin-right: 8px;
			}
		}
	}

	.main-view {
		&.empty-s {
			color: rgba($color: #000000, $alpha: 0.3);
			text-align: center;
			padding-top: 8px;
			font-size: 12px;
		}

		.item-cell {
			height: 40px;
			padding: 0 20px;
			cursor: pointer;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.left-content {
				display: flex;
				align-items: center;
				flex: 1;
				min-width: 0;
				.title-text {
					margin-left: 9px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #1d2129;
					line-height: 22px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}

			.right-badge {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 38px;
				height: 22px;
				background: #f2f3f5;
				border-radius: 4px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #86909c;
				line-height: 20px;
			}
		}

		.item-cell:hover {
			background: #f2f3f5;
		}

		.is-active {
			background: #f1f9ff !important;
			color: #0075c2 !important;
			.title-text {
				color: #0075c2 !important;
			}
		}
	}
}

.empty-s {
	padding: 12px;
	color: rgba($color: #000000, $alpha: 0.5);
	font-size: 12px;
}
</style>
