// 样式在index.vue中
<template>
	<div class="table-sort-filter" :class="customClass">
		<div class="describe-text">快捷操作</div>
		<div>
			<div
				class="item-cell"
				:class="{ 'active-cell': item.value == sortValue }"
				v-for="(item, index) in options"
				:key="index"
				@click="handleClick(item.value)"
			>
				<el-icon :size="16">
					<component :is="item.icon" />
				</el-icon>
				<span class="item-label">{{ item.label }}</span>
			</div>
		</div>
		<!-- <el-button v-if="showBtn" class="submit-btn" type="primary" size="small">确定</el-button> -->
	</div>
</template>

<script lang="ts" setup>
import { ref, watch, markRaw } from 'vue';
import { Menu, Top, Bottom } from '@element-plus/icons-vue';
interface Props {
	value?: string;
	titleText?: string;
	showBtn?: boolean;
	customClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
	titleText: '',
	showBtn: true,
});

const emit = defineEmits<{
	(ev: 'update:value', value: string): void;
	(ev: 'search'): void; //
}>();

const sortValue = ref('');
const options = [
	{
		icon: markRaw(Top),
		label: '正序排序',
		value: 'asc',
	},
	{
		icon: markRaw(Bottom),
		label: '倒序排序',
		value: 'desc',
	},
];

watch(
	() => props.value,
	(val) => {
		if (val) {
			sortValue.value = val;
		}
	},
	{
		immediate: true
	}
);

function handleClick(val) {
	console.log('111');
	console.log(val);

	sortValue.value = val;
	emit('update:value', sortValue.value);
	emit('search');
}
</script>
<style lang="scss" scoped>
.add-p-b-10 {
	padding-bottom: 10px;
}

.table-sort-filter {

	.describe-text {
		font-family: MicrosoftYaHei;
		font-size: 12px;
		color: #86909c;
		line-height: 20px;
		height: 34px;
		padding: 10px 12px 4px 12px;
		box-sizing: border-box;
	}

	.item-cell {
		height: 34px;
		padding: 0 12px;
		cursor: pointer;
		color: #1d2129;
		display: flex;
		align-items: center;
		.item-label {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 14px;
			line-height: 22px;
			margin-left: 9px;
		}
	}

	.item-cell:hover {
		background: #f2f3f5;
	}

	.active-cell {
		background: #f1f9ff !important;
		color: var(--el-color-primary);
	}

	.submit-btn {
		float: right;
		margin: 10px 16px;
		height: 28px;
	}
}
</style>
