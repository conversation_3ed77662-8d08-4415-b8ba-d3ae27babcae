<!--
  功能：文件导出组件
  作者：ligw
  时间：2024年08月09日 15:12:17
  版本：v1.0
  修改记录：
  修改内容：
  修改人员：
  修改时间：
-->
<template>
  <div @click="handleExport">
    <slot></slot>
    <ElButton 
      v-if="!$slots.default" 
      v-bind="$attrs"
      :loading="loading" 
      class="excel-upload-btn"  
      color="#0075C2"  
      plain
      type="primary"
      >
      <span >
        {{ title }}
      </span>
    </ElButton>
  </div>
</template>
<script lang='ts' setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus';
import { downloadByData } from '@/utils/file';
interface Props {
	title?: string ; // 导出按钮文案
  exportFun: Function; // 导出方法实现
  successMessage?: string; // 操作成功文案
}
const props = withDefaults(defineProps<Props>(), {
  title: '导出',
  exportFun: () => { },
  successMessage: '导出成功！'
});
// 上传按钮加载状态，防止重复点击
const loading = ref(false)
// 导出方法
const handleExport = () => {
  try {
    props.exportFun && props.exportFun((response: BlobPart, fileName: string, mime?: string, bom?: BlobPart) => {
      downloadByData(response, fileName, mime, bom)
      ElMessage.success(props?.successMessage)
    })
  } catch (error) {
    ElMessage.error('导出失败！')
  }
}
</script>
<style scoped lang='scss'>
</style>
