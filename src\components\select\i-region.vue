<template>
	<div class="t-form-box g-form-box">
		<el-form-item class="t-item-1" :label="label" :rules="rules_province">
			<i-select
				v-model="provinceCode_"
				v-model:label="provinceName_"
				placeholder="省"
				@change="shengChange"
				:disabled="disabledProvince"
			>
				<el-option v-for="item in shengOptions" :key="item.value" :label="item.label" :value="item.value" />
			</i-select>
		</el-form-item>
		<el-form-item label="" :prop="cityCodeProp" :rules="rules_city">
			<i-select
				v-model="cityCode_"
				v-model:label="cityName_"
				clearable
				placeholder="市"
				@change="shiChange"
				@focus="onFocus('1')"
				:disabled="disabledCity"
			>
				<el-option v-for="item in shiOptions" :key="item.value" :label="item.label" :value="item.value" />
			</i-select>
		</el-form-item>
		<el-form-item class="t-item-3" label="" :prop="regionCodeProp">
			<i-select
				:disabled="disabledRegion"
				v-model="regionCode_"
				v-model:label="regionName_"
				clearable
				placeholder="县"
				@change="xianChange"
				@focus="onFocus('2')"
			>
				<el-option v-for="item in xianOptions" :key="item.value" :label="item.label" :value="item.value" />
			</i-select>
		</el-form-item>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, watch } from 'vue';
import { api_getDistrictList, RequestDistrictListParam, ResponseDistrictListInfo } from '@/api';
import { rule_true_change } from '@/utils/validator';

export interface Props {
	provinceCode: string | undefined; // 省
	provinceName: string | undefined; // 省 名字
	cityCode: string | undefined; // 市
	cityName: string | undefined; // 市 名字
	regionCode: string | undefined; // 区
	regionName: string | undefined; // 区 名字
	label?: string;
	provinceCodeProp?: string; // 省
	cityCodeProp?: string; // 市
	regionCodeProp?: string; // 区
	disabledProvince?: boolean; // 省  禁选
	disabledCity?: boolean; // 市  禁选
	disabledRegion?: boolean; // 省  禁选
}
const props = withDefaults(defineProps<Props>(), {
	label: '所属行政区划',
	provinceCodeProp: 'regionCodeProvince', // 省
	cityCodeProp: 'regionCodeCity', // 市
	regionCodeProp: 'regionCode', // 区
	disabledProvince: false,
	disabledCity: false,
	disabledRegion: false,
});

const emit = defineEmits<{
	(ev: 'update:provinceCode', value: string): void;
	(ev: 'update:provinceName', value: string): void;
	(ev: 'update:cityCode', value: string): void;
	(ev: 'update:cityName', value: string): void;
	(ev: 'update:regionCode', value: string): void;
	(ev: 'update:regionName', value: string): void;
	(ev: 'change'): void;
}>();
const rules_province = { ...rule_true_change };
const rules_city = { ...rule_true_change };

const shengOptions = ref<ResponseDistrictListInfo[]>([]);
const shiOptions = ref<ResponseDistrictListInfo[]>([]);
const xianOptions = ref<ResponseDistrictListInfo[]>([]);

const provinceCode_ = computed({
	get() {
		return props.provinceCode || '';
	},
	set(value) {
		// if (value) {
		// 	getDistrictList({
		// 		level: '1',
		// 		parentCode: value,
		// 	}).then((list) => {
		// 		shiOptions.value = list;
		// 	});
		// }
		emit('update:provinceCode', value);
	},
});
const provinceName_ = computed({
	get() {
		return props.provinceName || '';
	},
	set(value) {
		emit('update:provinceName', value);
	},
});
const cityCode_ = computed({
	get() {
		return props.cityCode || '';
	},
	set(value) {
		// if (value) {
		// 	getDistrictList({
		// 		level: '2',
		// 		parentCode: value,
		// 	}).then((list) => {
		// 		xianOptions.value = list;
		// 	});
		// }
		emit('update:cityCode', value);
	},
});
const cityName_ = computed({
	get() {
		return props.cityName || '';
	},
	set(value) {
		emit('update:cityName', value);
	},
});
const regionCode_ = computed({
	get() {
		return props.regionCode || '';
	},
	set(value) {
		emit('update:regionCode', value);
	},
});
const regionName_ = computed({
	get() {
		return props.regionName || '';
	},
	set(value) {
		emit('update:regionName', value);
	},
});

function getDistrictList(param?: RequestDistrictListParam) {
	if (!param?.level) return Promise.reject('缺少参数');
	return api_getDistrictList(param).then((info = []) => {
		return info.map((val) => {
			return {
				...val,
				value: val.code + '',
				label: val.name,
			};
		});
	});
}
function onFocus(level?: string) {
	if (level === '1') {
		if (!provinceCode_.value) {
			ElMessage.error('请先选择省');
		}
	} else if (level === '2') {
		if (!cityCode_.value) {
			ElMessage.error('请先选择省市');
		}
	}
}
let timer;
watch(
	[() => props.provinceCode, () => props.cityCode],
	([val1, val2], [oldVal1, oldVal2]) => {
		clearTimeout(timer);
		timer = setTimeout(() => {
			if (!val1 && !val2) {
				// 重置了，  大概率是在外部重置了
				if (shengOptions.value.length) {
					provinceCode_.value = shengOptions.value[0].value as string;
					provinceName_.value = shengOptions.value[0].label;
					nextTick(() => {
						emit('change');
					});
				}
			} else {
				if (val1 && val1 !== oldVal1) {
					getDistrictList({
						level: '1',
						parentCode: val1,
					}).then((list) => {
						shiOptions.value = list;
					});
				}
				if (val2 && val2 !== oldVal2) {
					getDistrictList({
						level: '2',
						parentCode: val2,
					}).then((list) => {
						xianOptions.value = list;
					});
				}
			}
		}, 10);
	},
	{
		immediate: true,
	},
);
getDistrictList({
	level: '0',
}).then((list) => {
	shengOptions.value = list;
	if (list.length) {
		nextTick(() => {
			if (!provinceCode_.value) {
				provinceCode_.value = list[0].value;
				provinceName_.value = list[0].label;
				nextTick(() => {
					emit('change');
				});
			}
		});
	}
});
function shengChange() {
	cityCode_.value = '';
	cityName_.value = '';
	shiOptions.value = [];
	regionCode_.value = '';
	regionName_.value = '';
	xianOptions.value = [];
	nextTick(() => {
		emit('change');
	});
}
function shiChange() {
	regionCode_.value = '';
	regionName_.value = '';
	xianOptions.value = [];
	nextTick(() => {
		emit('change');
	});
}
function xianChange() {
	nextTick(() => {
		emit('change');
	});
}
</script>
<style lang="scss" scoped>
.t-form-box {
	width: 100%;
	display: flex;
	align-items: flex-end;
	flex-wrap: nowrap;
	justify-content: space-between;

	// margin-left: -14px;
	:deep(.el-form-item) {
		padding-left: 0;
		padding-right: 0;
		flex-grow: 0;
		// min-width: 33%;
		width: calc(33.3333% - 10px);
	}
	.t-item-1 {
		position: relative;
		margin-left: 0px;
		:deep(.el-form-item__label) {
			position: relative;
			width: 200px;
			// position: absolute;
		}
	}
}
</style>
