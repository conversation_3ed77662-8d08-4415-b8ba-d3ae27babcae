<template>
	<el-button size="small" text type="primary" @click="dialogVisible = true">查看作业轨迹</el-button>
	<WorkInfo v-model="dialogVisible" :rowData="rowData"></WorkInfo>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
interface Props {
	rowData?: any | null;
}
const props = withDefaults(defineProps<Props>(), {});
const emit = defineEmits<{
	(ev: 'update:value', value: string): void;
	(ev: 'change', seledObj: any): void;
}>();
const dialogVisible = ref(false);
</script>
<style lang="scss" scoped>
.t-job-path {
	height: 400px;
}
</style>
