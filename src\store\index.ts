// vue3中可以使用 ref/reactive对象代替vuex
import { reactive, readonly, ref } from 'vue';
import type { App } from 'vue';
import { createPinia } from 'pinia';
import { RouteRecordRaw } from 'vue-router';

const store = createPinia();
export function setupStore(app: App<Element>) {
	app.use(store);
}
export { store };

let userInfo_ = {} as UserInfo;
const storeUserInfo_ = reactive(userInfo_);
export const storeUserInfo = readonly(storeUserInfo_);
export function setStoreUserInfo(val: Partial<UserInfo>) {
	Object.assign(storeUserInfo_, val);
}

const storeMenus_ = ref<RouteRecordRaw[]>([]);
export const storeMenus = readonly(storeMenus_);
export function setStoreMenus(val: RouteRecordRaw[]) {
	storeMenus_.value = val || [];
}

// 相关权限
const storeAuthorities_ = ref<string[]>([]);
export const storeAuthorities = readonly(storeAuthorities_);
export function setStoreAuthorities(val: string[]) {
	storeAuthorities_.value = val || [];
}

interface StoreCommon {
	refresh: number; // 0; // 随机数，每次登录时都会变化，一些组件通过监听这个属性清缓存或者刷新
	isLogined: boolean; // 是否已经登录
}
const storeCommon_ = reactive<StoreCommon>({
	refresh: 0, // 随机数，每次登录时都会变化，一些组件通过监听这个属性清缓存或者刷新
	isLogined: false,
});
export const storeCommon = readonly(storeCommon_);
export function setStoreCommon(obj: Partial<StoreCommon>) {
	Object.assign(storeCommon_, obj);
}

export const innerH = ref(window.innerHeight);
export const innerW = ref(window.innerWidth);
export const resizeChange = ref(0);
let timer: any;
window.addEventListener('resize', () => {
	if (timer) clearTimeout(timer);
	timer = setTimeout(reSize, 150);
});

export function reSize() {
	document.documentElement.style.setProperty('--root-vw100', `${window.innerWidth}px`);
	document.documentElement.style.setProperty('--root-vh100', `${window.innerHeight}px`);
	innerH.value = window.innerHeight;
	innerW.value = window.innerWidth;
	resizeChange.value++;
}
