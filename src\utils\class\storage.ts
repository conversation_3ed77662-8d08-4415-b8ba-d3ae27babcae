// 默认缓存期限
type TypeTime = number | null;
export interface IStorageProps {
	prefixKey: string; // 存储时的前缀
	cacheTime: TypeTime; // 默认过期时间
}
export class IStorage<TypeEnumKey> {
	private prefixKey: string;
	private cacheTime: TypeTime;

	constructor(props: IStorageProps) {
		this.prefixKey = props.prefixKey;
		this.cacheTime = props.cacheTime;
	}
	private getKey(key: TypeEnumKey) {
		return `${this.prefixKey}${key}`.toUpperCase();
	}
	// type TypeStorageKey = keyof typeof EnumStorage;
	// 设置缓存
	set(key: TypeEnumKey, value: any, expire?: number | null) {
		expire = expire || this.cacheTime;
		const stringData = JSON.stringify({
			value,
			expire: expire !== null ? new Date().getTime() + expire * 1000 : null,
		});
		localStorage.setItem(this.getKey(key), stringData);
	}

	// 读取缓存 如果过期的话，返回null，不存在的话，返回 undefined
	get<T = Array<any> | string | number | Object>(key: TypeEnumKey, def?: any): T | null | undefined {
		const item = localStorage.getItem(this.getKey(key));
		if (item) {
			try {
				const data = JSON.parse(item);
				const { value, expire } = data || {};
				if (expire === undefined || value === undefined) {
					//不存在 返回 undefined
					this.remove(key);
					return undefined;
				} else if (expire >= Date.now()) {
					// 在有效期内 直接返回
					return value;
				} else if (expire >= Date.now()) {
					//不在有效期，返回null
					this.remove(key);
					return null;
				}
			} catch (err) {
				console.log(err);
				this.remove(key);
				return def;
			}
		}
		return def;
	}
	/**
	 * 从缓存删除某项
	 * @param {string} key key: TypeEnumKey,
	 */
	remove(key: TypeEnumKey) {
		localStorage.removeItem(this.getKey(key));
	}

	/**
	 * 清空所有缓存
	 * @memberOf Cache
	 */
	clear(): void {
		localStorage.clear();
	}
}
