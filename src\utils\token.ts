import { isDev, tokenTimeout } from '@/config';
import { setStoreCommon } from '@/store';
import { refreshMenus,goToLogout } from '@/api/user';
// token方法没有使用 storage
enum EnumLogin {
	toKenKey = 'EnumLogin_OILKAJVOIASHEUYNVLKASHD', //  localStorage 中 token的key
	isLogIn = 'EnumLogin_GLAKSFBVUIY34329SAKJ', // 是否登录
	newestTime = 'EnumLogin_LKJ9G828LKJ2L35J2LJ9S8', // 更新活动时间
	true = 'EnumLogin_7345gksd095sgt', // 表示true
	false = 'EnumLogin_734hsi987s4523', // 表示false
}
function getAppToken() {
	// 获取最新token 暂未开发；
	return Promise.resolve('5AuCxPREFv');
}

/**
 * 获取token值
 */
export function getTokenPromise(): Promise<string> {
	let token = getLocalToken();
	if (token) {
		return Promise.resolve(token);
	} else {
		return Promise.reject('token过期');
		return getAppToken().then((token) => {
			setTokenLocal(token);
			return token;
		});
	}
}

let timeStr = new Date().getTime();
let token_ = '';

/**
 * 获取token值
 */
export function getLocalToken(): string | null {
let token: string = token_;
	if (token) {
		return token;
	} else {
		setTokenLocal();
		return null;
	}
}

/**
 * 设置token的值
 */
export function setTokenLocal(token?: string) {
	setStoreCommon({
		isLogined: !!token,
	});
	if (token) {
		token_ = token;
		refreshToken();
	} else {
		token_ = '';
	}
}
// 刷新token
export function refreshToken() {
  setTimeout(() => {
    refreshMenus()
  }, 30 * 60 * 1000);
}

let timer
// 长时间无操作退出登录
export function addLogoutLongTime() {
  timer&&clearTimeout(timer);
  timer = setTimeout(() => {
    goToLogout()
  }, 30 * 60 * 1000);
}
