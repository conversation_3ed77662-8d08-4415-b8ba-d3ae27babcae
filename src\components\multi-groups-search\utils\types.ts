//每个搜索条件列表的单个数据
export interface ListItem {
    type?: string;
    name: string;
    code: string;
    uuid?: string;
}
//选择后的搜索条件的单个数据
export interface SelectListItem {
    type: string;
    label: string;
    name: string;
    code: string;
    uuid?: string;
    isMustSelect?: boolean;//单行是否必须选中一个
}
// 搜索组件数据中单个数据格式，showAll和isMustSelect互斥
export interface CategoryListItem {
    label: string;//单行搜索名称
    type: string;//单行搜索类型
    list: ListItem[];//单行搜索的数据
    showAll?: boolean;//是否展示不限按钮
    isMustSelect?: boolean;//单行是否必须选中一个
}
export interface MoreListItem {
    type: string;//单行搜索类型
    showMoreBtn: boolean;//是否展示更多按钮
    isFold: boolean;//是否折叠
}

// 搜索组件单行接口返回值格式
export type CategoryListResponse = ApiResponseData<ListItem[]>