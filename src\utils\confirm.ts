import { ElMessageBox } from 'element-plus';

// 操作确认
export const operateConfirm = (title: string, confirmButtonText: string = '确定', cancelButtonText: string = '取消') => {
	return new Promise((resolve, reject) => {
		ElMessageBox.confirm(title, '提示', {
			confirmButtonText: confirmButtonText,
			cancelButtonText: cancelButtonText,
			customClass: 'xirui-operate-confirm',
			closeOnClickModal: false,
		})
			.then(() => {
				resolve(true);
			})
			.catch(() => {
				reject(false);
			});
	});
};

// 删除确认
export const deleteConfirm = (title: string, confirmButtonText: string = '确定', cancelButtonText: string = '取消') => {
	return new Promise((resolve, reject) => {
		ElMessageBox.confirm(title, '提示', {
			confirmButtonText: confirmButtonText,
			cancelButtonText: cancelButtonText,
			customClass: 'xirui-operate-confirm xirui-delete-confirm',
			closeOnClickModal: false,
		})
			.then(() => {
				resolve(true);
			})
			.catch(() => {
				reject(false);
			});
	});
};

// 成功提示
export const successMsgTip = (title: string = '操作成功！', duration: number = 3000) => {
	ElMessage({
		message: title,
		type: 'success',
		customClass: 'xirui-common-tip xirui-success-tip',
		duration: duration,
	});
};

// 失败提示
export const errorMsgTip = (title: string = '操作失败！', duration: number = 3000) => {
	ElMessage({
		message: title,
		type: 'error',
		customClass: 'xirui-common-tip xirui-error-tip',
		duration: duration,
	});
};
