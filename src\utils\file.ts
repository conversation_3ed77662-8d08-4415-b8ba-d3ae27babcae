import type { UploadRawFile } from 'element-plus';
// nginx配置最大载荷
const NGINX_FILESIZE_LIMIT = 100 * 1024 * 1024;
/**
 * 接收后台接口文件流下载
 * @param data 文件流
 * @param fileName 文件名称
 * @param mime 媒体类型
 * @param bom
 */
export const downloadByData = (data: BlobPart, fileName: string, mime?: string, bom?: BlobPart) => {
	const blobData = typeof bom !== 'undefined' ? [bom, data] : [data];
	const blob = new Blob(blobData, { type: mime || 'application/octet-stream' });
	const blobURL = window.URL.createObjectURL(blob);
	downloadByUrl(blobURL, fileName);
};

/**
 * 通过a标签下载链接文件
 * @param url
 * @param fileName
 */
export const downloadByUrl = (url: string, fileName: string) => {
	const elink = document.createElement('a');
	elink.style.display = 'none';
	elink.href = url;
	elink.download = fileName;
	if (typeof elink.download === 'undefined') {
		elink.setAttribute('target', '_blank');
	}
	document.body.appendChild(elink);
	elink.click();
	document.body.removeChild(elink);
	window.URL.revokeObjectURL(elink.href);
};

/**
 * canvas下载图片
 * @param url
 * @param fileName
 * @param fileType
 */
export const downloadImage = (url: string, fileName: string, fileType: string) => {
	const image = new Image();
	image.src = url;
	image.setAttribute('crossOrigin', 'anonymous');
	image.onload = () => {
		const canvas = document.createElement('canvas');
		canvas.width = image.width;
		canvas.height = image.height;
		canvas.getContext('2d')?.drawImage(image, 0, 0, image.width, image.height);
		const a = document.createElement('a');
		a.download = fileName;
		a.href = canvas.toDataURL(fileType);
		a.click();
	};
	image.onerror = () => {
		ElMessage.error('下载失败！');
	};
};

/**
 * 解析文件大小匹配对应单位
 * @param size
 * @returns
 */
export const parseFileSize = (size: number) => {
	if (!size) {
		return;
	}
	if (size < 1024) {
		return size + 'B';
	} else if (size < 1048576) {
		return (size / 1024).toFixed(2) + 'KB';
	} else if (size < 1073741824) {
		return (size / 1024 / 1024).toFixed(2) + 'MB';
	} else {
		return (size / 1024 / 1024 / 1024).toFixed(2) + 'GB';
	}
};

/**
 * 根据mime对文件类型归类
 * @param mime
 * @returns
 */
export const judgeFileType = (mime: string) => {
	if (mime.startsWith('image/')) {
		return 'image';
	} else if (mime.startsWith('video/')) {
		return 'video';
	} else if (mime.startsWith('application/')) {
		return 'application';
	} else {
		return 'other';
	}
};

/**
 * 分片文件上传参数处理
 * @param file 待上传文件
 * @param fileUuid 文件uuid
 * @param max 分片大小
 * @param start 当前分片[从0开始]
 * @returns
 */
export const formatPartParams = (file: UploadRawFile, fileUuid: string, max: number = NGINX_FILESIZE_LIMIT, start: number = 0) => {
	const { size, name, uid } = file;
	const pieces = Math.ceil(size / max);
	return {
		chunk: start,
		chunks: pieces,
		fileName: name,
		fileUuid,
	} as ChunkParam;
};
