// 样式在index.vue中
<template>
	<div class="table-input-filter">
		<div class="describe-text">添加筛选条件</div>
		<div class="title-text">{{ titleText }}</div>
		<el-select v-if="showSelect" :teleported="false" v-model="selectValue" placeholder="请选择" class="search-type-select">
			<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
		</el-select>
		<el-input v-model="inputValue" class="n-t-input-box" clearable :maxlength="maxLength" :placeholder="placeholder" @keyup.enter.native="handleClick">
			<template #[slotName]>
				<el-icon class="t-input-icon" title="搜索"><Search /></el-icon>
			</template>
		</el-input>
		<div class="btn-box">
			<el-button @click="handleClick" class="submit-btn" type="primary" size="small">确定</el-button>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { Search } from '@element-plus/icons-vue';
interface Props {
	value: string;
	titleText: string;
	searchRule?: string | boolean; // 查询匹配规则。父组件传入，根据此规则，生成查询匹配规则
	rule?: string | boolean; // 匹配规则下拉框内容
	placeholder?: string;
	maxLength?: number;
	slotName?: 'prefix' | 'suffix';
}

const props = withDefaults(defineProps<Props>(), {
	maxLength: 200,
	slotName: 'suffix',
	placeholder: '关键字搜索',
	titleText: '',
	searchRule: 'contain',
	rule: '',
});
const emit = defineEmits<{
	(ev: 'update:value', value: string): void;
	(ev: 'update:rule', rule: string): void;
	(ev: 'search'): void;
}>();

const inputValue = ref(''); // 输入框内容
const selectValue = ref(''); // 下拉框，搜索规则
const showSelect = ref(false); // 是否展示下拉框
const options = [
	{
		value: 'contain',
		label: '包含',
	},
	{
		value: 'noContain',
		label: '不包含',
	},
	{
		value: 'equal',
		label: '等于',
	},
	{
		value: 'noEqual',
		label: '不等于',
	},
];

watch(
	() => props.value,
	(val) => {
		inputValue.value = val;
	},
);
watch(
	() => props.searchRule,
	(val) => {
		// 为true，展示查询规则下拉框
		if (val === true) {
			showSelect.value = true;
			selectValue.value = 'contain';
		} else if (val === false) {
			// 为false，不展示下拉框，默认为包含规则
			showSelect.value = false;
			selectValue.value = 'contain';
		} else {
			// 其他枚举字段，直接赋值即可
			showSelect.value = false;
			selectValue.value = val ? val : 'contain';
		}
	},
	{
		immediate: true,
	},
);

// 点击确认按钮
function handleClick() {
	emit('update:value', inputValue.value);
	emit('update:rule', selectValue.value);
	emit('search');
}
</script>
<style lang="scss" scoped>
.n-t-input-box {
	flex-shrink: 0;
	width: auto;
}
.t-input-icon {
	order: 1;
	cursor: pointer;
}

.table-input-filter {
	padding: 0 12px 12px 12px;
	.describe-text {
		font-family: MicrosoftYaHei;
		font-size: 12px;
		color: #86909c;
		line-height: 20px;
		height: 34px;
		padding: 10px 0 4px 0;
		box-sizing: border-box;
	}

	.title-text {
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		font-size: 14px;
		color: #1d2129;
		line-height: 22px;
		margin-bottom: 6px;
	}

	.search-type-select {
		margin-bottom: 12px;
	}

	.btn-box {
		text-align: right;

		.submit-btn {
			margin-top: 12px;
			height: 28px;
		}
	}
}
</style>
