import ImportResult from './import-result.vue';
import Preview from './preview.vue';
import BatchImport from './batch-import.vue';
import type { ImageViewerProps } from 'element-plus';

declare global {
	type PreviewInstance = InstanceType<typeof Preview>;
	type BatchImport = InstanceType<typeof BatchImport>;
	type ImportResult = InstanceType<typeof ImportResult>;
	type FileResponse = ApiResponseData<FileItem[]>;
	type ImportResultResponse = ApiResponseData<Result[]>;
	interface Result {
		failedResultList: Info[] | [];
		totalCount: number;
		failedCount: number;
		successCount?: number;
	}
	interface Info {
		errorMessage: string; //失败原因
		[key: string]: string | number; // 其他需显示字段
	}
	interface FileItem {
		fileUuid: string;
		fileName: string;
		fileType: string;
		fileAccessPath: string;
		fileStoragePath?: string;
		fileCreateTime?: string;
		fileSize?: number;
		uid?: number;
	}
	// 分片上传接口参数定义
	interface ChunkParam {
		chunk: number;
		chunks: number;
		fileName: string;
		fileUuid: string;
		[key: string]: any;
	}
	interface PreviewProps extends /* @vue-ignore */ ImageViewerProps {
		files?: FileItem[]; //初始文件列表渲染
	}
}
