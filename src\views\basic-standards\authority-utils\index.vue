<template>
	<div>
		<el-button @click="judgeHasAuth('MODULE_GOV_PROVINCE_USER')">单个权限判定</el-button>
		<el-button @click="judgeHasAuth(['MODULE_GOV_PROVINCE_USER', 'MODULE_SUBSIDY'])">数据权限判定</el-button>
	</div>
</template>

<script lang="tsx" setup>
import { getAuth } from '@/utils';
const judgeHasAuth = (auth: string | string[]) => {
	let hasAuth = getAuth(auth);
	console.log(hasAuth);
};
</script>
<style lang="scss" scoped></style>
