<template>
	<!-- <el-popover placement="right" trigger="click" width="286" popper-class="custom-config-center-popover">
		<template #reference>
			<li class="el-menu-item">
				<img :src="imgUrl" />
				<span class="t-text" title="系统配置">系统配置</span>
			</li>
		</template>
		<div class="config-center-box">
			<div class="config-center-main">
				<div @click="handleClick(item.type)" class="menu-cell" v-for="(item, index) in menuList" :key="index" c>
					{{ item.name }}
				</div>
			</div>
		</div>
	</el-popover> -->
	<li class="el-menu-item">
		<img :src="imgUrl" />
		<span class="t-text" title="系统配置">系统配置</span>
	</li>
</template>
<script lang="tsx" setup>
import { getSystemTheme, setSystemTheme } from '@/config';
import type { Action } from 'element-plus';
import { ref, watch } from 'vue';
import screenfull from 'screenfull';
const props = defineProps({
	systemTheme: {
		type: String,
	},
});
var imgUrl = ref('');
const menuList = [
	{
		name: '切换主题',
		type: 'switchTheme',
	},
	{
		name: '切换全屏',
		type: 'switchFullScreen',
	},
];
// 设置菜单图标
watch(
	() => props.systemTheme,
	(val) => {
		imgUrl.value = new URL(`/src/assets/images/menu/${val}-system-config.png`, import.meta.url).href;
	},
	{
		immediate: true,
	},
);

function handleClick(type) {
	switch (type) {
		case 'switchTheme':
			let theme = getSystemTheme();
			let themeText = theme == 'primary' ? '白色' : '蓝色';
			ElMessageBox.alert('是否切换为' + themeText + '主题？', '提示', {
				showCancelButton: true,
				cancelButtonText: '取消',
				confirmButtonText: '确定',
				callback: (action: Action) => {
					if (action == 'confirm') {
						setSystemTheme(theme == 'primary' ? 'default' : 'primary');
						window.location.reload();
					}
				},
			});
			break;
		case 'switchFullScreen':
			// 获取当前全屏状态，为后续状态使用
			// screenfull.isFullscreen;
			if (screenfull.isEnabled) {
				screenfull.toggle();
			}
			break;
	}
}
</script>
<style lang="scss" scoped>
.config-center-box {
	.config-center-main {
		.menu-cell {
			height: 40px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 14px;
			color: #1d2129;
			line-height: 22px;
			padding: 10px 16px 8px 16px;
			box-sizing: border-box;
			cursor: pointer;
		}

		.menu-cell:hover {
			background: #f2f3f5;
		}
	}
}
</style>
<style lang="scss">
.custom-config-center-popover {
	padding: 0 !important;
}
</style>
