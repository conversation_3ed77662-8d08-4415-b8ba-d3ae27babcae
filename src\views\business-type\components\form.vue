<template>
	<header-main
		:viewTitle="titleText"
		showCloseBtn
		@close="handleClickCloseBtn"
	>
		<template #main>
			<div class="form-container">
				<el-form
					ref="formRef"
					:model="formData"
					:rules="formRules"
					label-width="120px"
					label-position="left"
					class="business-type-form"
				>
					<el-row :gutter="20">
						<el-col :span="24">
							<el-form-item
								label="类别编码"
								prop="cNumber"
							>
								<el-input
									v-model="formData.cNumber"
									placeholder="请输入类别编码"
									clearable
									:disabled="props.type === 'edit'"
								/>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row :gutter="20">
						<el-col :span="24">
							<el-form-item
								label="类别名称"
								prop="cName"
							>
								<el-input
									v-model="formData.cName"
									placeholder="请输入类别名称"
									clearable
								/>
							</el-form-item>
						</el-col>
					</el-row>

					<el-form-item
						label="业务说明"
						prop="bDescription"
					>
						<el-input
							v-model="formData.bDescription"
							type="textarea"
							:rows="4"
							placeholder="请输入业务说明"
							maxlength="500"
							show-word-limit
							resize="none"
						/>
					</el-form-item>

					<el-row :gutter="20">
						<el-col :span="24">
							<el-form-item
								label="表单"
								prop="bFormFlag"
							>
								<el-select
									v-model="formData.bFormFlag"
									placeholder="请选择表单"
									clearable
									filterable
									@change="handleFormChange"
								>
									<el-option
										v-for="item in formOptions"
										:key="item.value"
										:label="item.label"
										:value="item.value"
									/>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
					<el-row :gutter="20">
						<el-col :span="24">
							<el-form-item
								label="流程"
								prop="dFlowNumber"
							>
								<el-select
									v-model="formData.dFlowNumber"
									placeholder="请选择流程"
									clearable
									filterable
								>
									<el-option
										v-for="item in processOptions"
										:key="item.pdId"
										:label="item.pdName"
										:value="item.pdId"
									/>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>

					<el-row :gutter="20">
						<el-col :span="24">
							<el-form-item
								label="会签表标识"
								prop="signTableFlag"
							>
								<el-input
									:disabled="true"
									v-model="formData.signTableFlag"
									placeholder="请输入会签表标识"
									clearable
								/>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item
								label="排序编号"
								prop="sortOrder"
							>
								<el-input-number
									v-model="formData.sortOrder"
									:min="0"
									:max="9999"
									controls-position="right"
									placeholder="请输入排序编号"
									style="width: 100%"
								/>
							</el-form-item>
						</el-col>
					</el-row>

					<el-form-item
						label="备注"
						prop="remark"
					>
						<el-input
							v-model="formData.remark"
							type="textarea"
							:rows="4"
							placeholder="请输入备注信息"
							maxlength="500"
							show-word-limit
							resize="none"
						/>
					</el-form-item>
				</el-form>
			</div>
		</template>
		<template #footer>
			<el-button
				style="width: 96px"
				type="primary"
				:loading="submitLoading"
				@click="handleSubmit"
			>
				保存
			</el-button>
			<el-button
				@click="handleCancel"
				style="width: 96px"
				>取消</el-button
			>
		</template>
	</header-main>
</template>

<script lang="ts" setup>
import { computed, reactive, ref, onMounted } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { addApprovalCategoryApi, updateApprovalCategoryApi, getProcessOptionsApi } from '../api';

// 审批类别表单数据接口
interface ApprovalCategoryForm {
	bDescription: string;
	bFormFlag: string;
	cName: string;
	cNumber: string;
	dFlowName: string;
	dFlowNumber: string;
	isValid: number;
	managers: string;
	modelConfig: string;
	remark: string;
	signTableFlag: string;
	sortOrder: number;
	wscpBtConfig: string;
	type: string;
}

interface Props {
	title?: string;
	type: string;
	initData?: Partial<ApprovalCategoryForm>;
}

//关联表单选项list
const formOptions = ref([
	{ label: '合同审批', value: '21' },
	{ label: '证照使用审批单', value: '24' },
	{ label: '项目招标采购需求审批表', value: '25' },
	{ label: '出差申请单', value: '31' },
	{ label: '出差费用报销单', value: '32' },
]);

//关联流程选项列
const processOptions = ref<any[]>([]);

const getProcessOptions = async () => {
	getProcessOptionsApi().then((res) => {
		processOptions.value = res[0].records;
	});
};
const props = withDefaults(defineProps<Props>(), {
	initData: () => ({}),
});

const emit = defineEmits<{
	(ev: 'back'): void;
	(ev: 'success'): void;
}>();

// 表单引用
const formRef = ref<FormInstance>();

// 加载状态
const submitLoading = ref(false);

// 表单数据
const formData = reactive<ApprovalCategoryForm>({
	bDescription: '',
	bFormFlag: '',
	cName: '',
	cNumber: '',
	dFlowName: '',
	dFlowNumber: '',
	isValid: 1,
	managers: '',
	modelConfig: '',
	remark: '',
	signTableFlag: '',
	sortOrder: 0,
	wscpBtConfig: '',
	type: 'generic_approval',
});

// 表单验证规则
const formRules: FormRules<ApprovalCategoryForm> = {
	cNumber: [
		{ required: true, message: '请输入类别编码', trigger: 'blur' },
		{ min: 1, max: 50, message: '类别编码长度应在1-50个字符之间', trigger: 'blur' },
	],
	cName: [
		{ required: true, message: '请输入类别名称', trigger: 'blur' },
		{ min: 1, max: 100, message: '类别名称长度应在1-100个字符之间', trigger: 'blur' },
	],
	bFormFlag: [{ required: true, message: '请选择表单', trigger: 'change' }],
	dFlowNumber: [{ required: true, message: '请选择流程', trigger: 'change' }],
	signTableFlag: [{ required: true, message: '请输入会签表标识', trigger: 'blur' }],
	sortOrder: [
		{ required: true, message: '请输入排序编号', trigger: 'blur' },
		{ type: 'number', min: 0, max: 9999, message: '排序编号应在0-9999之间', trigger: 'blur' },
	],
	remark: [{ max: 500, message: '备注不能超过500个字符', trigger: 'blur' }],
};

// 计算属性
const titleText = computed(() => {
	return props.type === 'edit' ? '修改业务类型' : '增加业务类型';
});

// 初始化表单数据
const initFormData = () => {
	if (props.initData && Object.keys(props.initData).length > 0) {
		Object.assign(formData, props.initData);
	}
};

// 重置表单
const resetForm = () => {
	formRef.value?.resetFields();
	Object.assign(formData, {
		bDescription: '',
		bFormFlag: '',
		cName: '',
		cNumber: '',
		dFlowName: '',
		dFlowNumber: '',
		isValid: 1,
		managers: '',
		modelConfig: '',
		remark: '',
		signTableFlag: '',
		sortOrder: 0,
		wscpBtConfig: '',
		type: 'generic_approval',
	});
};

const handleFormChange = (val: any) => {
	console.log(val);
	formData.signTableFlag = val;
};

// 提交表单
const handleSubmit = async () => {
	if (!formRef.value) return;

	try {
		await formRef.value.validate();
		submitLoading.value = true;
		let response: any;
		if (props.type === 'edit') {
			// 编辑时使用更新接口
			response = await updateApprovalCategoryApi(formData);
		} else {
			formData.dFlowName = processOptions.value.find((item) => item.pdId === formData.dFlowNumber)?.pdName;
			// 新增时使用添加接口
			response = await addApprovalCategoryApi(formData);
		}

		if (response.code === 0 || response.success) {
			ElMessage.success(props.type === 'edit' ? '修改成功' : '新增成功');
			emit('success');
			handleCancel();
		} else {
			throw new Error(response.message || '操作失败');
		}
	} finally {
		submitLoading.value = false;
	}
};
//后端获取关联流程选项列

// 取消操作
const handleCancel = () => {
	resetForm();
	emit('back');
};

// 关闭按钮点击
const handleClickCloseBtn = () => {
	handleCancel();
};

// 组件挂载时初始化
onMounted(() => {
	getProcessOptions();
	initFormData();
});
</script>

<style scoped lang="scss">
.form-container {
	padding: 20px;
	background: #fff;
	border-radius: 8px;

	.business-type-form {
		max-width: 800px;
		margin: 0 auto;
	}

	.form-footer {
		display: flex;
		justify-content: center;
		gap: 16px;
		margin-top: 40px;
		padding-top: 20px;
		border-top: 1px solid #e4e7ed;
	}
}

:deep(.el-form-item__label) {
	font-weight: 500;
	color: #303133;
}

:deep(.el-input-number) {
	width: 100%;
}

:deep(.el-textarea__inner) {
	resize: none;
}
</style>
