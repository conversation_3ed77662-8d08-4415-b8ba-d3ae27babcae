<template>
	<div class="header-main">
		<div class="header">
			<div class="header-left">
				<custom-icon-button
					v-if="showCloseBtn"
					class="close-icon"
					type="icon-arrow-go-back-line"
					@click="handleClickCloseBtn"
				>
				</custom-icon-button>
				<span class="title-text">{{ titleText }}</span>
				<slot name="header-left"></slot>
			</div>
			<div class="header-right">
				<slot name="header-right"></slot>
			</div>
		</div>
		<div class="main">
			<slot name="main"></slot>
		</div>
	</div>
</template>
<script setup lang="ts">
import { computed } from 'vue';
import { getTitle } from '@/config';

interface Props {
	viewTitle?: string; // 页面标题
	showCloseBtn?: boolean; // 显示关闭按钮，配套列表页面使用
}
const props = withDefaults(defineProps<Props>(), {
	viewTitle: '',
	showCloseBtn: true,
});
const emit = defineEmits<{
	(ev: 'close'): void;
}>();

// 标题文本，不传入默认采用左侧菜单栏
const titleText = computed(() => {
	return props.viewTitle ? props.viewTitle : getTitle();
});

// 点击关闭按钮
function handleClickCloseBtn() {
	emit('close');
}
</script>
<style lang="scss" scoped>
.header-main {
	height: 100%;
	.header {
		height: 48px;
		padding: 0 20px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid #e5e6eb;
		box-sizing: border-box;
		.header-left {
			display: flex;
			align-items: center;
			.close-icon {
				margin-right: 10px;
			}

			.title-text {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 18px;
				color: #1d2129;
				line-height: 22px;
				margin-right: 10px;
			}
		}
		.header-right {
			display: flex;
			align-items: center;
		}
	}

	.main {
		height: calc(100% - 48px);
		padding: 0;
		display: flex;
		flex-direction: column;
	}
}
</style>
