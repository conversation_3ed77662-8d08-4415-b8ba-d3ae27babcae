<template>
	<div
		ref="multiGroupsRef"
		class="multi-category-search-box"
		:class="{ 'border-bottom': showTotal || selectResult.length > 0 }"
	>
		<div class="category-search-area">
			<div
				class="category-row-box"
				v-for="(categoryItem, index) in categoryList_new"
				:class="{ 'heigth-0': index > 0 && categoryBoxIsFold }"
				:key="categoryItem.type"
			>
				<div
					class="category-row-label"
					:style="{ width: labelWidth + 'px' }"
				>
					{{ categoryItem.label }}
				</div>
				<div
					class="category-row-list"
					:class="{
						'category-row-list-close': showMoreList[index].isFold,
						'category-row-list-open': !showMoreList[index].isFold,
					}"
					:ref="(el) => setCategoryRowRef(el as HTMLElement, categoryItem.type)"
				>
					<div class="category-row-list-item-box">
						<div
							v-for="listItem in categoryItem.list"
							:key="listItem.code"
							class="category-row-list-item"
							:class="{ 'is-active': selectList_new[index].code === listItem.code }"
							@click="listItemClick(listItem, categoryItem.type, index)"
						>
							{{ listItem.name }}
						</div>
						<div
							v-if="categoryItem.list.length === 0"
							class="category-row-list-noData"
						>
							<el-icon><Warning /></el-icon>
							未找到符合条件的选项
						</div>
					</div>
				</div>
				<div
					v-if="showMoreList[index].showMoreBtn"
					class="category-row-more"
					@click="showMoreItem(index)"
				>
					{{ showMoreList[index].isFold ? '更多' : '收起' }}
				</div>
			</div>
			<div
				v-if="categoryList_new.length > 1"
				class="category-search-box-more"
			>
				<div
					class="isFold-btn-box"
					@click="isFoldCategoryBox"
				>
					<div class="isFold-text">{{ categoryBoxIsFold ? '展开' : '收起' }}</div>
					<el-icon><ArrowDown v-if="categoryBoxIsFold" /><ArrowUp v-if="!categoryBoxIsFold" /></el-icon>
				</div>
			</div>
		</div>
		<div
			class="search-result-area"
			:class="{ 'padding-0': !showTotal && selectResult.length === 0 }"
		>
			<div
				class="search-result-text"
				v-if="showTotal"
			>
				<span>共</span>
				<span class="m-l-3">{{ total }}</span>
				<span class="m-l-3">条符合条件的结果</span>
				<span v-if="selectResult.length > 0">：</span>
			</div>
			<div
				class="search-result-text"
				v-if="!showTotal && selectResult.length > 0"
			>
				<span>已选：</span>
			</div>
			<div
				v-if="selectResult.length > 0"
				class="select-result-list"
			>
				<div
					class="result-list-item"
					v-for="(item, index) in selectResult"
					:key="item.code"
				>
					<span>{{ item.label }}</span>
					<span class="m-l-3">包含</span>
					<span>“{{ item.name }}”</span>
					<el-icon
						v-if="!item.isMustSelect"
						class="m-l-3"
						@click="clearSelectItem(item)"
						><Close
					/></el-icon>
				</div>
				<div
					v-if="isMustSelectList.length > 0"
					class="clear-all-btn"
					@click="clearSelectAll"
				>
					清空条件
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="tsx">
import { debounce } from 'lodash-es';
import { ref, computed, nextTick, watch, onMounted, onUnmounted } from 'vue';
import type * as CategoryTypes from './utils/types';
interface Props {
	labelWidth?: string | number;
	categoryList: CategoryTypes.CategoryListItem[];
	selectList: CategoryTypes.SelectListItem[];
	showTotal?: boolean; //是否展示搜索结果条数
	total?: string | number; //搜索结果条数
	height?: string | number; //容器高度
}
interface CategoryRowRefItem {
	type: string;
	element: HTMLElement;
}
const props = withDefaults(defineProps<Props>(), {
	labelWidth: '45',
	categoryList: () => [],
	selectList: () => [],
	showTotal: true,
	total: 0,
	height: 136,
});
const multiGroupsRef = ref<HTMLElement>();
const selectList_new = ref<CategoryTypes.SelectListItem[]>([]);
const isMustSelectList = ref<CategoryTypes.SelectListItem[]>([]); //是否展示清空按钮
const categoryBoxIsFold = ref<boolean>(true);
const categoryRowRef = ref<CategoryRowRefItem[]>([]);
const showMoreList = ref<CategoryTypes.MoreListItem[]>([]);
type EmitProps = {
	(e: 'update:height', val: number): void; //容器高德
	(e: 'update:categoryList', val: CategoryTypes.CategoryListItem[]): void; //更新多组筛选数据
	(e: 'update:selectList', val: CategoryTypes.SelectListItem[]): void; //更新多组筛选选中的数据
	(e: 'select', selectItem: CategoryTypes.SelectListItem, selectList: CategoryTypes.SelectListItem[]): void; //多组筛选切换事件
	(e: 'clear', clearItem: CategoryTypes.SelectListItem | null, selectList: CategoryTypes.SelectListItem[]): void; //多组筛选条件清除事件
};
const emits = defineEmits<EmitProps>();
const debouncedHandleResize = debounce(computedRowHeight, 300);
onMounted(() => {
	window.addEventListener('resize', debouncedHandleResize);
});
onUnmounted(() => {
	window.removeEventListener('resize', debouncedHandleResize);
});
const categoryList_new = computed({
	get() {
		const newCategoryList = JSON.parse(JSON.stringify(props.categoryList));
		newCategoryList.forEach((item: CategoryTypes.CategoryListItem) => {
			//统一给每一项增加”不限“
			if (item.showAll) {
				item.list = [{ name: '不限', code: '' }, ...item.list];
			} else {
				item.list = [...item.list];
			}
			if (showMoreList.value.length !== newCategoryList.length) {
				showMoreList.value.push({ type: item.type, showMoreBtn: false, isFold: true });
			}
		});
		computedRowHeight();
		return newCategoryList;
	},
	set(val) {
		emits('update:categoryList', val);
	},
});
watch(
	() => props.selectList,
	(val) => {
		selectList_new.value = JSON.parse(JSON.stringify(val));
		nextTick(() => {
			updateheight();
		});
	},
	{ immediate: true, deep: true },
);
//获取多组筛选每个实例
const setCategoryRowRef = (el: HTMLElement, type: string) => {
	const hasType = categoryRowRef.value.find((item: CategoryRowRefItem) => item.type === type);
	!hasType && categoryRowRef.value.push({ type, element: el });
};
//多组筛选选中的结果
const selectResult = computed(() => {
	const newCategoryList = JSON.parse(JSON.stringify(props.categoryList));
	const newSelectList = JSON.parse(JSON.stringify(selectList_new.value));
	newSelectList.forEach((item: CategoryTypes.SelectListItem) => {
		const findItem = newCategoryList.find((listItem: CategoryTypes.CategoryListItem) => listItem.type === item.type);
		item.isMustSelect = findItem.isMustSelect; //同步主数据中单行是否必须选中
	});
	const resultList = newSelectList.filter((item: CategoryTypes.SelectListItem) => item.code !== '');
	//筛选出选择的结果中没有设置必须选中的行，用于判断是否展示清除按钮
	isMustSelectList.value = resultList.filter((item: CategoryTypes.SelectListItem) => !item.isMustSelect);
	return resultList;
});
// 计算每组筛选是否是多行
function computedRowHeight() {
	nextTick(() => {
		let itemRowBoxHeight = document.getElementsByClassName('category-row-list-item')[0]?.getBoundingClientRect().height + 2; //2为向上的margin
		categoryRowRef.value.forEach((item: CategoryRowRefItem, index: number) => {
			const moreListIndex = showMoreList.value.findIndex((moreItem: CategoryTypes.MoreListItem) => moreItem.type === item.type);
			const itemRowConHeight = item.element.getElementsByClassName('category-row-list-item-box')[0].getBoundingClientRect().height;
			if (itemRowConHeight > itemRowBoxHeight) {
				showMoreList.value[moreListIndex].showMoreBtn = true;
			} else {
				showMoreList.value[moreListIndex].showMoreBtn = false;
			}
		});

		updateheight();
	});
}
const updateheight = () => {
	const boxHeight = multiGroupsRef.value?.getBoundingClientRect().height as number;
	emits('update:height', Math.ceil(boxHeight));
};
// 多组筛选整体展开收起
const isFoldCategoryBox = () => {
	//收起时子项全部收起
	if (!categoryBoxIsFold.value) {
		showMoreList.value.forEach((item) => {
			item.isFold = true;
		});
	}
	categoryBoxIsFold.value = !categoryBoxIsFold.value;
	nextTick(() => {
		updateheight();
	});
};
// 单组筛选后的展开收起
const showMoreItem = (index: number) => {
	showMoreList.value[index].isFold = !showMoreList.value[index].isFold;
	nextTick(() => {
		updateheight();
	});
};
// 清除单个筛选
const clearSelectItem = (clearItem: CategoryTypes.SelectListItem) => {
	const newCategoryList = JSON.parse(JSON.stringify(props.categoryList));
	const newSelectList = JSON.parse(JSON.stringify(selectList_new.value));
	newSelectList.forEach((item: CategoryTypes.SelectListItem) => {
		const categoryListItem = newCategoryList.find((listItem: CategoryTypes.CategoryListItem) => item.type === listItem.type);
		if (item.type === clearItem.type) {
			if (categoryListItem.showAll) {
				item.name = '不限';
				item.code = '';
			} else {
				if (categoryListItem.isMustSelect) {
					item.name = categoryListItem.list[0].name || '';
					item.code = categoryListItem.list[0].code || '';
				} else {
					item.name = '';
					item.code = '';
				}
			}
		}
	});
	selectList_new.value = newSelectList;
	emits('update:selectList', newSelectList);
	emits('clear', clearItem, newSelectList);
};
// 清空筛选
const clearSelectAll = () => {
	const newCategoryList = JSON.parse(JSON.stringify(props.categoryList));
	const newSelectList = JSON.parse(JSON.stringify(selectList_new.value));
	newSelectList.forEach((item: CategoryTypes.SelectListItem) => {
		const categoryListItem = newCategoryList.find((listItem: CategoryTypes.CategoryListItem) => item.type === listItem.type);
		if (categoryListItem.showAll) {
			item.name = '不限';
			item.code = '';
		} else {
			if (categoryListItem.isMustSelect) {
				item.name = categoryListItem.list[0].name || '';
				item.code = categoryListItem.list[0].code || '';
			} else {
				item.name = '';
				item.code = '';
			}
		}
	});
	selectList_new.value = newSelectList;
	emits('update:selectList', newSelectList);
	emits('clear', null, newSelectList);
};
// 单组筛选项的点击事件
const listItemClick = (listItem: CategoryTypes.ListItem, type: string, index: number) => {
	const newSelectList = JSON.parse(JSON.stringify(selectList_new.value));
	if (newSelectList[index].code === listItem.code) {
		return;
	}
	newSelectList.forEach((item: CategoryTypes.SelectListItem) => {
		if (item.type === type) {
			item.name = listItem.name || '';
			item.code = listItem.code || '';
		}
	});
	selectList_new.value = newSelectList;
	emits('update:selectList', newSelectList);
	emits('select', newSelectList[index], newSelectList);
};
// 更新单组筛选的数据
const updateRowList = (data: CategoryTypes.CategoryListItem) => {
	nextTick(() => {
		const { type, list } = data;
		const newCategoryList = JSON.parse(JSON.stringify(props.categoryList));
		newCategoryList.forEach((item: CategoryTypes.CategoryListItem) => {
			if (item.type === type) {
				item.list = [...list];
			}
		});
		emits('update:categoryList', newCategoryList);
		const newSelectList = JSON.parse(JSON.stringify(selectList_new.value));
		//更加要更新的数据得type,找出对应Type选中的数据
		const filterSelectList = newSelectList.find((item: CategoryTypes.SelectListItem) => item.type === type);
		// 判断当前Type选中的code在更新的数据中存不存在
		const hasCode = list.find((item: CategoryTypes.ListItem) => item.code === filterSelectList.code);
		//更新的数据中不存在当前选中的code，进行重置
		if (!hasCode?.code) {
			newSelectList.forEach((item: CategoryTypes.SelectListItem) => {
				if (item.type === type) {
					item.name = (hasCode?.name as string) || '';
					item.code = (hasCode?.code as string) || '';
				}
			});
			selectList_new.value = newSelectList;
			emits('update:selectList', newSelectList);
		}
	});
};
// 更新多组筛选的所有数据
const updateCategoryList = (data: CategoryTypes.CategoryListItem[]) => {
	nextTick(() => {
		const newCategoryList = JSON.parse(JSON.stringify(props.categoryList));
		const newSelectList = JSON.parse(JSON.stringify(selectList_new.value));
		const hasCodeList: CategoryTypes.SelectListItem[] = [];
		data.forEach((item: CategoryTypes.CategoryListItem, index: number) => {
			const { label, type, list } = item;
			const categoryIndex = newCategoryList.findIndex((item: CategoryTypes.CategoryListItem) => item.type === type);
			if (categoryIndex !== -1) {
				newCategoryList[categoryIndex] = { label, type, list };
			}
			//更加要更新的数据得type,找出对应Type选中的数据
			const filterSelectList = newSelectList.find((item: CategoryTypes.SelectListItem) => item.type === type);
			// 判断当前Type选中的code在更新的数据中存不存在
			const hasCode = list.find((item: CategoryTypes.ListItem) => item.code === filterSelectList.code);
			//更新的数据中不存在当前选中的code，进行重置
			if (!hasCode?.code) {
				newSelectList.forEach((item: CategoryTypes.SelectListItem) => {
					if (item.type === type) {
						item.name = (hasCode?.name as string) || '';
						item.code = (hasCode?.code as string) || '';
						hasCodeList.push(item);
					}
				});
			} else {
				hasCodeList.push(filterSelectList);
			}
		});
		selectList_new.value = hasCodeList;
		emits('update:categoryList', newCategoryList);
		emits('update:selectList', hasCodeList);
	});
};
// 设置单组筛选选中
const setRowSelect = (data: CategoryTypes.SelectListItem) => {
	nextTick(() => {
		const { type, code } = data;
		const newCategoryList = JSON.parse(JSON.stringify(props.categoryList));
		const filterCategoryList = newCategoryList.find((item: CategoryTypes.CategoryListItem) => item.type === type) || { list: [] };
		const filterItemList = filterCategoryList.list;
		const hasCode = filterItemList.find((item: CategoryTypes.ListItem) => item.code === code); //设置的code在数据中存不存在
		const newSelectList = JSON.parse(JSON.stringify(selectList_new.value));
		newSelectList.forEach((item: CategoryTypes.SelectListItem) => {
			if (item.type === type) {
				item.name = (hasCode?.name as string) || '';
				item.code = (hasCode?.code as string) || '';
			}
		});
		selectList_new.value = newSelectList;
		emits('update:selectList', newSelectList);
	});
};
// 根据type获取单组选中数据
const getRowSelect = (type: string, resultFormat: 'object' | 'Array' = 'object') => {
	const newSelectList = JSON.parse(JSON.stringify(selectList_new.value));
	if (resultFormat === 'object') {
		return newSelectList.find((item: CategoryTypes.SelectListItem) => item.type === type);
	} else if (resultFormat === 'Array') {
		return newSelectList.filter((item: CategoryTypes.SelectListItem) => item.type === type);
	}
};
// 获取多组筛选选中的结果
const getSelectList = () => {
	return JSON.parse(JSON.stringify(selectList_new.value));
};
defineExpose({
	updateRowList,
	updateCategoryList,
	setRowSelect,
	getRowSelect,
	getSelectList,
});
</script>

<style lang="scss" scoped>
.multi-category-search-box {
	padding: 20px 20px 0;
	width: 100%;
	.category-search-area {
		width: 100%;
		background: #f7f8fa;
		border-radius: 4px;
		padding: 8px 16px 0;
		overflow: hidden;
		.category-row-box {
			display: flex;
			padding-bottom: 8px;
			box-sizing: border-box;
			overflow: hidden;
			.category-row-label {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #86909c;
				line-height: 28px;
			}
			.category-row-list {
				flex: 1;
				padding: 0 24px 0;
				overflow: hidden;
				.category-row-list-item-box {
					width: 100%;
					display: flex;
					flex-wrap: wrap;
				}
				.category-row-list-item {
					cursor: pointer;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #1d2129;
					line-height: 22px;
					padding: 2px 8px;
					margin: 2px 8px 0 0;
					box-sizing: border-box;
					&:hover {
						background: #e5e6eb;
						border-radius: 2px;
					}
				}
				.category-row-list-noData {
					display: flex;
					align-items: center;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #86909c;
					line-height: 22px;
					padding: 2px 8px;
					margin: 2px 8px 0 0;
					box-sizing: border-box;
					.el-icon {
						font-size: 14px;
						color: #86909c;
						margin-right: 3px;
					}
				}
				.category-row-list-item.is-active {
					background: #0075c2;
					color: #ffffff;
					border-radius: 2px;
					&:hover {
						background: #0075c2;
					}
				}
			}
			.category-row-list-close {
				height: 28px;
			}
			.category-row-list-open {
				height: auto;
			}
			.category-row-more {
				cursor: pointer;
				font-family: MicrosoftYaHei;
				font-size: 14px;
				color: #0075c2;
				line-height: 28px;
			}
		}
		.category-search-box-more {
			display: flex;
			align-items: center;
			justify-content: center;
			padding-bottom: 8px;
			.isFold-btn-box {
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			.isFold-text {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 12px;
				color: #0075c2;
				line-height: 20px;
				margin-right: 4px;
			}
			.el-icon {
				font-size: 16px;
				color: #0075c2;
			}
		}
		.heigth-0 {
			height: 0 !important;
			padding-bottom: 0;
		}
	}
	.padding-0 {
		padding: 0 !important;
	}
	.search-result-area {
		display: flex;
		padding: 8px 0 0 0;
		.search-result-text {
			font-family: MicrosoftYaHei;
			font-size: 14px;
			color: #1d2129;
			line-height: 28px;
		}
		.select-result-list {
			flex: 1;
			display: flex;
			flex-wrap: wrap;
			.result-list-item {
				display: flex;
				align-items: center;
				font-family: MicrosoftYaHei;
				font-size: 12px;
				color: #1d2129;
				line-height: 20px;
				padding: 4px 8px;
				border-radius: 3px;
				border: 1px solid #e5e6eb;
				margin: 0 8px 8px 0;
				.el-icon {
					cursor: pointer;
					font-size: 16px;
					color: #000000;
				}
			}
			.clear-all-btn {
				cursor: pointer;
				font-family: MicrosoftYaHei;
				font-size: 14px;
				color: #0075c2;
				line-height: 28px;
			}
		}
		.m-l-3 {
			margin-left: 3px;
		}
	}
}
.border-bottom {
	box-shadow: inset 0px -1px 0px 0px #e5e6eb;
}
</style>
