<template>
	<el-dialog
		v-model="dialogVisible"
		class="select-organization-member-dialog"
		:title="title"
		width="960"
		destroy-on-close
		draggable
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:before-close="handleClose"
	>
		<div class="select-dialog-box">
			<div class="header-search">
				<el-input
					v-model="filterText"
					class="filterInput"
					:placeholder="placeholder"
					:prefix-icon="Search"
					clearable
					@input="onInput"
					@clear="inputClear"
				/>
			</div>
			<div class="select-dialog-con">
				<div
					v-show="filterText"
					class="filter-left-box"
				>
					<div class="filter-result-box">
						<el-table
							v-if="selectType === 'organization'"
							ref="organizationTableRef"
							class="custom-filter-table"
							:data="filterOrganizationList"
							row-key="uuid"
							height="390"
							size="small"
							@row-click="filterOrganizationRowClick"
							@selection-change="filterOrganizationChange"
							style="width: 100%"
						>
							<el-table-column
								type="selection"
								width="55"
							/>
							<el-table-column
								prop="name"
								label="组织名称"
								width="280"
							/>
							<el-table-column
								prop="parentPathName"
								label="组织路径"
							/>
						</el-table>
						<el-table
							v-if="selectType === 'personnel'"
							ref="userTableRef"
							class="custom-filter-table"
							:data="filterUserList"
							row-key="uuid"
							height="390"
							size="small"
							@row-click="filterUserRowClick"
							@selection-change="filterUserChange"
							style="width: 100%"
						>
							<el-table-column
								type="selection"
								width="55"
							/>
							<el-table-column
								prop="name"
								label="用户"
								width="280"
							/>
							<el-table-column
								prop="parentOrganizationName"
								label="部门"
							/>
						</el-table>
					</div>
				</div>
				<div
					v-show="!filterText"
					class="select-left-box"
				>
					<div class="select-item-box-left">
						<div class="select-header-box"><span class="header-title">标签</span></div>
						<div class="select-list-content">
							<el-scrollbar>
								<div
									v-for="item in labelList"
									class="select-label-item"
									:class="{ 'select-label-current': selectLabelUuid === item.uuid }"
									:key="item.uuid"
									:title="item.name as string"
									@click="selectLabel(item)"
								>
									{{ item.name }}
								</div>
								<div
									v-if="labelList.length === 0"
									class="el-tree__empty-block"
								>
									<span class="el-tree__empty-text">暂无数据</span>
								</div>
							</el-scrollbar>
						</div>
					</div>
					<div
						class="select-item-box-left"
						:class="{ 'width-440': selectType === 'organization' }"
					>
						<div class="select-header-box"><span class="header-title">组织机构</span></div>
						<div class="select-list-content">
							<el-scrollbar ref="organizationTreeScrollbarRef">
								<el-tree
									class="custom-dialog-organization-tree"
									:class="{ 'list-organization-tree': selectLabelUuid }"
									ref="organizationTreeRef"
									:data="organizationList"
									:props="organizationProps"
									node-key="uuid"
									show-checkbox
									:default-expanded-keys="organizationDefaultExpanded"
									:current-node-key="organizationCurrentNodeKey"
									:check-strictly="checkStrictly"
									:highlight-current="true"
									:check-on-click-node="organizationCheckOnClick"
									:expand-on-click-node="false"
									icon="ArrowRight"
									@check="organizationCheck"
									@node-click="organizationNodeClick"
								>
									<template #default="{ node, data }">
										<span
											class="custom-tree-node"
											:title="node.label"
										>
											<span>{{ node.label }}</span>
										</span>
									</template>
								</el-tree>
							</el-scrollbar>
						</div>
					</div>
					<div
						v-if="selectType === 'personnel'"
						class="select-item-box-left"
					>
						<div class="select-header-box">
							<span class="header-title">用户</span>
							<el-checkbox
								v-model="checkedAllUser"
								class="check-all-user"
								:indeterminate="isIndeterminate"
								label="全选"
								size="large"
								:disabled="userList.length === 0"
								@change="handleCheckAllUser"
							/>
						</div>
						<div
							class="select-list-content"
							ref="userTreeBoxRef"
						>
							<el-scrollbar
								ref="userTreeScrollbarRef"
								@scroll="userTreeScroll"
								style="height: 100%"
							>
								<el-tree
									class="custom-dialog-user-tree"
									ref="userTreeRef"
									:data="userList.slice(0, userTreeShowNum)"
									:props="userProps"
									node-key="uuid"
									show-checkbox
									:highlight-current="true"
									:check-on-click-node="true"
									@check="userNodeCheck"
								>
									<template #default="{ node, data }">
										<span
											class="custom-tree-node"
											:title="node.label"
										>
											<span>{{ node.label }}</span>
										</span>
									</template>
								</el-tree>
							</el-scrollbar>
						</div>
					</div>
				</div>
				<div class="select-right-box">
					<div class="select-item-box-right">
						<div class="select-header-box">
							<span class="header-title">已选择 {{ selectUserOrOrganizationList.length }} 项</span>
							<el-button
								v-if="selectUserOrOrganizationList.length > 0"
								link
								type="primary"
								class="header-cancel-btn"
								@click="clearSelect"
							>
								清空
							</el-button>
						</div>
						<div
							class="select-list-content padding-4"
							ref="selectListBoxRef"
						>
							<el-scrollbar
								ref="selectListScrollbarRef"
								@scroll="selectListScroll"
								style="height: 100%"
							>
								<div
									v-for="item in selectUserOrOrganizationList.slice(0, userListShowNum)"
									class="select-user-organization"
									:key="item.uuid"
								>
									<div
										class="select-user-organization-name"
										:title="item.name"
									>
										{{ item.name }}
									</div>
									<el-button
										link
										@click="clearSelectItem(item)"
									>
										<el-icon class="select-user-organization-close"><Close /></el-icon>
									</el-button>
								</div>
								<div
									v-if="selectUserOrOrganizationList.length === 0"
									class="el-tree__empty-block"
								>
									<span class="el-tree__empty-text">暂未选择</span>
								</div>
							</el-scrollbar>
						</div>
					</div>
				</div>
			</div>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button
					class="confirm-btn"
					type="primary"
					:disabled="selectUserOrOrganizationList.length === 0"
					@click="handleConfirm"
				>
					确认
				</el-button>
				<el-button
					class="cancel-btn"
					@click="handleClose"
					>取消</el-button
				>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="tsx">
import { ref, watch, computed, Component, onMounted, reactive, nextTick } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { cloneDeep } from 'lodash-es';
import * as SelectTypes from './utils/types';
import { getLabelListApi, filterOrganizationListApi, getOrganizationListApi, getUserListApi } from './utils/api';
import type Node from 'element-plus/es/components/tree/src/model/node';
import { TableInstance } from 'element-plus';
type selectType = 'personnel' | 'organization'; //personnel为选择成员、organization为选择组织
//选择的人员或者组织机构数据定义
export interface selectUserOrOrganization {
	uuid: string; //唯一标识
	name: string; //名称
	parentPathName?: string; //组织路径
	parentOrganizationName?: string; //部门
}
interface DialogProps {
	modelValue: boolean; //弹窗是否打开
	selectType?: selectType; //弹窗的选择类型
	organizationDefaultSelects?: selectUserOrOrganization[]; //选择组织机构时默认回显的数据
	userDefaultSelects?: selectUserOrOrganization[]; //选择人员时默认回显的数据
}
const props = withDefaults(defineProps<DialogProps>(), {
	modelValue: false,
	selectType: 'personnel',
	organizationDefaultSelects: () => [],
	userDefaultSelects: () => [],
});

// #region 筛选操作相关变量定义
const filterText = ref(''); //筛选字段
const filterOrganizationList = ref<SelectTypes.OrganizationListItem[]>([]); //搜索的组织机构结果
const filterUserList = ref<SelectTypes.UserListItem[]>([]); //搜索的用户结果
const organizationTableRef = ref<TableInstance>(); //搜索组织机构表格实例
const organizationTableSelection = ref<SelectTypes.FilterOrganizationListItem[]>([]); //搜索组织机构时列表复选框选中的结果
const userTableRef = ref<TableInstance>(); //搜索用户表格实例
const userTableSelection = ref<SelectTypes.UserListItem[]>([]); //搜索人员时列表复选框选中的结果

// #endregion
// #region 标签操作相关变量定义
const labelList = ref<SelectTypes.LabelListItem[]>([]); //标签列表数据
const selectLabelUuid = ref<string | undefined>(''); //当前选中的标签
// #endregion
// #region 组织机构相关变量定义
const rootOrganizationUuid = ref<string>('');
const organizationTreeRef = ref(); //组织机构树的实例
const organizationList = ref<SelectTypes.OrganizationListItem[]>([]); //组织机构列表数据
const organizationProps = reactive({
	children: 'children',
	label: 'name',
});
const organizationTreeScrollbarRef = ref(); //组织树el-scrollbar的实例
const organizationCheckOnClick = ref<boolean>(false); //点击节点时选择复选框
const checkStrictly = ref<boolean>(false); //组织机构树是否父子关联
const organizationDefaultExpanded = ref<string[]>([]); //组织机构树默认展开项
const organizationCurrentNodeKey = ref<string>(); //组织机构树默认选中节点
// #endregion

// #region 用户相关变量定义
const checkedAllUser = ref<boolean>(false);
const isIndeterminate = ref(false);
const userTreeShowNum = ref<number>(20);
const userTreeBoxRef = ref(); //用户树盒子的实例
const userTreeScrollbarRef = ref(); //用户树el-scrollbar的实例
const userTreeRef = ref(); //用户树的实例
const selectListBoxRef = ref(); //选择的用户结果列表盒子的实例
const selectListScrollbarRef = ref(); //选择的用户结果列表el-scrollbar的实例
const userListShowNum = ref<number>(20);
const userSelectKeys = ref<SelectTypes.UserListItem[]>([]); //选中的用户数据
const userList = ref<SelectTypes.UserListItem[]>([]);
const userProps = reactive({
	children: 'children',
	label: 'name',
});
// #endregion

const selectUserOrOrganizationList = ref<selectUserOrOrganization[]>([]); //选择的人员或者组织机构

type EmitProps = {
	(e: 'update:modelValue', val: boolean): void;
	(e: 'handleConfirm', val: selectUserOrOrganization[]): void;
};
const emits = defineEmits<EmitProps>();
const dialogVisible = computed({
	get() {
		if (props.modelValue) {
			if (props.selectType === 'organization') {
				selectUserOrOrganizationList.value = cloneDeep(props.organizationDefaultSelects);
			} else {
				selectUserOrOrganizationList.value = cloneDeep(props.userDefaultSelects);
			}
			getLabelList();
		}
		return props.modelValue;
	},
	set(val) {
		emits('update:modelValue', val);
	},
});
//根据选择类型来计算弹窗标题
const title = computed(() => {
	if (props.selectType === 'personnel') {
		return '添加成员';
	}
	if (props.selectType === 'organization') {
		return '添加组织';
	}
});
const placeholder = computed(() => {
	if (props.selectType === 'personnel') {
		return '请输入人员姓名查询';
	}
	if (props.selectType === 'organization') {
		return '请输入组织名称查询';
	}
});
//初始化弹窗
const initDialog = () => {
	filterText.value = '';
	filterOrganizationList.value = [];
	organizationTableSelection.value = [];
	filterUserList.value = [];
	userTableSelection.value = [];
	selectUserOrOrganizationList.value = [];
	labelList.value = [];
	selectLabelUuid.value = '';
	organizationList.value = [];
	checkStrictly.value = false;
	organizationCheckOnClick.value = false;
	organizationDefaultExpanded.value = [];
	organizationCurrentNodeKey.value = '';
	userList.value = [];
	checkedAllUser.value = false;
	isIndeterminate.value = false;
	userSelectKeys.value = [];
	organizationTreeRef.value.setCheckedKeys([]);
	if (props.selectType === 'personnel') {
		userTreeScrollbarRef.value.scrollTo(0, 0);
		userTreeShowNum.value = 20;
		userTreeRef.value.setCheckedKeys([]);
	}
	selectListScrollbarRef.value.scrollTo(0, 0);
	userListShowNum.value = 20;
};
// #region 搜索框相关事件逻辑
const onInput = (value: string) => {
	if (props.selectType === 'organization') {
		value && getFilterOrganizationList(value);
	} else {
		value && rootOrganizationUuid.value && getUserList(rootOrganizationUuid.value, 1, value);
	}
};
const inputClear = () => {
	const selectedMap = new Map();
	selectUserOrOrganizationList.value.forEach((item) => {
		selectedMap.set(item.uuid, item);
	});
	const checkedKeys = Array.from(selectedMap.keys());
	if (props.selectType === 'organization') {
		organizationTableSelection.value = [];
		organizationTableRef.value!.clearSelection();
		filterOrganizationList.value = [];
		organizationTreeRef.value.setCheckedKeys(checkedKeys);
	} else {
		userTableSelection.value = [];
		userTableRef.value!.clearSelection();
		filterUserList.value = [];
		userTreeRef.value.setCheckedKeys(checkedKeys);
		//找出勾选的人员
		userSelectKeys.value = userList.value.filter((value) => selectedMap.get(value.uuid)?.uuid);
		if (userList.value.length === 0) {
			checkedAllUser.value = false;
			isIndeterminate.value = false;
		} else {
			if (userSelectKeys.value.length === userList.value.length) {
				checkedAllUser.value = true;
				isIndeterminate.value = false;
			} else {
				checkedAllUser.value = false;
				isIndeterminate.value = userSelectKeys.value.length === 0 ? false : true;
			}
		}
	}
};
const filterOrganizationRowClick = (row: SelectTypes.FilterOrganizationListItem, column: any, event: Event) => {
	const hasIndex = organizationTableSelection.value.findIndex((item) => item.uuid === row.uuid);
	if (hasIndex > -1) {
		organizationTableRef.value!.toggleRowSelection(row, false);
	} else {
		organizationTableRef.value!.toggleRowSelection(row, true);
	}
};
const filterOrganizationChange = (newSelection: SelectTypes.FilterOrganizationListItem[]) => {
	const organizationTableSelectionMap = new Map();
	organizationTableSelection.value.forEach((item) => {
		organizationTableSelectionMap.set(item.uuid, item);
	});
	const newSelectionMap = new Map();
	newSelection.forEach((item) => {
		newSelectionMap.set(item.uuid, item);
	});
	const selectedMap = new Map();
	selectUserOrOrganizationList.value.forEach((item) => {
		selectedMap.set(item.uuid, item);
	});
	//找出新勾选的人员
	const newAddUser = newSelection.filter((value) => !organizationTableSelectionMap.get(value.uuid)?.uuid);
	//找出取消勾选的人员
	const cancelUser = organizationTableSelection.value.filter((value) => !newSelectionMap.get(value.uuid)?.uuid);
	//代表新勾选了人员
	if (newAddUser.length > 0) {
		newAddUser.forEach((addItem) => {
			const hasItem = selectedMap.has(addItem.uuid);
			if (!hasItem) {
				selectedMap.set(addItem.uuid, addItem);
			}
		});
	}
	//代表取消勾选了人员
	if (cancelUser.length > 0) {
		cancelUser.forEach((cancelItem) => {
			const hasItem = selectedMap.has(cancelItem.uuid);
			if (hasItem) {
				selectedMap.delete(cancelItem.uuid);
			}
		});
	}
	selectUserOrOrganizationList.value = Array.from(selectedMap.values());
	organizationTableSelection.value = organizationTableRef.value!.getSelectionRows();
};
const filterUserRowClick = (row: SelectTypes.UserListItem, column: any, event: Event) => {
	const hasIndex = userTableSelection.value.findIndex((item) => item.uuid === row.uuid);
	if (hasIndex > -1) {
		userTableRef.value!.toggleRowSelection(row, false);
	} else {
		userTableRef.value!.toggleRowSelection(row, true);
	}
};
const filterUserChange = (newSelection: SelectTypes.UserListItem[]) => {
	userTreeScrollbarRef.value.scrollTo(0, 0);
	userTreeShowNum.value = 20;
	const userTableSelectionMap = new Map();
	userTableSelection.value.forEach((item) => {
		userTableSelectionMap.set(item.uuid, item);
	});
	const newSelectionMap = new Map();
	newSelection.forEach((item) => {
		newSelectionMap.set(item.uuid, item);
	});
	const selectedMap = new Map();
	selectUserOrOrganizationList.value.forEach((item) => {
		selectedMap.set(item.uuid, item);
	});
	//找出新勾选的人员
	const newAddUser = newSelection.filter((value) => !userTableSelectionMap.get(value.uuid)?.uuid);
	//找出取消勾选的人员
	const cancelUser = userTableSelection.value.filter((value) => !newSelectionMap.get(value.uuid)?.uuid);
	//代表新勾选了人员
	if (newAddUser.length > 0) {
		newAddUser.forEach((addItem) => {
			const hasItem = selectedMap.has(addItem.uuid);
			if (!hasItem) {
				selectedMap.set(addItem.uuid, addItem);
			}
		});
	}
	//代表取消勾选了人员
	if (cancelUser.length > 0) {
		cancelUser.forEach((cancelItem) => {
			const hasItem = selectedMap.has(cancelItem.uuid);
			if (hasItem) {
				selectedMap.delete(cancelItem.uuid);
			}
		});
	}
	selectUserOrOrganizationList.value = Array.from(selectedMap.values());
	userTableSelection.value = userTableRef.value!.getSelectionRows();
};
//根据关键字搜索组织机构列表
const getFilterOrganizationList = (organizationName: string) => {
	filterOrganizationListApi({ organizationName }).then((res) => {
		if (res.code === 0) {
			organizationTableSelection.value = [];
			organizationTableRef.value!.clearSelection();
			filterOrganizationList.value = res.data;
			nextTick(() => {
				if (selectUserOrOrganizationList.value.length > 0) {
					const selectedMap = new Map();
					selectUserOrOrganizationList.value.forEach((item) => {
						selectedMap.set(item.uuid, item);
					});
					filterOrganizationList.value.forEach((itemUser) => {
						if (selectedMap.has(itemUser.uuid)) {
							organizationTableRef.value!.toggleRowSelection(itemUser, true);
						}
					});
				}
			});
		}
	});
};

// #endregion

// #region 标签操作相关逻辑
//获取标签列表数据
const getLabelList = () => {
	getLabelListApi({ tenantId: '' }).then((res) => {
		if (res.code === 0) {
			res.data.unshift({
				uuid: '',
				name: '全部组织',
			});
			labelList.value = res.data;
			selectLabelUuid.value = res.data[0].uuid;
			if (props.selectType === 'organization') {
				checkStrictly.value = true;
				organizationCheckOnClick.value = true;
			}
			getOrganizationList(selectLabelUuid.value!);
		}
	});
};
const selectLabel = (item: SelectTypes.LabelListItem) => {
	if (selectLabelUuid.value === item.uuid) {
		return;
	}
	selectLabelUuid.value = item.uuid;
	getOrganizationList(selectLabelUuid.value!);
};
// #endregion

// #region 组织机构操作相关逻辑
//获取组织机构列表
const getOrganizationList = (labelUuid: string) => {
	getOrganizationListApi({ labelUuid }).then((res) => {
		if (res.code === 0) {
			organizationTreeScrollbarRef.value?.scrollTo(0, 0);
			organizationList.value = res.data;
			if (res.data[0]) {
				//初始化时保存组织机构根组织uuid
				if (!rootOrganizationUuid.value) {
					rootOrganizationUuid.value = res.data[0].uuid!;
				}
				nextTick(() => {
					if (props.selectType === 'organization') {
						const checkedKeys = selectUserOrOrganizationList.value.map((item) => item.uuid);
						if (checkedKeys.length > 0) {
							//右侧有选中的组织机构，设置组织机构树默认展开以及复选框选中
							organizationDefaultExpanded.value = checkedKeys;
							organizationTreeRef.value.setCheckedKeys(checkedKeys);
						} else {
							if (res.data[0]?.children && res.data[0]?.children.length > 0) {
								//如果第一个节点存在子级默认展开第一个节点
								organizationDefaultExpanded.value = [res.data[0]?.uuid!];
							}
						}
					} else {
						organizationTreeRef.value.setCheckedKeys([]);
						if (res.data[0]?.children && res.data[0]?.children.length > 0) {
							//如果第一个节点存在子级默认展开第一个节点
							//解决切换标签时第一个节点uuid一样，导致不默认展开
							organizationDefaultExpanded.value = [];
							setTimeout(() => {
								organizationDefaultExpanded.value = [res.data[0]?.uuid!];
							}, 20);
						}
						// 默认选中第一个节点
						//解决切换标签时第一个节点uuid一样，导致不默认选中
						organizationCurrentNodeKey.value = '';
						setTimeout(() => {
							organizationCurrentNodeKey.value = res.data[0]?.uuid!;
						}, 20);
						getUserList(res.data[0]?.uuid!, 0);
					}
				});
			} else {
				//组织机构无数据时
				initOrganization();
				userList.value = [];
			}
		}
	});
};
const initOrganization = () => {
	organizationDefaultExpanded.value = [];
	organizationCurrentNodeKey.value = '';
};
//组织机构复选框事件
const organizationCheck = (data: SelectTypes.LabelListItem, checkedObj: any) => {
	if (props.selectType === 'personnel') {
		let organizationSelectKeys = organizationTreeRef.value.getCheckedKeys();
		getUserList(organizationSelectKeys.join(), 1);
	} else {
		selectListScrollbarRef.value.scrollTo(0, 0);
		userListShowNum.value = 20;
		const checkNode = organizationTreeRef.value.getNode(data.uuid);
		const isCheckSelf = checkNode && checkNode.checked;
		const hasIndex = selectUserOrOrganizationList.value.findIndex((item) => item.uuid === data.uuid);
		if (isCheckSelf) {
			const checkData = { uuid: data.uuid!, name: data.name! };
			hasIndex === -1 && selectUserOrOrganizationList.value.push(checkData);
		} else {
			if (hasIndex > -1) {
				selectUserOrOrganizationList.value.splice(hasIndex, 1);
			}
		}
	}
};
// 组织机构点击节点
const organizationNodeClick = (data: SelectTypes.OrganizationListItem, node: Node) => {
	if (props.selectType === 'personnel') {
		organizationTreeRef.value.setCheckedKeys([]); //点击节点时清空复选框选中后，再次选中当前
		getUserList(data.uuid!, 0);
	}
};

// #endregion

// #region 用户列表操作相关逻辑
//根据选择的组织机构UUid查询用户
const getUserList = (organizationSelectKey: string, includeAllSubLevels: number = 1, keywords: string = '') => {
	const params: SelectTypes.UserListRequest = {
		parentOrganizationId: organizationSelectKey,
		includeAllSubLevels: includeAllSubLevels,
		keywords: keywords,
		offset: 0,
		limit: 99999,
	};
	getUserListApi(params).then((res) => {
		if (res.code === 0) {
			if (keywords) {
				//代表用户进行搜索操作
				if (props.selectType === 'personnel') {
					userTableSelection.value = [];
					userTableRef.value!.clearSelection();
					filterUserList.value = res.data;
					nextTick(() => {
						if (selectUserOrOrganizationList.value.length > 0) {
							const selectedMap = new Map();
							selectUserOrOrganizationList.value.forEach((item) => {
								selectedMap.set(item.uuid, item);
							});
							filterUserList.value.forEach((itemUser) => {
								if (selectedMap.has(itemUser.uuid)) {
									userTableRef.value!.toggleRowSelection(itemUser, true);
								}
							});
						}
					});
				}
			} else {
				userTreeScrollbarRef.value.scrollTo(0, 0);
				userTreeShowNum.value = 20;
				userList.value = res.data;
				const selectedMap = new Map();
				selectUserOrOrganizationList.value.forEach((item) => {
					selectedMap.set(item.uuid, item);
				});
				const checkedKeys = Array.from(selectedMap.keys());
				userTreeRef.value.setCheckedKeys(checkedKeys);

				selectListScrollbarRef.value.scrollTo(0, 0);
				userListShowNum.value = 20;
				//找出勾选的人员
				userSelectKeys.value = userList.value.filter((value) => selectedMap.get(value.uuid)?.uuid);

				if (userList.value.length === 0) {
					checkedAllUser.value = false;
					isIndeterminate.value = false;
				} else {
					if (userSelectKeys.value.length === userList.value.length) {
						checkedAllUser.value = true;
						isIndeterminate.value = false;
					} else {
						checkedAllUser.value = false;
						isIndeterminate.value = userSelectKeys.value.length === 0 ? false : true;
					}
				}
			}
		}
	});
};
const handleCheckAllUser = (isCheckSelf: string | number | boolean) => {
	userTreeScrollbarRef.value.scrollTo(0, 0);
	userTreeShowNum.value = 20;
	selectListScrollbarRef.value.scrollTo(0, 0);
	userListShowNum.value = 20;
	isIndeterminate.value = false;
	const selectedMap = new Map();
	selectUserOrOrganizationList.value.forEach((item) => {
		selectedMap.set(item.uuid, item);
	});
	if (isCheckSelf) {
		const checkedKeys = userList.value.map((item) => item.uuid);
		userTreeRef.value.setCheckedKeys(checkedKeys);
		userSelectKeys.value = JSON.parse(JSON.stringify(userList.value));
		//找出新勾选的人员
		const newAddUser = userList.value.filter((value) => !selectedMap.get(value.uuid)?.uuid);
		//代表新勾选了人员
		if (newAddUser.length > 0) {
			newAddUser.forEach((addItem) => {
				selectedMap.set(addItem.uuid, addItem);
			});
		}
	} else {
		userTreeRef.value.setCheckedKeys([]);
		//代表取消勾选了人员
		if (userList.value.length > 0) {
			userList.value.forEach((cancelItem) => {
				const hasItem = selectedMap.has(cancelItem.uuid);
				if (hasItem) {
					selectedMap.delete(cancelItem.uuid);
				}
			});
		}
	}
	selectUserOrOrganizationList.value = Array.from(selectedMap.values());
};
const userNodeCheck = (data: SelectTypes.UserListItem, checkedObj: any) => {
	selectListScrollbarRef.value.scrollTo(0, 0);
	userListShowNum.value = 20;

	const checkNode = userTreeRef.value.getNode(data.uuid);
	const isCheckSelf = checkNode && checkNode.checked;
	const hasIndex = selectUserOrOrganizationList.value.findIndex((item) => item.uuid === data.uuid);
	const hasUserIndex = userSelectKeys.value.findIndex((item) => item.uuid === data.uuid);
	if (isCheckSelf) {
		const checkData = { uuid: data.uuid!, name: data.name!, parentOrganizationName: data.parentOrganizationName! };
		hasIndex === -1 && selectUserOrOrganizationList.value.push(checkData);
		hasUserIndex === -1 && userSelectKeys.value.push(checkData);
	} else {
		hasIndex > -1 && selectUserOrOrganizationList.value.splice(hasIndex, 1);
		hasUserIndex > -1 && userSelectKeys.value.splice(hasUserIndex, 1);
	}
	if (userList.value.length === 0) {
		checkedAllUser.value = false;
		isIndeterminate.value = false;
	} else {
		if (userSelectKeys.value.length === userList.value.length) {
			checkedAllUser.value = true;
			isIndeterminate.value = false;
		} else {
			checkedAllUser.value = false;
			isIndeterminate.value = userSelectKeys.value.length === 0 ? false : true;
		}
	}
};

const userTreeScroll = (event: any) => {
	if (userTreeShowNum.value > userList.value.length) {
		return;
	}
	const scrollTop = event.scrollTop;
	const clientHeight = userTreeBoxRef.value.getBoundingClientRect().height;
	const scrollHeight = userTreeScrollbarRef.value.wrapRef.scrollHeight;
	if (scrollTop + clientHeight + 10 >= scrollHeight) {
		userTreeShowNum.value += 20;
		nextTick(() => {
			const checkedKeys = selectUserOrOrganizationList.value.map((item) => item.uuid);
			userTreeRef.value.setCheckedKeys(checkedKeys);
		});
	}
};
const selectListScroll = (event: any) => {
	if (userListShowNum.value > selectUserOrOrganizationList.value.length) {
		return;
	}
	const scrollTop = event.scrollTop;
	const clientHeight = selectListBoxRef.value.getBoundingClientRect().height;
	const scrollHeight = selectListScrollbarRef.value.wrapRef.scrollHeight;
	if (scrollTop + clientHeight + 10 >= scrollHeight) {
		userListShowNum.value += 20;
	}
};
// #endregion

// #region 右侧选中区域清空按钮
// 清空操作
const clearSelect = () => {
	selectUserOrOrganizationList.value = [];

	selectListScrollbarRef.value.scrollTo(0, 0);
	userListShowNum.value = 20;
	if (props.selectType === 'organization') {
		if (filterText.value) {
			organizationTableRef.value!.clearSelection();
		} else {
			organizationTreeRef.value.setCheckedKeys([]);
		}
	} else {
		userTreeScrollbarRef.value.scrollTo(0, 0);
		userTreeShowNum.value = 20;
		userSelectKeys.value = [];
		checkedAllUser.value = false;
		isIndeterminate.value = false;
		if (filterText.value) {
			userTableRef.value!.clearSelection();
		} else {
			userTreeRef.value.setCheckedKeys([]);
		}
	}
};
//右侧选中区域单项删除
const clearSelectItem = (selectItem: selectUserOrOrganization) => {
	const hasIndex = selectUserOrOrganizationList.value.findIndex((item) => item.uuid === selectItem.uuid);

	hasIndex > -1 && selectUserOrOrganizationList.value.splice(hasIndex, 1);

	const checkedKeys = selectUserOrOrganizationList.value.map((item) => item.uuid);
	if (props.selectType === 'organization') {
		if (filterText.value) {
			const findItem = organizationTableSelection.value.find((item) => item.uuid === selectItem.uuid);
			findItem && organizationTableRef.value!.toggleRowSelection(findItem, false);
		} else {
			organizationTreeRef.value.setCheckedKeys(checkedKeys);
		}
	} else {
		userTreeScrollbarRef.value.scrollTo(0, 0);
		userTreeShowNum.value = 20;
		if (filterText.value) {
			const findItem = userTableSelection.value.find((item) => item.uuid === selectItem.uuid);
			findItem && userTableRef.value!.toggleRowSelection(findItem, false);
		} else {
			userTreeRef.value.setCheckedKeys(checkedKeys);
		}
		const hasUserIndex = userSelectKeys.value.findIndex((item) => item.uuid === selectItem.uuid);
		hasUserIndex > -1 && userSelectKeys.value.splice(hasUserIndex, 1);
		if (userList.value.length === 0) {
			checkedAllUser.value = false;
			isIndeterminate.value = false;
		} else {
			if (userSelectKeys.value.length === userList.value.length) {
				checkedAllUser.value = true;
				isIndeterminate.value = false;
			} else {
				checkedAllUser.value = false;
				isIndeterminate.value = userSelectKeys.value.length === 0 ? false : true;
			}
		}
	}
};

// #endregion
//弹窗点击确实事件
const handleConfirm = () => {
	emits('handleConfirm', selectUserOrOrganizationList.value);
	handleClose();
};
//弹窗关闭
const handleClose = () => {
	emits('update:modelValue', false);
	initDialog();
};
</script>

<style lang="scss">
.select-organization-member-dialog {
	padding: 12px 20px;
	.el-dialog__header {
		padding-bottom: 32px;
		.el-dialog__title {
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 16px;
			color: #1d2129;
			line-height: 24px;
		}
	}
	.select-dialog-box {
		.select-dialog-con {
			width: 100%;
			height: 391px;
			margin-top: 20px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			background-color: #ffffff;
			.filter-left-box {
				width: 660px;
				height: 100%;
				box-sizing: border-box;
				border: 1px solid #e5e6eb;
				display: flex;
				.filter-result-box {
					width: 100%;
					height: 100%;
					.custom-filter-table {
						font-size: 14px;
						thead {
							color: #86909c;
							th {
								font-weight: 400;
							}
						}
						th.el-table__cell {
							background-color: #f2f3f5;
						}
						.el-table__cell {
							padding: 6px 0;
						}
						.el-checkbox.el-checkbox--small .el-checkbox__inner {
							width: 16px;
							height: 16px;
						}
						.el-checkbox__input.is-indeterminate .el-checkbox__inner:before {
							top: 6px;
						}
						.el-checkbox__inner:after {
							top: 2px;
							left: 6px;
						}
					}
				}
			}
			.select-left-box {
				height: 100%;
				box-sizing: border-box;
				display: flex;
				border: 1px solid #e5e6eb;
			}
			.select-item-box-left {
				box-sizing: border-box;
				width: 220px;
				height: 100%;
				border-right: 1px solid #e5e6eb;
				&:last-of-type {
					border-right: none;
				}
			}
			.width-440 {
				width: 440px;
			}
			.select-right-box {
				box-sizing: border-box;
				width: 240px;
				height: 100%;
				border: 1px solid #e5e6eb;
				.select-item-box-right {
					width: 100%;
					height: 100%;
				}
			}
			.select-header-box {
				height: 34px;
				background: #f2f3f5;
				border-bottom: 1px solid #e5e6eb;
				padding: 0 12px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				.header-title {
					font-family: MicrosoftYaHei;
					font-size: 14px;
					color: #86909c;
					line-height: 19px;
				}
				.header-cancel-btn {
					// cursor: pointer;
					// font-family: MicrosoftYaHei;
					font-size: 14px;
					// color: #0075c2;
					line-height: 19px;
				}
			}
			.select-list-content {
				width: 100%;
				height: calc(100% - 40px);
				overflow: hidden;
				box-sizing: border-box;
				.check-all-user {
					padding-left: 12px;
					height: 34px;
					.el-checkbox__label {
						font-size: 14px;
						color: #1d2129;
					}
					.el-checkbox__inner {
						width: 16px;
						height: 16px;
					}
					.el-checkbox__inner:after {
						top: 2px;
						left: 5px;
					}
					.el-checkbox__inner:before {
						top: 6px;
					}
				}
				.custom-dialog-organization-tree,
				.custom-dialog-user-tree {
					--el-tree-node-content-height: 34px;
					--el-tree-node-hover-bg-color: #f2f3f5;
					--el-tree-text-color: #1d2129;
					cursor: default;
					font-size: 14px;
					.el-tree-node__expand-icon {
						color: #86909c;
						font-size: 16px;
						padding: 0;
						margin: 0 4px 0 12px;
					}
					.custom-tree-node {
						flex: 1;
						display: inline-block;
						padding-right: 12px;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
					.el-checkbox__inner {
						width: 16px;
						height: 16px;
					}
					.el-checkbox__inner:after {
						top: 2px;
						left: 5px;
					}
					.el-checkbox__inner:before {
						top: 6px;
					}
				}
				.list-organization-tree,
				.custom-dialog-user-tree {
					.el-tree-node__expand-icon {
						display: none;
					}
					.el-checkbox {
						margin-left: 12px;
					}
				}
				.select-label-item {
					cursor: pointer;
					width: auto;
					height: 34px;
					padding: 0 12px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 14px;
					color: #1d2129;
					line-height: 34px;
					&:hover {
						background: #f2f3f5;
					}
				}
				.select-label-current {
					background: #f1f9ff;
					color: #0075c2;
					&:hover {
						background: #f1f9ff;
					}
				}
				.select-user-organization {
					width: auto;
					height: 26px;
					margin-bottom: 8px;
					padding: 0 8px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					background: #f2f3f5;
					border-radius: 2px;
					.select-user-organization-name {
						flex: 1;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 14px;
						color: #1d2129;
						line-height: 22px;
						margin-right: 8px;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
					.select-user-organization-close {
						font-size: 16px;
						cursor: pointer;
					}
				}
			}
			.padding-4 {
				padding: 4px;
			}
		}
	}
	.dialog-footer {
		padding-top: 30px;
		text-align: left;
		.el-button {
			border-radius: 2px;
			height: 28px;
			line-height: 28px;
		}
		.el-button.is-disabled,
		.el-button.is-disabled:hover {
			background-color: #0075c2;
			opacity: 0.35;
		}
		.confirm-btn {
			background-color: #0075c2;
			&:hover {
				background-color: #79bbff;
			}
		}
	}
	.el-tree__empty-block {
		height: 340px;
	}
}
</style>
