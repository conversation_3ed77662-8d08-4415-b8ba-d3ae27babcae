import { ElButton, ElPopover } from 'element-plus';
export type ElButtonType = InstanceType<typeof ElButton>['$props']['type'];
export type ElPopoverPlacement = InstanceType<typeof ElPopover>['$props']['placement'];
declare global {
	interface OptionBtn<Row = any> {
		label: string; // 按钮文字
		onClick: (val: Row, ev?: any) => void; // 按钮点击事件
		icon?: string; // 如果存在icon 则显示icon图标，隐藏文字
		key?: string; // 唯一
		auth?: string | string[]; // 按钮的权限 拥有这个权限的时候，按钮才会显示
		type?: ElButtonType; // 按钮类型
		tips?: boolean | string | ((val: Row) => boolean | string); // 是否需要确认框
		order?: number; //  顺序 从1开始算， 1在第一位
		hide?: boolean | ((val: Row) => boolean); // 是否隐藏
		placement?: ElPopoverPlacement; // 确认框的位置 element-plus 暂时不支持
		disabled?: boolean | ((val: Row) => boolean); // 是否禁用按钮
		more?: boolean; // 是否显示到。。。中
		// other?: AnyObj; // 其它字段
	}
}
