<template>
	<div class="edit-form">
		<div class="main">
			<el-form
				ref="ruleFormRef"
				:model="ruleForm"
				:rules="rules"
				label-width="90"
				class="form-view"
				label-position="left"
				status-icon
			>
				<div class="form-line">
					<div class="form-text">主体信息</div>
				</div>
				<el-form-item
					label="名称"
					prop="name"
				>
					<el-input
						v-model="ruleForm.name"
						placeholder="请输入名称"
					/>
				</el-form-item>
				<el-form-item
					label="活动区域"
					prop="region"
				>
					<el-select
						v-model="ruleForm.region"
						placeholder="请选择活动区域"
					>
						<el-option
							label="Zone one"
							value="shanghai"
						/>
						<el-option
							label="Zone two"
							value="beijing"
						/>
					</el-select>
				</el-form-item>
				<el-form-item
					label="活动时间"
					required
				>
					<el-col :span="11">
						<el-form-item prop="date1">
							<el-date-picker
								v-model="ruleForm.date1"
								type="date"
								aria-label="Pick a date"
								placeholder="选择日期"
								style="width: 100%"
							/>
						</el-form-item>
					</el-col>
					<el-col
						class="text-center"
						:span="2"
					>
						<span class="text-gray-500">-</span>
					</el-col>
					<el-col :span="11">
						<el-form-item prop="date2">
							<el-time-picker
								v-model="ruleForm.date2"
								aria-label="Pick a time"
								placeholder="选择时间"
								style="width: 100%"
							/>
						</el-form-item>
					</el-col>
				</el-form-item>
				<div class="form-line">
					<div class="form-text">扩展信息</div>
				</div>
				<el-form-item
					label="名称"
					prop="name"
				>
					<el-input
						v-model="ruleForm.name"
						placeholder="请输入名称"
					/>
				</el-form-item>
				<el-form-item
					label="活动区域"
					prop="region"
				>
					<el-select
						v-model="ruleForm.region"
						placeholder="请选择活动区域"
					>
						<el-option
							label="Zone one"
							value="shanghai"
						/>
						<el-option
							label="Zone two"
							value="beijing"
						/>
					</el-select>
				</el-form-item>
				<el-form-item
					label="活动时间"
					required
				>
					<el-col :span="11">
						<el-form-item prop="date1">
							<el-date-picker
								v-model="ruleForm.date1"
								type="date"
								aria-label="Pick a date"
								placeholder="选择日期"
								style="width: 100%"
							/>
						</el-form-item>
					</el-col>
					<el-col
						class="text-center"
						:span="2"
					>
						<span class="text-gray-500">-</span>
					</el-col>
					<el-col :span="11">
						<el-form-item prop="date2">
							<el-time-picker
								v-model="ruleForm.date2"
								aria-label="Pick a time"
								placeholder="选择时间"
								style="width: 100%"
							/>
						</el-form-item>
					</el-col>
				</el-form-item>
				<el-form-item
					label="名称"
					prop="name"
				>
					<el-input
						v-model="ruleForm.name"
						placeholder="请输入名称"
					/>
				</el-form-item>
				<el-form-item
					label="活动区域"
					prop="region"
				>
					<el-select
						v-model="ruleForm.region"
						placeholder="请选择活动区域"
					>
						<el-option
							label="Zone one"
							value="shanghai"
						/>
						<el-option
							label="Zone two"
							value="beijing"
						/>
					</el-select>
				</el-form-item>
				<el-form-item
					label="活动时间"
					required
				>
					<el-col :span="11">
						<el-form-item prop="date1">
							<el-date-picker
								v-model="ruleForm.date1"
								type="date"
								aria-label="Pick a date"
								placeholder="选择日期"
								style="width: 100%"
							/>
						</el-form-item>
					</el-col>
					<el-col
						class="text-center"
						:span="2"
					>
						<span class="text-gray-500">-</span>
					</el-col>
					<el-col :span="11">
						<el-form-item prop="date2">
							<el-time-picker
								v-model="ruleForm.date2"
								aria-label="Pick a time"
								placeholder="选择时间"
								style="width: 100%"
							/>
						</el-form-item>
					</el-col>
				</el-form-item>
				<el-form-item
					label="名称"
					prop="name"
				>
					<el-input
						v-model="ruleForm.name"
						placeholder="请输入名称"
					/>
				</el-form-item>
				<el-form-item
					label="活动区域"
					prop="region"
				>
					<el-select
						v-model="ruleForm.region"
						placeholder="请选择活动区域"
					>
						<el-option
							label="Zone one"
							value="shanghai"
						/>
						<el-option
							label="Zone two"
							value="beijing"
						/>
					</el-select>
				</el-form-item>
				<el-form-item
					label="活动时间"
					required
				>
					<el-col :span="11">
						<el-form-item prop="date1">
							<el-date-picker
								v-model="ruleForm.date1"
								type="date"
								aria-label="Pick a date"
								placeholder="选择日期"
								style="width: 100%"
							/>
						</el-form-item>
					</el-col>
					<el-col
						class="text-center"
						:span="2"
					>
						<span class="text-gray-500">-</span>
					</el-col>
					<el-col :span="11">
						<el-form-item prop="date2">
							<el-time-picker
								v-model="ruleForm.date2"
								aria-label="Pick a time"
								placeholder="选择时间"
								style="width: 100%"
							/>
						</el-form-item>
					</el-col>
				</el-form-item>
				<el-form-item
					label="性质"
					prop="type"
				>
					<el-checkbox-group v-model="ruleForm.type">
						<el-checkbox
							value="1"
							name="type"
							>美食/餐厅线上活动</el-checkbox
						>
						<el-checkbox
							value="2"
							name="type"
							>地推活动</el-checkbox
						>
						<el-checkbox
							value="3"
							name="type"
							>线下主题活动</el-checkbox
						>
						<el-checkbox
							value="4"
							name="type"
							>单纯品牌曝光</el-checkbox
						>
					</el-checkbox-group>
				</el-form-item>
				<el-form-item
					label="特殊资源"
					prop="resource"
				>
					<el-radio-group v-model="ruleForm.resource">
						<el-radio value="1">线上品牌商赞助</el-radio>
						<el-radio value="2">线下场地免费</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item
					label="即时配送"
					prop="delivery"
				>
					<el-switch
						class="w-full"
						:active-text="ruleForm.delivery ? '已开启' : '已关闭'"
						v-model="ruleForm.delivery"
					/>
					<div class="tip-text">开启后，将在下单后由系统立即安排配送</div>
				</el-form-item>
				<el-form-item
					label="活动介绍"
					prop="desc"
				>
					<el-input
						rows="4"
						resize="none"
						maxlength="200"
						show-word-limit
						v-model="ruleForm.desc"
						placeholder="请输入活动结束"
						type="textarea"
					/>
				</el-form-item>

				<!-- <el-form-item label="Activity location" prop="location">
					<el-segmented v-model="ruleForm.location" :options="locationOptions" />
				</el-form-item> -->

				<!-- <el-form-item>
					<el-button type="primary" @click="submitForm(ruleFormRef)"> Create </el-button>
					<el-button @click="resetForm(ruleFormRef)">Reset</el-button>
				</el-form-item> -->
			</el-form>
		</div>
		<div class="footer">
			<div class="footer-view">
				<el-button type="primary">立即创建</el-button>
				<el-button>保存草稿</el-button>
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
interface RuleForm {
	name: string;
	region: string;
	date1: string;
	date2: string;
	delivery: boolean;
	type: string[];
	resource: string;
	desc: string;
}

const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive<RuleForm>({
	name: '',
	region: '',
	date1: '',
	date2: '',
	delivery: false,
	type: [],
	resource: '',
	desc: '',
});

const rules = reactive<FormRules<RuleForm>>({
	name: [
		{ required: true, message: 'Please input Activity name', trigger: 'blur' },
		{ min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' },
	],
	region: [
		{
			required: true,
			message: 'Please select Activity zone',
			trigger: 'change',
		},
	],
	date1: [
		{
			type: 'date',
			required: true,
			message: 'Please pick a date',
			trigger: 'change',
		},
	],
	date2: [
		{
			type: 'date',
			required: true,
			message: 'Please pick a time',
			trigger: 'change',
		},
	],
	type: [
		{
			type: 'array',
			required: true,
			message: 'Please select at least one activity type',
			trigger: 'change',
		},
	],
	resource: [
		{
			required: true,
			message: 'Please select activity resource',
			trigger: 'change',
		},
	],
	desc: [{ required: true, message: 'Please input activity form', trigger: 'blur' }],
});

const submitForm = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate((valid, fields) => {
		if (valid) {
			console.log('submit!');
		} else {
			console.log('error submit!', fields);
		}
	});
};

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	formEl.resetFields();
};

const options = Array.from({ length: 10000 }).map((_, idx) => ({
	value: `${idx + 1}`,
	label: `${idx + 1}`,
}));
</script>
<style lang="scss" scoped>
.edit-form {
	display: flex;
	flex-direction: column;
	height: 100%;
	.main {
		flex: 1;
		min-height: 0;
		overflow: auto;

		.form-view {
			width: 777px;
			margin: 20px auto;

			.form-line {
				height: 48px;
				display: flex;
				align-items: center;
				.form-text {
					border-left: 3px solid var(--el-color-primary);
					font-family: PingFangSC, PingFang SC;
					font-weight: 500;
					font-size: 16px;
					color: #1d2129;
					line-height: 24px;
					padding-left: 6px;
				}
			}
		}
	}
	.footer {
		border-top: 1px solid #e5e6eb;
		height: 64px;
		display: flex;
		align-items: center;
		.footer-view {
			width: 777px;
			margin: 0 auto;
		}
	}
}
</style>
