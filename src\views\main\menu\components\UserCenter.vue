<template>
	<el-popover placement="right" trigger="click" width="286" popper-class="custom-user-center-popover">
		<template #reference>
			<li class="el-menu-item custom-menu-item">
				<img src="/src/assets/images/menu/avatar.png" class="avatar-img" />
				<span class="t-text" title="个人中心">个人中心</span>
			</li>
		</template>
		<div class="user-center-box">
			<div class="user-center-header">
				<img src="/src/assets/images/menu/avatar.png" class="avatar-photo" />
				<div class="user-data-box">
					<div class="user-name">{{ userData.name }}</div>
					<div class="user-account">
						<span>{{ userData.enterpriseName }}</span>
						<span class="department-name">{{ userData.departmentName }}</span>
					</div>
				</div>
			</div>
			<div class="user-center-main">
				<div @click="handleClick(item.type)" class="menu-cell" v-for="(item, index) in menuList" :key="index" c>
					{{ item.name }}
				</div>
			</div>
		</div>
	</el-popover>
	<DialogPassword v-model="dialogVisible"></DialogPassword>
	<DialogUserinfo v-model="dialogVisibleUserInfo"></DialogUserinfo>
</template>
<script lang="tsx" setup>
import DialogPassword from './DialogPassword.vue';
import DialogUserinfo from './DialogUserinfo.vue';
import type { Action } from 'element-plus';
import { getSystemTheme, setSystemTheme } from '@/config';
import screenfull from 'screenfull';
import { goToLogout } from '@/api/user';
import { commonConfirm } from '@/utils';
import { storeUserInfo } from '@/store';
import { ref, watch } from 'vue';
const dialogVisible = ref(false);
const dialogVisibleUserInfo = ref(false);
const userData = {
	name: storeUserInfo.name,
	enterpriseName: storeUserInfo.enterpriseName,
	departmentName: storeUserInfo.departmentName,
};
const menuList = [
	{
		name: '个人中心',
		type: 'userCenter',
	},
	{
		name: '下载移动端',
		type: 'downloadApp',
	},
	{
		name: '切换全屏',
		type: 'switchFullScreen',
	},
	{
		name: '切换主题',
		type: 'switchTheme',
	},
	{
		name: '修改密码',
		type: 'changePassword',
	},
	{
		name: '切换用户',
		type: 'switchUser',
	},
	{
		name: '退出登录',
		type: 'logOut',
	},
];

function handleClick(type) {
	switch (type) {
		case 'changePassword':
			dialogVisible.value = true;
			break;
		case 'logOut':
			commonConfirm('是否退出？').then(() => {
				goToLogout();
			});
			break;
		case 'userCenter':
			dialogVisibleUserInfo.value = true;
			break;
		case 'switchFullScreen':
			// 获取当前全屏状态，为后续状态使用
			// screenfull.isFullscreen;
			if (screenfull.isEnabled) {
				screenfull.toggle();
			}
			break;
		case 'switchTheme':
			let theme = getSystemTheme();
			let themeText = theme == 'primary' ? '白色' : '蓝色';
			ElMessageBox.alert('是否切换为' + themeText + '主题？', '提示', {
				showCancelButton: true,
				cancelButtonText: '取消',
				confirmButtonText: '确定',
				callback: (action: Action) => {
					if (action == 'confirm') {
						setSystemTheme(theme == 'primary' ? 'default' : 'primary');
						window.location.reload();
					}
				},
			});
			break;
	}
}
</script>
<style lang="scss" scoped>
.avatar-img {
	width: 24px;
	height: 24px;
	border-radius: 18px;
}
.user-center-box {
	.user-center-header {
		padding: 16px;
		display: flex;
		border-bottom: 1px solid #e5e6eb;

		.avatar-photo {
			width: 40px;
			height: 40px;
			border: 1px solid #e5e6eb;
			border-radius: 40px;
			margin-right: 12px;
		}

		.user-data-box {
			flex: 1;
			min-width: 0;
			.user-name {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #1d2129;
				line-height: 22px;
				margin-bottom: 4px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.user-account {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 12px;
				color: #86909c;
				line-height: 22px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				.department-name {
					margin-left: 6px;
				}
			}
		}
	}
	.user-center-main {
		.menu-cell {
			height: 40px;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 14px;
			color: #1d2129;
			line-height: 22px;
			padding: 10px 16px 8px 16px;
			box-sizing: border-box;
			cursor: pointer;
		}

		.menu-cell:last-child {
			border-top: 1px solid #e5e6eb;
		}

		.menu-cell:hover {
			background: #f2f3f5;
		}
	}
}
</style>
<style lang="scss">
.custom-user-center-popover {
	padding: 0 !important;
}

.custom-menu-item {
	padding-left: 8px!important;

	.t-text {
		margin-left: 12px!important;
	}
}
</style>
