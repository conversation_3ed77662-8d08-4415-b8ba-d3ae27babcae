<template>
	<el-dialog
		v-model="dialogVisible"
		width="400px"
		:title="title + '原因'"
		:close-on-click-modal="false"
		append-to-body
		align-center
		destroy-on-close
		@closed="closeDialog"
		@open="handleOpen"
		><el-input
			v-model="textarea"
			style="width: 100%"
			:rows="5"
			type="textarea"
			:placeholder="'请输入' + title + '原因'"
		/>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="closeDialog">取消</el-button>
				<el-button
					type="primary"
					@click="submit"
				>
					确定
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
interface Props {
	title: string; //新增还是查看还是编辑
}
const props = withDefaults(defineProps<Props>(), {
	title: '',
});
const dialogVisible = defineModel<boolean>('visible');
const textarea = ref('');
const emits = defineEmits<{
	(ev: 'sure', str: string): void;
}>();

onMounted(() => {});

function handleOpen() {
	textarea.value = '';
}

function submit() {
	emits('sure', textarea.value);
	dialogVisible.value = false;
	textarea.value = '';
}

function closeDialog() {
	dialogVisible.value = false;
	textarea.value = '';
}

defineExpose({
	open,
	close,
});
</script>
<style lang="scss" scoped></style>
