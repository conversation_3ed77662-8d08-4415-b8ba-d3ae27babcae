<template>
    <el-input v-model="displayValue" :placeholder="placeholder" @input="handleInput" @blur="handleBlur"
        :disabled="disabled" clearable>
        <template #append>
            <div style="width: 40px;">元</div>
        </template></el-input>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue'

// 定义 props
const props = defineProps({
    // 绑定的金额数值（以分为单位或元为单位，根据业务需求调整）
    modelValue: {
        type: Number,
        default: 0
    },
    // 占位提示文本
    placeholder: {
        type: String,
        default: '请输入'
    },
    // 是否禁用
    disabled: {
        type: Boolean,
        default: false
    },
    // 最大金额限制
    max: {
        type: Number,
        default: Infinity
    }
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'change'])

// 显示用的值（可能带千位分隔符）
const displayValue = ref('')
// 原始数值（用于计算）
const rawValue = ref(0)

/**
 * 格式化金额：保留两位小数并添加千位分隔符
 * @param {Number} num - 原始数值
 * @returns {String} 格式化后的金额字符串
 */
const formatAmount = (num) => {
    if (isNaN(num) || num === 0) return ''

    // 保留两位小数
    const fixedNum = num.toFixed(2)

    // 分割整数和小数部分
    const [integerPart, decimalPart] = fixedNum.split('.')

    // 整数部分添加千位分隔符
    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

    return `${formattedInteger}.${decimalPart}`
}

// 初始化：将传入的数值转换为显示格式
watch(
    () => props.modelValue,
    (val) => {
        const num = Number(val) || 0
        rawValue.value = num
        displayValue.value = formatAmount(num)
    },
    { immediate: true }
)



/**
 * 解析用户输入的字符串为数值
 * @param {String} str - 用户输入的字符串
 * @returns {Number} 解析后的数值
 */
const parseAmount = (str) => {
    if (!str || str.trim() === '') return 0

    // 移除所有千位分隔符和非数字字符（保留小数点）
    const cleaned = str.replace(/,/g, '').replace(/[^\d.]/g, '')

    // 处理多个小数点的情况
    const dotIndex = cleaned.indexOf('.')
    if (dotIndex !== -1) {
        // 只保留第一个小数点，且小数点后最多两位
        return parseFloat(cleaned.substring(0, dotIndex + 3)) || 0
    }

    return parseFloat(cleaned) || 0
}

/**
 * 处理输入事件：实时过滤非法字符
 */
const handleInput = (value) => {
    // 过滤非数字和非小数点字符，移除多余的小数点
    const filtered = value.replace(/[^\d.,]/g, '').replace(/(?<=^\d*)\.(?=.*\.)/g, '')
    displayValue.value = filtered
}

/**
 * 处理失焦事件：格式化最终值并同步给父组件
 */
const handleBlur = () => {
    const parsed = parseAmount(displayValue.value)

    // 校验最大值
    if (parsed > props.max) {
        displayValue.value = formatAmount(props.max)
        rawValue.value = props.max
    } else {
        displayValue.value = formatAmount(parsed)
        rawValue.value = parsed
    }

    // 同步给父组件
    emit('update:modelValue', rawValue.value)
    emit('change', rawValue.value)
}
</script>

<style scoped></style>
