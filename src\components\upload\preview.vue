<!--
  功能：功能描述
  作者：ligw
  时间：2024年08月14日 16:07:58
  版本：v1.0
  修改记录：
  修改内容：
  修改人员：
  修改时间：
-->
<template>
  <div v-if="visible" class="file-viewer-container">
    <FileViewer 
    v-bind="$attrs" 
    :initialIndex="activeIndex"
    :urlList="urlList" 
    @close="handleClose" ></FileViewer>
  </div>
</template>
<script lang='ts' setup>
import FileViewer from './file-viewer/index.mjs'
import { ref, computed } from 'vue'
import type { FileItem } from './image-upload.vue'
// import type { ImageViewerProps } from './file-viewer/src/image-viewer.d.ts'
import type {ImageViewerProps} from 'element-plus'
import { envConfig } from '@/config'
interface Props extends /* @vue-ignore */ ImageViewerProps {
  files?: FileItem[]; //初始文件列表渲染
}
const props = withDefaults(defineProps<Props>(), {
  files: () => []
})
const urlList = computed(()=>{
  return props.files.map(item => {
    return {
      ...item,
      fileAccessPath: envConfig.VITE_BASE_FILE_SATIC_PATH + item.fileAccessPath
    }
  })
})
const visible = ref<boolean>(false)
const activeIndex = ref<number>(0)
const handleClose = () => {
  visible.value = false
}
const show = (index:number) => {
  visible.value = true
  activeIndex.value = index
}

defineExpose({ show })

</script>
<style scoped lang='scss'>
.file-viewer-container{
  :deep(.el-image-viewer__actions){
    width: 424px;
    height: 48px;
    background: rgba(29,33,41,0.75);
    box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.06);
    border-radius: 4px;
    
  }
  :deep(.el-image-viewer__canvas){
    padding: 68px;
  }
}
</style>
