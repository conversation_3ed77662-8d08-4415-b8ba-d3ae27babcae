<template>
	<div class="container">
		<el-row>
			<el-col :span="24">
				<el-card class="card-view">
					<template #header>
						<div class="card-header">
							<span>选择人员</span>
							<el-button
								type="primary"
								@click="selsctOrganization"
								>选择人员</el-button
							>
						</div>
					</template>
					<div>
						<div>1、使用时，请在public/config.json文件下，配置“VITE_SELECT_ORGANIZATION_MEMBER”</div>
						<div>2、弹窗内接口需要正式有效token，请保证本地运行时token有效</div>
						<div>3、userDefaultSelects为回显勾选过的人员</div>
						<img
							class="example-img"
							src="./assets/example.png"
							alt=""
							srcset=""
						/>
					</div>
				</el-card>
			</el-col>
		</el-row>
		<select-organization-member
			v-model="dialogVisible"
			:selectType="selectType"
			:userDefaultSelects="userDefaultSelects"
			@handleConfirm="handleConfirm"
		/>
	</div>
</template>
<script lang="tsx" setup>
import { ref } from 'vue';
import { type selectUserOrOrganization } from '@/components/select-organization-member/index.vue';
type selectType = 'personnel' | 'organization';
const selectType = ref<selectType>('personnel');
const dialogVisible = ref<boolean>(false);
//默认选中数据
const userDefaultSelects = ref<selectUserOrOrganization[]>([
	{
		uuid: '66a8beba78652baa88b4cd24',
		name: '马驰',
		parentOrganizationName: '16134中心（阿联酋）',
	},
	{
		uuid: '66b5bb4952faff0008d86b07',
		name: '王博【勿删除！】',
		parentOrganizationName: '中国机械设备工程股份有限公司',
	},
	{
		uuid: '66b6f6a852faff0007d86faf',
		name: '郭敏庆【勿删除】',
		parentOrganizationName: '中国机械设备工程股份有限公司',
	},
	{
		uuid: '66c6d09446e0fb0001614976',
		name: '何晶',
		parentOrganizationName: '中机国际（湖南）工程咨询有限责任公司',
	},
	{
		uuid: '66c6d09446e0fb0001614982',
		name: '郑丽媛',
		parentOrganizationName: '办公室',
	},
	{
		uuid: '66c6d09446e0fb0001614984',
		name: '李军社',
		parentOrganizationName: '机械工业勘察设计研究院有限公司',
	},
	{
		uuid: '66c6d09446e0fb0001614986',
		name: '龚非',
		parentOrganizationName: '技术部',
	},
	{
		uuid: '66c6d09446e0fb0001614987',
		name: '袁韬',
		parentOrganizationName: '设备及自动化工程所',
	},
	{
		uuid: '66c6d09446e0fb0001614988',
		name: '何翔',
		parentOrganizationName: '设备及自动化工程所',
	},
	{
		uuid: '66c6d09446e0fb0001614989',
		name: '文良勇',
		parentOrganizationName: '中设广场项目运营管理部',
	},
	{
		uuid: '66c6d09446e0fb000161498c',
		name: '王蓓',
		parentOrganizationName: '加工贸易部',
	},
	{
		uuid: '66c6d09446e0fb000161498d',
		name: '周雨先',
		parentOrganizationName: '工程设计研究所',
	},
	{
		uuid: '66c6d09446e0fb000161498e',
		name: '刘孝达',
		parentOrganizationName: '工程四部',
	},
	{
		uuid: '66c6d09446e0fb000161498f',
		name: '吴蕊',
		parentOrganizationName: '欧洲区域',
	},
	{
		uuid: '66c6d09446e0fb00016149d3',
		name: '刘智',
		parentOrganizationName: '机械工业勘察设计研究院有限公司',
	},
	{
		uuid: '66c6d09446e0fb00016149d4',
		name: '杨利民',
		parentOrganizationName: '泰昌电子',
	},
	{
		uuid: '66c6d09446e0fb00016149d5',
		name: '姚海霞',
		parentOrganizationName: '办公室',
	},
	{
		uuid: '66c6d09446e0fb00016149d6',
		name: '李昌坤',
		parentOrganizationName: '中机国际工程设计研究院有限责任公司',
	},
	{
		uuid: '66c6d09446e0fb00016149d7',
		name: '王玥',
		parentOrganizationName: '业务二部',
	},
	{
		uuid: '66c6d09446e0fb00016149d8',
		name: '苏广雷',
		parentOrganizationName: '工程三部',
	},
	{
		uuid: '66c6d09446e0fb00016149e3',
		name: '孙朕',
		parentOrganizationName: '工程一部',
	},
	{
		uuid: '66c6d09446e0fb00016149e4',
		name: '虞戈',
		parentOrganizationName: '工程六部',
	},
	{
		uuid: '66c6d09446e0fb00016149e5',
		name: '张克涛',
		parentOrganizationName: '设备及自动化工程所',
	},
	{
		uuid: '66c6d09446e0fb00016149e6',
		name: '杨璠',
		parentOrganizationName: '机械工业勘察设计研究院有限公司',
	},
	{
		uuid: '66c6d09446e0fb00016149e7',
		name: '李聪',
		parentOrganizationName: '电力工程所',
	},
	{
		uuid: '66c6d09446e0fb00016149e8',
		name: '周伟',
		parentOrganizationName: '公司领导',
	},
	{
		uuid: '66c6d09446e0fb00016149e9',
		name: '邓利民',
		parentOrganizationName: '办公室',
	},
	{
		uuid: '66c6d09546e0fb0001614a00',
		name: '朱圣元',
		parentOrganizationName: '机械工业勘察设计研究院有限公司',
	},
	{
		uuid: '66c6d09546e0fb0001614a01',
		name: '胡延结',
		parentOrganizationName: '工程部',
	},
	{
		uuid: '66c6d09546e0fb0001614a02',
		name: '刘小龙',
		parentOrganizationName: '第一市政环保工程所',
	},
	{
		uuid: '66c6d09546e0fb0001614a03',
		name: '朱丽霞',
		parentOrganizationName: '财务部',
	},
	{
		uuid: '66c6d09546e0fb0001614a04',
		name: '范培军',
		parentOrganizationName: '工程三部',
	},
	{
		uuid: '66c6d09546e0fb0001614a05',
		name: '张建平',
		parentOrganizationName: '运行管理部',
	},
	{
		uuid: '66c6d09546e0fb0001614a06',
		name: '冒亭菲',
		parentOrganizationName: '建筑景观板块',
	},
	{
		uuid: '66c6d09546e0fb0001614a07',
		name: '徐传召',
		parentOrganizationName: '机械工业勘察设计研究院有限公司',
	},
	{
		uuid: '66c6d09546e0fb0001614a08',
		name: '林晓莉',
		parentOrganizationName: '中机中电设计研究院有限公司',
	},
	{
		uuid: '66c6d09546e0fb0001614a15',
		name: '刘月',
		parentOrganizationName: '供应链服务部',
	},
	{
		uuid: '66c6d09546e0fb0001614a16',
		name: '吉翔',
		parentOrganizationName: '第六工程与贸易事业部',
	},
	{
		uuid: '66c6d09546e0fb0001614a17',
		name: '廖华国',
		parentOrganizationName: '道路桥梁设计所',
	},
	{
		uuid: '66c6d09546e0fb0001614a18',
		name: '白福东',
		parentOrganizationName: '研发部（工程部）',
	},
]);
const selsctOrganization = () => {
	dialogVisible.value = true;
};
const handleConfirm = (selectList: any) => {
	console.log(selectList, 'selectList======选择的人员或者组织机构');
};
</script>
<style lang="scss" scoped>
.container {
	padding: 20px;

	.card-view {
		margin-bottom: 20px;
	}

	.example-img {
		width: 700px;
		margin-top: 20px;
	}
}
</style>
