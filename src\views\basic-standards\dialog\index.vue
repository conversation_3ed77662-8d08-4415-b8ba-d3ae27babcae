<template>
	<div class="dialog-content">
		<el-row :gutter="20">
			<el-col
				:span="6"
				v-for="(item, index) in dialigExampleList"
				:key="index"
			>
				<el-card class="box-card">
					<template #header>
						<div class="card-header">
							<span>{{ item.name }}</span>
							<el-button
								type="primary"
								class="button"
								@click="openDialog(item.type)"
								>Open</el-button
							>
						</div>
					</template>
					<div>
						<div v-for="tip in item.tips">{{ tip }}</div>
					</div>
				</el-card>
			</el-col>
		</el-row>
		<mini-dialog v-model:visible="miniDialogVisible" />
		<small-dialog v-model:visible="smallDialogVisible" />
		<medium-dialog v-model:visible="mediumDialogVisible" />
		<large-dialog v-model:visible="largeDialogVisible" />
	</div>
</template>

<script lang="tsx" setup>
import { ref } from 'vue';
import MiniDialog from './components/MiniDialog.vue';
import SmallDialog from './components/SmallDialog.vue';
import MediumDialog from './components/MediumDialog.vue';
import LargeDialog from './components/LargeDialog.vue';

// 弹窗示例列表
const dialigExampleList = [
	{
		name: '小号弹窗',
		type: 'mini',
		tips: ['宽度：固定320px', '高度：最大320px'],
	},
	{
		name: '中号弹窗',
		type: 'small',
		tips: ['宽度：固定560px', '高度：最大800px'],
	},
	{
		name: '大号弹窗',
		type: 'medium',
		tips: ['弹窗宽度：固定320px', '内容高度：最大210px'],
	},
	{
		name: '全屏弹窗',
		type: 'large',
		tips: ['弹窗宽度：固定320px', '内容高度：最大210px'],
	},
];

// 小号弹窗显隐
const miniDialogVisible = ref<boolean>(false);
// 中号弹窗显隐
const smallDialogVisible = ref<boolean>(false);
// 大号弹窗显隐
const mediumDialogVisible = ref<boolean>(false);
// 全屏弹窗显隐
const largeDialogVisible = ref<boolean>(false);

const openDialog = (type) => {
	switch (type) {
		case 'mini':
			miniDialogVisible.value = true;
			break;
		case 'small':
			smallDialogVisible.value = true;
			break;
		case 'medium':
			mediumDialogVisible.value = true;
			break;
		case 'large':
			largeDialogVisible.value = true;
			break;
		default:
			break;
	}
};
</script>

<style lang="scss" scoped>
.dialog-content {
	padding: 20px;

	.button {
		margin-left: 20px;
	}
}
</style>
