<template>
	<header-main>
		<template #header-right>
			<el-button type="primary">导出清单</el-button>
		</template>
		<template #main>
			<common-table
				:pagination="false"
				showIndex
				:loading="loading"
				:columns="tableColumns"
				:fill-screen="true"
				:data="tableData"
				:createButtonText="'创建类型'"
				@onClickCreateBtn="onClickCreateBtn"
				:queryList="queryList"
				:btnsMoreNumber="2"
				:getBtns="btns"
			>
				<template #header-right> </template>
				<template #slotStatus="{ row }">
					<el-switch
						:model-value="row.isValid === 1"
						@change="(val: any) => handleStatusChange(row, val as boolean)"
					/>
				</template>
			</common-table>
		</template>
	</header-main>
</template>

<script lang="tsx" setup>
import { onMounted, ref, reactive } from 'vue';
import { deleteApprovalCategoryApi, getApprovalCategoryListApi, updateApprovalCategoryApi } from '../api';
import { ElMessage } from 'element-plus';

interface Props {
	title?: string;
}
const props = withDefaults(defineProps<Props>(), {});

const emit = defineEmits<{
	(ev: 'update:value', value: string): void;
	(ev: 'openForm', type: string): void;
	(ev: 'editForm', type: string, data: any): void;
}>();
//表单标识的枚举映射表
const formFlagMap = {
	21: '合同审批',
	24: '证照使用审批单',
	25: '付款申请单',
	29: '项目招标采购需求审批表',
	31: '出差申请单',
	32: '出差费用报销单',
};
// 表格loading
const loading = ref(false);

// 表格数据
const tableData = ref<any[]>([]);

// 表格列定义
const tableColumns: CommonTableColumn<any>[] = [
	{
		label: '类别编码',
		prop: 'cNumber',
		width: 120,
	},
	{
		label: '类别名称',
		prop: 'cName',
		width: 150,
	},
	{
		label: '业务说明',
		prop: 'bDescription',
		width: 200,
		showOverflowTooltip: true,
	},
	{
		label: '表单标识',
		prop: 'bFormFlag',
		width: 100,
		formatter: (row: any) => {
			return formFlagMap[row.bFormFlag] || '--';
		},
	},
	{
		label: '流程名称',
		prop: 'dFlowName',
		width: 150,
		showOverflowTooltip: true,
	},
	{
		label: '流程编号',
		prop: 'dFlowNumber',
		width: 120,
	},
	{
		label: '会签表标识',
		prop: 'signTableFlag',
		width: 120,
		formatter: (row: any) => {
			return row.signTableFlag === 1 ? '是' : '否';
		},
	},
	{
		label: '是否启用',
		prop: 'isValid',
		width: 100,
		slotName: 'slotStatus',
	},
	{
		label: '排序',
		prop: 'sortOrder',
		width: 80,
	},
	{
		label: '备注',
		prop: 'remark',
		width: 150,
		showOverflowTooltip: true,
	},
];

const btns: OptionBtn[] = [
	{
		label: '编辑',
		onClick(row: any) {
			console.log('编辑');
			emit('editForm', 'edit', row);
		},
	},
	{
		label: '删除',
		tips: '确定删除吗？',
		onClick(row: any) {
			console.log('删除');
			deleteApprovalCategoryApi(row.cNumber).then(() => {
				getList();
			});
		},
	},
];

// 初始化表格数据
onMounted(() => {
	getList();
});

const getList = () => {
	loading.value = true;
	getApprovalCategoryListApi({
		isOnlyValid:false,
		types:'generic_approval'
	})
		.then((res: any) => {
			console.log(res);
			tableData.value = res || [];
		})
		.finally(() => {
			loading.value = false;
		});
};

const refreshList = () => {
	getList();
};

// 暴露给父组件的方法
defineExpose({
	refreshList,
	getList,
});

// 状态切换处理
const handleStatusChange = (row: any, val: boolean) => {
	console.log('状态切换:', row, val);
	// 更新本地数据
	row.isValid = val ? 1 : 0;
	// TODO: 调用接口更新状态
	ElMessage.success(`已${val ? '启用' : '禁用'}该项目`);
	//调用修改接口
	updateApprovalCategoryApi(row);
};

const onClickCreateBtn = () => {
	console.log('点击新增按钮');
	emit('openForm', 'add');
};

const queryList = ref<CommonTableOptions[]>([
	{
		label: '类别编码',
		value: 'cNumber',
	},
	{
		label: '类别名称',
		value: 'cName',
	},
	{
		label: '流程名称',
		value: 'dFlowName',
	},
]);
</script>

<style scoped lang="scss"></style>
