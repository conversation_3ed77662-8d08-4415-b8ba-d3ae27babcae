<template>
	<div class="">
		<table-list
			v-if="!hasOpenForm"
			@openForm="onOpenForm"
			@editForm="onEditForm"
		></table-list>
		<Form
			v-else
			@back="onFormBack"
			@success="onFormSuccess"
			:type="formType"
			:initData="formInitData"
		></Form>
	</div>
</template>

<script lang="ts" setup>
import tableList from './components/table-list.vue';
import Form from './components/form.vue';
import { ref } from 'vue';
import type { BusinessTypeForm } from './api/types';

const hasOpenForm = ref(false); //是否打开表单组件
const formType = ref('add'); //表单类型
const formInitData = ref<Partial<BusinessTypeForm>>({}); //表单初始数据

// 打开新增表单
const onOpenForm = (type: string) => {
	hasOpenForm.value = true;
	formType.value = type;
	formInitData.value = {};
};

// 打开编辑表单
const onEditForm = (type: string, data: BusinessTypeForm) => {
	hasOpenForm.value = true;
	formType.value = type;
	formInitData.value = data;
};

// 表单返回
const onFormBack = () => {
	hasOpenForm.value = false;
	formInitData.value = {};
};

// 表单保存成功
const onFormSuccess = () => {
	// 这里可以添加刷新列表等逻辑
	console.log('表单保存成功，可以刷新列表');
};
</script>

<style scoped lang="scss"></style>
