<template>
    <div class="xirui-flex-row-center address-action">
        <el-button type="primary" style="cursor: pointer;" @click="handleAddAddress">添加</el-button>
        <el-button type="" plain style="cursor: pointer;" @click="handleDeleteAddress">删除</el-button>
    </div>
    <el-table ref="tableRef" :data="tableData" style="width: 100%" border
        :header-cell-style="{ background: '#F2F3F5', textAlign: 'center' }" @selection-change="handleSelectionChange">
        <!-- 多选列 -->
        <el-table-column type="selection" width="55" align="center" />
        <!-- 序号列 -->
        <el-table-column label="序号" align="center" width="70">
            <template #default="scope">
                {{ scope.$index + 1 }}
            </template>
        </el-table-column>
        <!-- 基础信息列（一级表头） -->
        <el-table-column label="起止地点" align="center">
            <!-- 二级表头：嵌套在基础信息下 -->
            <!-- 出发地列（下拉示例） -->
            <el-table-column label="出发地" prop="StartCityNumber">
                <template #default="scope">
                    <el-select v-model="scope.row.StartCityNumber" placeholder="请搜索出发地" clearable style="width: 100%"
                        filterable remote :loading="loading" remote-show-suffix :remote-method="handleSearch"
                        @change="addressChangeAction($event, scope.row, 'start')">
                        <el-option v-for="item in addressOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </template>
            </el-table-column>
            <!-- 到达地列（下拉示例） -->
            <el-table-column label="到达地" prop="EndCityNumber">
                <template #default="scope">
                    <el-select v-model="scope.row.EndCityNumber" placeholder="请搜索到达地" clearable style="width: 100%"
                        filterable remote :loading="loading" remote-show-suffix :remote-method="handleSearch"
                        @change="addressChangeAction($event, scope.row, 'end')">
                        <el-option v-for="item in addressOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </template>
            </el-table-column>
        </el-table-column>
    </el-table>
</template>

<script setup>
import { ref, onUnmounted } from 'vue'
import { api_getRegionList } from '../api'
import { debounce } from 'lodash-es';

const loading = ref(false)

// 表格数据
const tableData = ref([
    {
        StartCityNumber: "",
        StartCityName: "",
        EndCityNumber: "",
        EndCityName: "",
    }
])

// 地址下拉选项示例
const addressOptions = ref([])

// 表格实例
const tableRef = ref(null)

// 存储选中的行
const selectedRows = ref([])

// 监听选中变化
function handleSelectionChange(rows) {
    selectedRows.value = rows
}

// 地点切换
function addressChangeAction(newValue, row, type) {
    console.log('newValue:', row);
    let tempNameKey = '';
    if (type === 'start') {
        tempNameKey = 'StartCityName';
    } else if (type === 'end') {
        tempNameKey = 'EndCityName';
    } else {
        return;
    }
    const selectedOption = addressOptions.value.find((item) => item.value === newValue) || {};
    row[tempNameKey] = selectedOption.label || '';
    console.log('tableData---------------', tableData.value);

}

// 添加按钮点击事件
function handleAddAddress() {
    tableData.value.push({
        StartCityNumber: "",
        StartCityName: "",
        EndCityNumber: "",
        EndCityName: "",
    });
}

// 删除按钮点击事件
function handleDeleteAddress() {
    if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择要删除的行')
        return
    }
    ElMessageBox.confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
    })
        .then(() => {
            console.log('selectedRows', selectedRows.value)
            const selectedIds = selectedRows.value.map(row => row.number)
            tableData.value = tableData.value.filter(row => !selectedIds.includes(row.number))
        })
        .catch(() => {
        })
}

// 输入校验逻辑：判断是否包含非法字符
const validateInput = (value) => {
    if (!value) return false  // 空值校验（允许清空）

    // 允许中文、字母、空格（根据业务调整规则）
    const reg = /^[\u4e00-\u9fa5a-zA-Z ]+$/
    return reg.test(value)
}

// 获取行政区划列表
function getRegionList(keyword) {
    // 1. 先进行输入校验，不符合合法则不发起请求
    if (!validateInput(keyword)) {
        addressOptions.value = [];
        return;
    }
    api_getRegionList(keyword).then((res) => {
        addressOptions.value = (res[0].records || []).map(val => { return { ...val, label: val.name, value: val.number }; });
    });
}

// 为函数添加防抖：延迟500ms执行，多次触发则重新计时
const handleSearch = debounce(getRegionList, 500)

// 组件卸载时清理防抖函数（避免内存泄漏）
onUnmounted(() => {
    handleSearch.cancel()  // 取消未执行的防抖函数
})

// 暴露方法给父组件，用于获取表格数据
defineExpose({
    getTableData: () => tableData.value
})

</script>

<style scoped>
.address-action {
    margin-top: 8px;
    margin-bottom: 16px;
}

/* 可根据需求调整样式细节，比如表头文字大小、颜色等 */
.el-table th {
    text-align: center;
    font-weight: 500;
}

.el-table td {
    text-align: center;
}
</style>