<template>
	<div>门户页</div>
	<ElButton @click="onClick">去登录</ElButton>
	<ElButton @click="onClick1">去注册</ElButton>
	<ElButton @click="onClick2">进入后台</ElButton>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { ElButton } from 'element-plus';
import { debounce, cloneDeep } from 'lodash-es';
import { routerInstance } from '@/router';

// import {} from '@element-plus/icons-vue';
// import imgUrl from './img.png'
defineOptions({ inheritAttrs: false });
// export type ModalFormInstance = InstanceType<typeof ModalForm>;
// const refElButton = ref<InstanceType<typeof ElButton>>();
interface Props {
	accept?: string;
	multiple?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
	accept: '111',
});
const emit = defineEmits<{
	(ev: 'update:value', value: string): void;
	(ev: 'change', seledObj: any): void;
}>();
function onClick() {
	routerInstance.push({
		name: 'login',
	});
}
function onClick1() {
	routerInstance.push({
		name: 'register',
	});
}
function onClick2() {
	routerInstance.push({
		name: 'main',
	});
}
// extends
</script>
<style lang="scss" scoped></style>
