import { envConfig } from '@/config';

interface msgDataType {
	appCode?: string;
	routerUrl?: string;
	routerParams?: any;
	url?: string;
	urlParams?: any;
	isFullScreen?: boolean;
}
// 发送token无效消息
export function sendTokenInvalidMsg() {
	sendMessage('TOKEN_INVALID', {});
}

// 发送登录无效消息
export function sendLoginInvalidMsg() {
	sendMessage('LOGIN_INVALID', {});
}

/**
 * 发送消息,打开其他应用
 * @param appCode 其他应用标识，一般为appName
 * @param routerUrl 路由路径。示例：/form/base-form
 * @param routerParams 路由扩展参数
 */
export function sendOpenOtherAppPageMsg(appCode: string, routerUrl: string, routerParams: any) {
	sendMessage('OPEN_OTHER_APP_PAGE', {
		appCode: appCode,
		routerUrl: routerUrl,
		routerParams: routerParams,
	});
}
/**
 * 发送消息,切换其他应用
 * @param appCode 其他应用标识，一般为appName
 */
export function sendChangeOtherAppPageMsg(appCode: string) {
	sendMessage('CHANGE_OTHER_APP_PAGE', {
		appCode: appCode,
	});
}
/**
 * 发送消息,浏览器打开新标签页
 * @param appCode 其他应用标识，一般为appName
 * @param url 需要打开的链接地址。示例：http://172.16.40.124/system-portal/home
 * @param urlParams 路由扩展参数
 */
export function sendOpenNewWindowTagMsg(appCode: string, url: string, urlParams: any) {
	sendMessage('OPEN_NEW_WINDOW_TAG', {
		appCode: appCode,
		url: url,
		urlParams: urlParams,
	});
}

export function sendResizeScreenMsg(isFullScreen: boolean) {
	sendMessage('RESIZE_SCREEN', { isFullScreen });
	const layoutSider = document.getElementsByClassName('layout-sider')[0] as HTMLElement;
	if (layoutSider) {
		layoutSider.style.display = isFullScreen ? 'none' : '';
	}
}

// 发送消息
function sendMessage(msgCode: string, msgData: msgDataType) {
	let messageData = {
		appCode: envConfig.VITE_APP_NAME,
		msgCode: msgCode,
		msgData: msgData,
	};
	window.parent.postMessage(messageData, '*');
}
