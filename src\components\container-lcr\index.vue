<template>
	<div
		class="i-container-lcr-box"
		ref="refBoxDom"
		:class="{
			't-show-right': showRight_start,
			't-show-header': hasHeader,
		}"
	>
		<div class="t-header" v-if="title_ || hasHeader">
			<slot name="header">
				<div class="t-title" :title="title_">{{ title_ }}</div>
			</slot>
		</div>
		<div class="t-content">
			<slot></slot>
		</div>
		<div class="t-show-right-box" v-if="hasRight">
			<div class="t-show-rigth-btn" @click="toggleRight()">
				<el-icon v-if="showRight_start"><ArrowRight /></el-icon>
				<el-icon v-else><ArrowLeft /></el-icon>
			</div>
		</div>
		<div class="t-right" v-if="hasRight">
			<slot name="right" :show="showRight_" :data="rightData" :title="rightTitle">
				<el-empty style="margin-top: 100px" :image-size="150" description="暂无内容" />
			</slot>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, watch, provide, useSlots, computed, onMounted } from 'vue';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import { commonConfirm } from '@/utils';
import { useRoute } from 'vue-router';

interface Props {
	title?: string | boolean; // 默认读取 路由中的title
	showRight?: boolean;
	duration?: number; //  关闭动画持续时间
	rightWidth?: number; //  右侧展开宽度
}
const props = withDefaults(defineProps<Props>(), {
	duration: 250,
	rightWidth: 386,
	title: true,
});
const emit = defineEmits<{
	(ev: 'update:showRight', value: boolean): void;
	(ev: 'rightOpen', seledObj: any): void; // 弹窗开始打开时触发
	(ev: 'rightClose', seledObj: any): void; // 弹窗关闭完成后触发
}>();
const refBoxDom = ref<HTMLDivElement>();
const rightData = ref<any>({}); // 打开右侧弹窗时，可以传一些数据，可以快捷传入right插槽的作用域中
const slots = useSlots();
const route = useRoute();
// 是否存在右侧插槽
const hasRight = computed(() => !!slots.right);
const hasHeader = computed(() => props.title || !!slots.header);
let timer: number; // 定时器
// 打开右侧时  showRight_ 和 start 先变为 true，然后 end 变为 true
// 关闭右侧时  start 先变为 false，然后 showRight_ 和 end变为 false
const showRight_ = ref(false); //
const showRight_start = ref(false); // 右侧打开前
const showRight_end = ref(false); // 右侧打开后
const rightTitle = ref(''); //
const rightIsEdit = ref(false); //右侧是否处于编辑状态

const title_ = computed(() => {
	if (props.title === false) return '';
	return props.title === true ? (route.meta?.title as string) : props.title;
});

const toggleRight: ToggleContainerRight = (show, data) => {
	if (!hasRight.value) return;
	Promise.resolve()
		.then(() => {
			const tips = data?.tips === false ? false : '正在编辑中，确定放弃吗？';
			if (show && showRight_start.value && rightIsEdit.value && tips) {
				return commonConfirm(tips);
			}
			return Promise.resolve('');
		})
		.then(() => {
			rightIsEdit.value = data?.isEdit || false;
			rightTitle.value = data?.title || '';
			show = show ?? !showRight_start.value;
			emit('update:showRight', show);
			setRightState(show, data);
		});
};
function setRightState(state: boolean, data?: ToggleContainerRightParams) {
	showRight_start.value = state;
	if (showRight_start.value) {
		rightData.value = data?.data || {};
		showRight_.value = true;
		emit('rightOpen', rightData.value);
		eventsRightOpen.forEach((cb) => {
			cb(data);
		});
	} else {
		eventsRightClose.forEach((cb) => {
			cb(data);
		});
	}
	clearTimeout(timer);
	timer = setTimeout(() => {
		showRight_end.value = showRight_start.value;
		showRight_.value = showRight_start.value;
		if (!showRight_.value) {
			rightData.value = {};
			emit('rightClose', data);
		}
	}, props.duration);
}
watch(
	() => props.showRight,
	(val) => {
		setRightState(val);
	},
);
onMounted(() => {
	refBoxDom.value!.style.setProperty('--animations-duration', `${props.duration / 1000}s`);
	refBoxDom.value!.style.setProperty('--right-width', `${props.rightWidth}px`);
});
let eventsRightOpen: Function[] = [];
let eventsRightClose: Function[] = [];
function onRightOpen(callBack: (val: any) => void) {
	eventsRightOpen.push(callBack);
}
function onRightClose(callBack: (val: any) => void) {
	eventsRightClose.push(callBack);
}
//  通过 provide 注入到子组件中，快捷调用
provide('toggleRight', toggleRight);
provide('onRightOpen', onRightOpen);
provide('onRightClose', onRightClose);
provide('rightState', showRight_);
defineExpose({
	toggleRight: toggleRight,
	onRightOpen: onRightOpen,
	onRightClose: onRightClose,
});
</script>
<style lang="scss" scoped>
// 这个类目不能动，在外部有用
.i-container-lcr-box {
	--animations-duration: 0.25s;
	--right-width: 386px;
	position: relative;
	background-color: #fff;
	height: 100%;
	box-sizing: border-box;
	margin-right: 8px;
	border-radius: 4px;
	transition: all var(--animations-duration);
	display: flex;
	flex-direction: column;
	&.t-show-right {
		margin-right: calc(var(--right-width) + 26px);
	}
	::v-deep(.i-common-table-box) {
		height: 100%;
	}
}
.t-header {
	flex-shrink: 0;
	line-height: 46px;
	border-bottom: 1px solid #ebeef5;
	padding: 0 16px;
	.t-title {
		line-height: 46px;
		font-size: 16px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		font-weight: bold;
	}
}
.t-content {
	padding: 16px;
	flex-grow: 1;
	overflow: auto;
}
.t-right {
	position: absolute;
	top: 0;
	bottom: 0;
	right: calc(var(--right-width) * -1 - 16px);
	width: calc(var(--right-width));
	background-color: #fff;
	display: flex;
	flex-direction: column;
	border-radius: 4px;
}
.t-show-right-box {
	position: absolute;
	width: 10px;
	height: 100%;
	right: -16px;
	top: 0;
	cursor: pointer;
	&:hover {
		background-color: rgb(229, 232, 239);
		border-radius: 10px;

		.t-show-rigth-btn {
			background-color: var(--el-color-primary);
			color: #fff;
		}
	}

	.t-show-rigth-btn {
		position: absolute;
		width: 10px;
		height: 60px;
		line-height: 60px;
		top: 0;
		bottom: 0;
		margin: auto;
		background-color: #e5e8ef;
		font-size: 12px;
		border-radius: 4px 0 0 4px;
		color: #97989b;
		i {
			margin-left: -1px;
		}
	}
}
</style>
