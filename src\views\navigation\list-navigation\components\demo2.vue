<template>
	<list-navigation
		:listData="data"
		v-model="navigationKey"
		:isShowBadge="isShowBadge"
		:isShowAllBadge="isShowAllBadge"
	></list-navigation>
</template>

<script setup lang="tsx">
import { ref, reactive, watchEffect } from 'vue';
/** 左侧导航栏相关 */
import { ListItem } from '@/components/navigation/list-navigation.vue';
// 配置
const showAllBadge = false; // 为true时，即使num = 0也会显示
const data: ListItem[] = [
	{
		icon: 'icon-settings-4-line',
		title: '示例种类1',
		num: 12,
		key: 'key1',
	},
	{
		icon: 'icon-calendar-line',
		title: '示例种类2-超长示例超长示例超长示例超长示例超长示例',
		num: 102,
		key: 'key2',
	},
	{
		icon: 'icon-time-line',
		title: '示例种类3',
		num: 0,
		key: 'key3',
	},
];
const navigationKey = ref(data[0].key);
watchEffect(() => {
	console.log('左侧导航Key值变动:', navigationKey.value);
});

function handleBadgeClick(t) {
	console.log('badge 被点击：', t);
}

const isShowBadge = true;
const isShowAllBadge = true; // 为 false时， 数据为0时不显示
</script>
<style lang="scss" scoped></style>
