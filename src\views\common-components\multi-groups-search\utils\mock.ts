
import { resolveFunc } from "@/utils/axios";
import type * as CategoryTypes from '@/components/multi-groups-search/utils/types';
export function getYearListApi() {
    return resolveFunc<CategoryTypes.CategoryListResponse>({
        "code": 0,
        "data": [
            { name: '2024', code: '2024' },
            { name: '2023', code: '2023' },
            { name: '2022', code: '2022' },
            { name: '2021', code: '2021' },
            { name: '2020', code: '2020' },
            { name: '2019', code: '2019' },
            { name: '2018', code: '2018' },
            { name: '2017', code: '2017' },
            { name: '2016', code: '2016' },
            { name: '2015', code: '2015' },
            { name: '2014', code: '2014' },
            { name: '2013', code: '2013' },
            { name: '2012', code: '2012' },
            { name: '2011', code: '2011' },
            { name: '2010', code: '2010' },
            { name: '2009', code: '2009' },
            { name: '2008', code: '2008' },
            { name: '2007', code: '2007' },
            { name: '2006', code: '2006' },
            { name: '2005', code: '2005' },
        ],
        "message": "操作成功!"
    })
}
export function getMonthListApi() {
    return resolveFunc<CategoryTypes.CategoryListResponse>({
        "code": 0,
        "data": [
            { name: '1月', code: '1' },
            { name: '2月', code: '2' },
            { name: '3月', code: '3' },
            { name: '4月', code: '4' },
            { name: '5月', code: '5' },
            { name: '6月', code: '6' },
            { name: '7月', code: '7' },
            { name: '8月', code: '8' },
            { name: '9月', code: '9' },
            { name: '10月', code: '10' },
            { name: '11月', code: '11' },
            { name: '12月', code: '12' },
        ],
        "message": "操作成功!"
    })
}
export function getBusinessListApi() {
    return resolveFunc<CategoryTypes.CategoryListResponse>({
        "code": 0,
        "data": [
            { name: '第一工程事业部', code: 'business-1' },
            { name: '第二工程事业部', code: 'business-2' },
            { name: '第三工程事业部', code: 'business-3' },
            { name: '第四工程事业部', code: 'business-4' },
            { name: '第五工程事业部', code: 'business-5' },
            { name: '第六工程事业部', code: 'business-6' },
            { name: '第七工程事业部', code: 'business-7' },
            { name: '第八工程事业部', code: 'business-8' },
            { name: '第九工程事业部', code: 'business-9' },
            { name: '第十工程事业部', code: 'business-10' },
            { name: '第十一工程事业部', code: 'business-11' },
            { name: '第十二工程事业部', code: 'business-12' },
            { name: '第十三工程事业部', code: 'business-13' },
            { name: '第十四工程事业部', code: 'business-14' },
            { name: '第十五工程事业部', code: 'business-15' },
            { name: '第十六工程事业部', code: 'business-16' },
            { name: '第十七工程事业部', code: 'business-17' },
            { name: '第十八工程事业部', code: 'business-18' },

        ],
        "message": "操作成功!"
    })
}
export function getReportFormListApi() {
    return resolveFunc<CategoryTypes.CategoryListResponse>({
        "code": 0,
        "data": [
            { name: '安全生产隐患排查治理表', code: 'report-form-1' },
            { name: '安全生产风险控制表', code: 'report-form-2' },
            { name: '安全生产事故统计表', code: 'report-form-3' },
            { name: '安全生产基本信息统计表', code: 'report-form-4' },
            { name: '安全生产xxxxx统计表5', code: 'report-form-5' },
            { name: '安全生产xxxxx统计表6', code: 'report-form-6' },
            { name: '安全生产xxxxx统计表7', code: 'report-form-7' },
            { name: '安全生产xxxxx统计表8', code: 'report-form-8' },
            { name: '安全生产xxxxx统计表9', code: 'report-form-9' },
            { name: '安全生产xxxxx统计表10', code: 'report-form-10' },
            { name: '安全生产xxxxx统计表11', code: 'report-form-11' },
            { name: '安全生产xxxxx统计表12', code: 'report-form-12' },
            { name: '安全生产xxxxx统计表13', code: 'report-form-13' },
            { name: '安全生产xxxxx统计表14', code: 'report-form-14' },
            { name: '安全生产xxxxx统计表15', code: 'report-form-15' },
            { name: '安全生产xxxxx统计表16', code: 'report-form-16' },
            { name: '安全生产xxxxx统计表17', code: 'report-form-17' },
            { name: '安全生产xxxxx统计表18', code: 'report-form-18' },

        ],
        "message": "操作成功!"
    })
}
