<template>
	<div class="table-date-filter">
		<div class="describe-text">添加筛选条件</div>
		<div class="title-text">{{ titleText }}</div>
		<el-select
			@change="handleChange"
			v-if="showSelect"
			:teleported="false"
			v-model="selectValue"
			placeholder="请选择"
			class="search-type-select"
		>
			<el-option
				v-for="item in options"
				:key="item.value"
				:label="item.label"
				:value="item.value"
			/>
		</el-select>
		<div class="date-box">
			<el-date-picker
				format="YYYY/MM/DD"
				value-format="YYYY-MM-DD"
				:teleported="false"
				v-model="dateValue"
				popper-class="date-picker-view"
				:type="selectValue === 'between' ? 'daterange' : 'date'"
				placeholder="请选择"
				start-placeholder="请选择"
				end-placeholder="请选择"
			/>
		</div>
		<div class="btn-box">
			<el-button
				@click="handleClick"
				class="submit-btn"
				type="primary"
				size="small"
				>确定</el-button
			>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
interface Props {
	value: string;
	titleText: string;
	searchRule?: string | boolean; // 查询匹配规则。父组件传入，根据此规则，生成查询匹配规则
	rule?: string | boolean; // 匹配规则下拉框内容
}

const props = withDefaults(defineProps<Props>(), {
	titleText: '',
	searchRule: 'between',
	rule: '',
});
const emit = defineEmits<{
	(ev: 'update:value', value: string): void;
	(ev: 'update:rule', rule: string): void;
	(ev: 'search'): void; //
}>();

const showSelect = ref(false); // 是否展示下拉框
const options = [
	{
		value: 'equal',
		label: '等于',
	},
	{
		value: 'noEqual',
		label: '不等于',
	},
	{
		value: 'greaterOrEqual',
		label: '大于等于',
	},
	{
		value: 'lessOrEqual',
		label: '小于等于',
	},
	{
		value: 'between',
		label: '介于',
	},
];

// 下拉框，搜索规则
const selectValue = ref();

// 日期框内容
const dateValue = ref<any>('');

watch(
	() => props.value,
	(val) => {
		if (val) {
			let dateArr = val.split(',');
			dateValue.value = dateArr.length > 1 ? dateArr : dateArr[0];
		} else {
			dateValue.value = '';
		}
	},
);

watch(
	() => props.searchRule,
	(val) => {
		// 为true，展示查询规则下拉框
		if (val === true) {
			showSelect.value = true;
			selectValue.value = 'between';
		} else if (val === false) {
			// 为false，不展示下拉框，默认为介于规则
			showSelect.value = false;
			selectValue.value = 'between';
		} else {
			// 其他枚举字段，直接赋值即可
			showSelect.value = false;
			selectValue.value = val ? val : 'between';
		}
	},
	{
		immediate: true,
	},
);

function handleClick() {
	if (dateValue.value && typeof dateValue.value === 'object') {
		emit('update:value', dateValue.value.join());
	} else {
		emit('update:value', dateValue.value ?? '');
	}

	emit('update:rule', selectValue.value);

	emit('search');
}

function handleChange() {
	dateValue.value = '';
}
</script>
<style lang="scss" scoped>
.n-t-input-box {
	flex-shrink: 0;
	width: auto;
}
.t-input-icon {
	order: 1;
	cursor: pointer;
}

.table-date-filter {
	padding: 0 12px 12px 12px;
	.describe-text {
		font-family: MicrosoftYaHei;
		font-size: 12px;
		color: #86909c;
		line-height: 20px;
		height: 34px;
		padding: 10px 0 4px 0;
		box-sizing: border-box;
	}

	.title-text {
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		font-size: 14px;
		color: #1d2129;
		line-height: 22px;
		margin-bottom: 6px;
	}

	.search-type-select {
		margin-bottom: 12px;
	}

	.btn-box {
		text-align: right;

		.submit-btn {
			margin-top: 12px;
			height: 28px;
		}
	}
}
</style>
<style lang="scss">
.date-box {
	.el-date-editor--date {
		width: 100%;
	}

	.el-date-editor--daterange {
		width: 100%;
		// max-width: 185px;
	}
}
</style>
