// 业务类型表单数据接口
export interface BusinessTypeForm {
	id?: string;
	categoryCode: string;
	categoryName: string;
	businessDescription: string;
	relatedForm: string;
	relatedProcess: string;
	signTableId: string;
	sortNumber: number | undefined;
	remark: string;
}

// 创建或更新业务类型请求数据
export interface CreateOrUpdateBusinessTypeRequest extends BusinessTypeForm {}

// 业务类型列表请求参数
export interface BusinessTypeListRequest {
	currentPage?: number;
	size?: number;
	categoryCode?: string;
	categoryName?: string;
}

// 业务类型列表响应数据
export interface BusinessTypeListResponse {
	code: number;
	message: string;
	data: {
		list: BusinessTypeForm[];
		total: number;
	};
}

// 下拉选项接口
export interface SelectOption {
	label: string;
	value: string;
}

// 表单选项响应
export interface FormOptionsResponse {
	code: number;
	message: string;
	data: SelectOption[];
}

// 流程选项响应
export interface ProcessOptionsResponse {
	code: number;
	message: string;
	data: SelectOption[];
}

// 通用响应接口
export interface CommonResponse {
	code: number;
	message: string;
	data?: any;
}