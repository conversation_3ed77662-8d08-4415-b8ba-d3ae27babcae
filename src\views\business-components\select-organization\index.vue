<template>
	<div class="container">
		<el-row>
			<el-col :span="24">
				<el-card class="card-view">
					<template #header>
						<div class="card-header">
							<span>选择组织机构组件</span>
							<el-button
								type="primary"
								@click="selsctOrganization"
								>选择组织机构</el-button
							>
						</div>
					</template>
					<div>
						<div>1、使用时，请在public/config.json文件下，配置“VITE_SELECT_ORGANIZATION_MEMBER”</div>
						<div>2、弹窗内接口需要正式有效token，请保证本地运行时token有效</div>
						<div>3、selectUserOrOrganizationList为回显勾选过的组织机构</div>
						<img
							class="example-img"
							src="./assets/example.png"
							alt=""
							srcset=""
						/>
					</div>
				</el-card>
			</el-col>
		</el-row>
		<select-organization-member
			v-model="dialogVisible"
			:selectType="selectType"
			:organizationDefaultSelects="organizationDefaultSelects"
			@handleConfirm="handleConfirm"
		/>
	</div>
</template>
<script lang="tsx" setup>
import { type selectUserOrOrganization } from '@/components/select-organization-member/index.vue';
import { ref } from 'vue';
type selectType = 'personnel' | 'organization';
const selectType = ref<selectType>('organization');
const dialogVisible = ref<boolean>(false);
//默认选中的数据
const organizationDefaultSelects = ref<selectUserOrOrganization[]>([
	{
		uuid: '66b96fd752faff0007d889dc',
		name: '国机工程集团',
	},
	{
		uuid: '66c6d07c46e0fb0001614353',
		name: '京外全资子公司',
	},
	{
		uuid: '66c6d08246e0fb00016148cb',
		name: '中国自控系统工程有限公司及下属机构',
	},
	{
		uuid: '66d82e97e8288600081dfa37',
		name: '技术研发平台',
	},
	{
		uuid: '66f287ece82886000719b002',
		name: '运行管理部',
	},
	{
		uuid: '66c86f67e8288600081be51a',
		name: 'wfj测试三级组织',
	},
	{
		uuid: '66c6d07d46e0fb00016143c9',
		name: '信息化办公室',
	},
	{
		uuid: '66c6d07c46e0fb0001614343',
		name: '中国机械设备工程股份有限公司',
	},
	{
		uuid: '66c6d07c46e0fb0001614345',
		name: '区域平台',
	},
	{
		uuid: '66c6d07c46e0fb0001614347',
		name: '公司本部',
	},
	{
		uuid: '66c6d07c46e0fb0001614369',
		name: '总经理助理',
	},
	{
		uuid: '66c6d07c46e0fb000161436b',
		name: '国机工程集团管理委员会',
	},
	{
		uuid: '66c6d07c46e0fb000161436d',
		name: '董事会',
	},
	{
		uuid: '66c6d07c46e0fb000161436f',
		name: '公司投资总监',
	},
	{
		uuid: '66c6d07c46e0fb0001614371',
		name: '职能部门',
	},
	{
		uuid: '66c6d07c46e0fb000161437f',
		name: '其他',
	},
	{
		uuid: '66c6d07c46e0fb0001614381',
		name: '投资管理部',
	},
	{
		uuid: '66c6d07c46e0fb0001614383',
		name: '运行管理部',
	},
	{
		uuid: '66c6d07c46e0fb0001614385',
		name: '巡察工作领导小组办公室',
	},
	{
		uuid: '66c6d07c46e0fb0001614387',
		name: '人力资源部（党委组织部）',
	},
	{
		uuid: '66c6d07c46e0fb0001614389',
		name: '离职',
	},
	{
		uuid: '66c6d07c46e0fb000161438b',
		name: '纪委办公室（巡察办）',
	},
	{
		uuid: '66c6d07d46e0fb000161438d',
		name: '党委工作部',
	},
	{
		uuid: '66c6d07d46e0fb000161438f',
		name: '安全生产部',
	},
	{
		uuid: '66c6d07d46e0fb0001614391',
		name: '综合管理部（党委办公室、董事会办公室）',
	},
	{
		uuid: '66c6d07d46e0fb00016143cb',
		name: '保卫处',
	},
	{
		uuid: '66c6d07d46e0fb0001614393',
		name: '法律与风险管理部',
	},
	{
		uuid: '66c6d07d46e0fb00016143a3',
		name: '战略发展与协同管理部',
	},
	{
		uuid: '66c6d07d46e0fb00016143a5',
		name: '融资部',
	},
	{
		uuid: '66c6d07d46e0fb00016143a7',
		name: '财务管理部',
	},
	{
		uuid: '66d921ede8288600081e139a',
		name: '考评测试职能部门',
	},
	{
		uuid: '66c6d07c46e0fb0001614373',
		name: '区域中心',
	},
	{
		uuid: '66c6d07d46e0fb00016143a1',
		name: '离退休',
	},
	{
		uuid: '66c6d07d46e0fb000161439f',
		name: '投资资产管理部',
	},
	{
		uuid: '66c6d07d46e0fb000161439d',
		name: '工程成套管理部',
	},
	{
		uuid: '66c6d07d46e0fb000161439b',
		name: '审计部',
	},
	{
		uuid: '66c6d07d46e0fb0001614399',
		name: '老干部工作部',
	},
	{
		uuid: '66c6d07d46e0fb0001614397',
		name: '设计咨询与科技质量管理部',
	},
	{
		uuid: '66c6d07d46e0fb0001614395',
		name: '出口管制办公室',
	},
	{
		uuid: '66c6d07c46e0fb0001614375',
		name: '公司顾问',
	},
	{
		uuid: '66c6d07c46e0fb0001614377',
		name: '公司总工程师',
	},
	{
		uuid: '66c6d07c46e0fb0001614379',
		name: '公司领导班子',
	},
	{
		uuid: '66c6d07c46e0fb000161437b',
		name: '监事会',
	},
	{
		uuid: '66c6d07c46e0fb000161437d',
		name: '事业部',
	},
	{
		uuid: '66c6d07c46e0fb0001614349',
		name: '京内全资子公司',
	},
	{
		uuid: '66c6d07c46e0fb000161434b',
		name: '境外子公司',
	},
	{
		uuid: '66c6d07c46e0fb000161434d',
		name: '京内控股子公司',
	},
	{
		uuid: '66c6d07c46e0fb000161434f',
		name: '境内其他机构',
	},
	{
		uuid: '66c6d07c46e0fb0001614351',
		name: '境外代表处',
	},
	{
		uuid: '66c6d08046e0fb00016146cd',
		name: '机械工业勘察设计研究院有限公司',
	},
	{
		uuid: '66c6d08046e0fb00016146cf',
		name: '上海中经进出口有限责任公司',
	},
	{
		uuid: '66c6d08046e0fb00016146d1',
		name: '中设（苏州）机械设备工程有限公司',
	},
	{
		uuid: '66c6d08046e0fb00016146d3',
		name: '中国机械设备工程（银川综合保税）有限公司',
	},
	{
		uuid: '66c6d08046e0fb00016146d5',
		name: '中设无锡机械设备工程有限公司',
	},
]);
const selsctOrganization = () => {
	dialogVisible.value = true;
};
const handleConfirm = (selectList: any) => {
	console.log(selectList, 'selectList======选择的人员或者组织机构');
};
</script>
<style lang="scss" scoped>
.container {
	padding: 20px;

	.card-view {
		margin-bottom: 20px;
	}

	.example-img {
		width: 700px;
		margin-top: 20px;
	}
}
</style>
