<template>
	<operation-tree
		ref="operationTreeRef"
		treeType="tree"
		:treeList="treeList"
		:show-add-btn="true"
		nodeKey="uuid"
		:props="defaultProps"
		:render-after-expand="true"
		:default-expanded-keys="defaultExpandKeys"
		@node-click="nodeClick"
		@header-add-click="headerAddClick"
	>
		<template #btnTabs>
			<ButtonTabs
				v-model:activeButton="activeButton"
				buttonType="middle"
				:buttonList="buttonList"
				@button-change="buttonChange"
			/>
		</template>
		<template #dropdown="{ node, data }">
			<el-dropdown
				placement="bottom"
				trigger="hover"
				:teleported="false"
				@command="handleDropdownCommand"
			>
				<span class="el-dropdown-link">
					<el-icon class="el-icon--right">
						<MoreFilled />
					</el-icon>
				</span>
				<template #dropdown>
					<el-dropdown-menu>
						<el-dropdown-item
							v-for="item in operationList"
							:key="item.actionCode"
							:divided="item.divided"
							:disabled="item.disabled"
							:icon="item.icon"
							:command="{
								actionCode: item.actionCode,
								actionLabel: item.actionLabel,
								node,
								data,
							}"
							>{{ item.actionLabel }}</el-dropdown-item
						>
					</el-dropdown-menu>
				</template>
			</el-dropdown>
		</template>
	</operation-tree>
</template>
<script setup lang="tsx">
import { ref, reactive, onMounted, nextTick, computed } from 'vue';
import type Node from 'element-plus/es/components/tree/src/model/node';
import type * as TreeTypes from '../utils/types';
import { getTreeListApi } from '../utils/mock';

const operationTreeRef = ref();
const treeList = ref<TreeTypes.TreeListItem[]>([]); //树形列表数据
//设置tree默认props
const defaultProps = ref<TreeTypes.DefaultProps>({
	label: 'name',
	children: 'children',
});
//默认展开
const defaultExpandKeys = ref<string[]>([]);

const activeButton = ref<string>('tree');
const buttonList = ref<TreeTypes.ButtonItem[]>([
	{ label: '树形列表', key: 'tree' },
	{ label: '纯列表', key: 'list' },
]);

onMounted(() => {
	getTreeList();
});

const getTreeList = () => {
	getTreeListApi().then((res) => {
		if (res.code === 0) {
			treeList.value = res.data;
			nextTick(() => {
				defaultExpandKeys.value = [res.data[0].uuid!]; //默认展开
				operationTreeRef.value.setCurrentKey(res.data[0].uuid!); //设置选中
			});
		}
	});
};

const operationList = ref<TreeTypes.OperationItem[]>([
	{ actionCode: 'add', actionLabel: '添加子组织' },
	{ actionCode: 'edit', actionLabel: '修改组织' },
	{ actionCode: 'del', actionLabel: '删除' },
	{ actionCode: 'moveUp', actionLabel: '上移' },
	{ actionCode: 'moveDown', actionLabel: '下移' },
]);

const headerAddClick = () => {
	console.log('点击头部新增按钮');
};
const nodeClick = (data: TreeTypes.TreeListItem, node: Node) => {
	console.log(data, node, '点击节点');
};
const buttonChange = (val: string | number) => {
	console.log(val, '按钮组切换');
};

const handleDropdownCommand = (command: string | number | object) => {
	console.log(command, '点击下拉菜单');
};
</script>
<style lang="scss" scoped></style>
