// 一些公共的 接口
import { isDev, envConfig } from '@/config';
import http, { EnumAim, Result } from '@/utils/axios';
import loginData from '@/mock/login-data';
import { ElMessageBox } from 'element-plus';
import { setStoreAuthorities, setStoreUserInfo } from '@/store';
import { setTokenLocal } from '@/utils/token';
import { sendLoginInvalidMsg, sendChangeOtherAppPageMsg } from '@/utils/message';
// import md5 from 'js-md5';

declare global {
	interface ResponseMenusRouters {
		authCode: string; //"MODULE_THIRD_PARTY_ORGANIZATION_CONTRACT_INFORMATION",
		name: string; //"第三方机构合同信息",
		type: number; //2
	}
	interface UserInfo {
		departmentName: string; //  " 检验认证管理中心",
		number: string; //  "64e34124cff47e000846ca24",
		departmentId: string; //  "6543575d52faff00083b0062",
		name: string; //  "贾佳",
		fullOrganizationPath: string; //  "root_organization_tenant_iip/64df1056cff47e0008e25939/64e3485dcff47e0008eec043/6543575d52faff00083b0062",
		telephone: string; //  "13903813433",
		enterpriseId: string; //  "64e3485dcff47e0008eec043",
		enterpriseName: string; //  "河南交院工程技术集团有限公司",
		username: string; //  "13903813433"
	}
	interface ResponseMenusInfo {
		user: UserInfo;
		routers: ResponseMenusRouters[];
		authorities: {
			role: string; // "ROLE_ADMIN"
		}[];
		token: {
			access_token: string; // 'bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..-';
			expires_in: number;
		};
	}
	interface RequestPassword {
		oldPassword: string;
		newPassword: string;
		confirmPassword: string;
	}
}

export function getMenus() {
	return Promise.resolve()
		.then(() => {
			if (isDev && 1) {
				window.localStorage.setItem('lastTimeToken', new Date().getTime() + ''); // 设置获取token的时间
				return Promise.resolve(loginData);
			}
			const url = envConfig.VITE_CAS_AUTH_URL + '/web/portal/menu/list';
			window.localStorage.setItem('lastTimeToken', new Date().getTime() + ''); // 设置获取token的时间
			return http.get(
				url,
				{
					appName: envConfig.VITE_APP_NAME,
					// queryEnhanceOrganizationInfo: 1,
				},
				{
					fullPath: true,
					useToken: false,
					errorTips: false,
					originData: true,
				},
			);
		})
		.then((info: Result<ResponseMenusInfo[]>) => {
			const { code, message, data } = info;
			if (code === http.resultCode.success) {
				const response = data?.[0]! || {};
				const { user, routers = [], authorities = [], token } = response;
				// if (!routers.length && !authorities.length) {
				// 	ElMessageBox({
				// 		type: 'error',
				// 		title: '错误提示',
				// 		showClose: false,
				// 		// 后台返回的错误信息可能需要换行，换行符为 \n 这里替换成 br
				// 		message: '该用户没有任何操作权限，请联系管理员配置权限后重新登录。',
				// 	}).then(() => {
				// 		// goToLogout(); // 调用登出接口,防止死循环
				// 		sendChangeOtherAppPageMsg('home');
				// 	});
				// 	return Promise.reject(message || '没有权限');
				// }
				// setStoreAuthorities(authorities.map((item) => item.role));
				setStoreUserInfo(user);
				// 用来判断登录超时时是否需要提示  只有用户点击登录后，才提示登录超时，用户刷新页面时，不提示，直接跳转到登录页面
				setTokenLocal(token.access_token);

				// return routers;
				return http.get(
					'/open-api/eepbase/v1/user/menu/list',
					{
						names: '通用审批,印章管理',
						subSystemNumber: 205,
					},
					{ useIDAAS: true, aim:EnumAim.idaas, ignoreGlobalAim: true, successTips: false, errorTips: false },
				);
			} else if (code === http.resultCode.not_login) {
				// 用户未登录，跳转到登录页面
				goToLogin();
			} else if (code === 1) {
				// code 为 1 时 要触发退出登录
				goToLogout();
			}
			return Promise.reject(message || '获取用户菜单失败，请刷新重试');
		});
}
let timer: any;
// 跳转到登录页面
export function goToLogin() {
	// const url = envConfig.VITE_CAS_AUTH_URL + '?appName=' + envConfig.VITE_APP_NAME;
	// console.log('即将跳转的页面', url);
	// if (isDev && 1) {
	// 	// 打开新窗口时，可能会同时打开多个，这里加个防抖
	// 	clearTimeout(timer);
	// 	timer = setTimeout(() => {
	// 		window.open(url);
	// 	}, 100);
	// } else {
	// 	window.location.href = url;
	// }

	// DOING 子系统不支持直接退出，向门户发送消息
	sendLoginInvalidMsg();
}
/**
 * @description: 用户登出
 */
export function goToLogout() {
	setTokenLocal();
	// setTimeout(() => {
	// 	const url = envConfig.VITE_CAS_AUTH_URL + '?appName=' + envConfig.VITE_APP_NAME;
	// 	const url1 = `${envConfig.VITE_CAS_LOGOUT_URL}?service=${url}`;
	// 	window.location.href = url1;
	// }, 20);

	// DOING 子系统不支持直接退出，向门户发送消息
	sendLoginInvalidMsg();
}

export function api_changePassword(params: RequestPassword) {
	const url = envConfig.VITE_CAS_AUTH_URL + '/web/portal/account/password';
	return http.put(
		url,
		{
			oldPassword: params.oldPassword,
			newPassword: params.newPassword,
			confirmPassword: params.confirmPassword,
		},
		{
			fullPath: true,
			loading: true,
			successTips: true,
			useToken: false,
		},
	);
}

export function refreshMenus() {
	return Promise.resolve()
		.then(() => {
			if (isDev && 1) {
				window.localStorage.setItem('lastTimeToken', new Date().getTime() + ''); // 设置获取token的时间
				return Promise.resolve(loginData);
			}
			const url = envConfig.VITE_CAS_AUTH_URL + '/web/portal/menu/list';
			window.localStorage.setItem('lastTimeToken', new Date().getTime() + ''); // 设置获取token的时间
			return http.get(
				url,
				{
					appName: envConfig.VITE_APP_NAME,
					// queryEnhanceOrganizationInfo: 1,
				},
				{
					fullPath: true,
					useToken: false,
					errorTips: false,
					originData: true,
				},
			);
		})
		.then((info: Result<ResponseMenusInfo[]>) => {
			const { code, message, data } = info;
			if (code === http.resultCode.success) {
				const response = data?.[0]! || {};
				const { user, routers = [], authorities = [], token } = response;
				setStoreAuthorities(authorities.map((item) => item.role));
				setStoreUserInfo(user);
				// 用来判断登录超时时是否需要提示  只有用户点击登录后，才提示登录超时，用户刷新页面时，不提示，直接跳转到登录页面
				setTokenLocal(token.access_token);
				return routers;
			} else if (code === http.resultCode.not_login) {
				// 用户未登录，跳转到登录页面
				goToLogin();
			} else if (code === 1) {
				// code 为 1 时 要触发退出登录
				goToLogout();
			}
			return Promise.reject(message);
		});
}
