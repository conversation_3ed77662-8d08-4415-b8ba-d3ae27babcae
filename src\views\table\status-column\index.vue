<template>
	<header-main>
		<template #main>
			<div class="tag-box">
				<el-tag
					type="primary"
					class="xirui-primary-tag"
					>primary</el-tag
				>
				<el-tag
					type="success"
					class="xirui-success-tag"
					>success</el-tag
				>
				<el-tag
					type="info"
					class="xirui-info-tag"
					>info</el-tag
				>
				<el-tag
					type="warning"
					class="xirui-warning-tag"
					>warning</el-tag
				>
				<el-tag
					type="danger"
					class="xirui-danger-tag"
					>danger</el-tag
				>
				<el-tag class="xirui-other-tag">other</el-tag>
			</div>
			<common-table
				v-bind="pageInfo"
				:loading="loading"
				:createButtonText="createButtonText"
				:createButtonOptions="createButtonOptions"
				:pagination="true"
				:columns="tableColumns"
				:fill-screen="true"
				:data="tableData"
				@pageChange="onPageChange"
				@onClickCreateBtn="onClickCreateBtn"
				@cell-click="handleCellClick"
			>
				<template #slotStatus="{ row, column, $index }">
					<el-tag
						v-if="row.status"
						type="success"
						class="xirui-success-tag"
						>有效</el-tag
					>
					<el-tag
						v-else
						type="danger"
						class="xirui-danger-tag"
						>无效</el-tag
					>
				</template>
			</common-table>

			<user-manage-dialog
				:initFormData="initFormData"
				v-model:visible="disalogVisible"
				:viewType="dialogViewType"
				@refreshList="refreshList"
			/>
		</template>
	</header-main>
</template>

<script lang="tsx" setup>
import { onMounted, ref, reactive } from 'vue';
import userManageDialog from './views/user-manage-dialog.vue';
import { cloneDeep } from 'lodash-es';
import { ElMessage } from 'element-plus';
import { type TableData } from './utils/types';
// import { getTableDataApi } from './utils/mock';
import { deleteTableDataApi, getTableDataApi } from './utils/api';
// #region 表格参数定义
// 表格loading
const loading = ref(false);

// 表格分页查询条件
const pageInfo = reactive<PageInfo>({
	total: 0,
	pageNumber: 1,
	pageSize: 20,
});

// 表格数据
const tableData = ref<TableData[]>([]);

// 表格列定义
const tableColumns: CommonTableColumn<TableData>[] = [
	{
		label: '用户名',
		prop: 'username',
		searchType: 'input', // 不配置searchRule字段，查询规则执行默认【contain 包含】
		hoverHighLight: true,
	},
	{
		label: '手机号',
		prop: 'phone',
		searchType: 'input',
		searchRule: false, // 配置为false，查询规则执行默认【contain 包含】
		hoverHighLight: true,
	},
	{
		label: '角色',
		prop: 'roles',
		searchType: 'input',
		searchRule: true, // 配置为true，查询规则为下拉框，手动选择
	},

	{
		label: '邮箱',
		prop: 'email',
		searchType: 'input',
		searchRule: 'equal', // 配置为枚举字段，查询规则为传入值。不展示下拉框
	},
	{
		label: '创建时间',
		prop: 'createTime',
		openSort: true,
		searchType: 'input',
	},
	{
		label: '状态',
		prop: 'status',
		slotName: 'slotStatus',
		width: 100,
		align: 'center'
	},
	{
		label: '操作',
		width: 120,
		formatter: (row, column, cellValue, index) => {
			const btns: OptionBtn[] = [
				{
					label: '编辑',
					onClick() {
						initFormData.value = cloneDeep(row);
						dialogViewType.value = 'edit';
						disalogVisible.value = true;
					},
				},
				{
					label: '删除',
					tips: '确定删除吗？',
					placement: 'left-start',
					onClick() {
						deleteTableDataApi(row.id).then(() => {
							ElMessage.success('删除成功');
							getList();
						});
					},
				},
			];

			return <i-btns btns={btns}></i-btns>;
		},
	},
];
// #endregion

// 初始化表格数据
onMounted(() => {
	getList();
});

// #region 列表交互逻辑
const getList = () => {
	loading.value = true;
	getTableDataApi({
		currentPage: pageInfo.pageNumber,
		size: pageInfo.pageSize,
	})
		.then((res) => {
			tableData.value = res.data.list;
			pageInfo.total = res.data.total;
		})
		.finally(() => {
			loading.value = false;
		});
};

const handleCellClick = (data) => {
	initFormData.value = cloneDeep(data);
	dialogViewType.value = 'view';
	disalogVisible.value = true;
};

// 分页器改变
const onPageChange = (pageNumber, pageSize) => {
	pageInfo.pageNumber = pageNumber;
	pageInfo.pageSize = pageSize;
	getList();
};

const refreshList = () => {
	pageInfo.pageNumber = 1;
	getList();
};
// #endregion

// #region 用户新增、修改逻辑
// 弹窗类型
const dialogViewType = ref<string>('');

// 弹窗显隐
const disalogVisible = ref<boolean>(false);

// 表单初始内容
const initFormData = ref<any>({});
// #endregion

// #region 新建按钮逻辑
const createButtonText = ref('创建任务');
const createButtonOptions: CommonTableOptions[] = [
	{
		label: '批量新建',
		value: 'batchCreate',
	},
	{
		label: '模板导入',
		value: 'templateImport',
	},
];

const onClickCreateBtn = (type) => {
	console.log('点击新增按钮', type);
	dialogViewType.value = 'add';
	disalogVisible.value = true;
};
// #endregion
</script>
<style lang="scss" scoped>
.tag-box {
	height: 50px;
	padding: 0 20px;
	display: flex;
	align-items: center;
	.el-tag {
		margin-right: 20px;
	}
}
</style>
