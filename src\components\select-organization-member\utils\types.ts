//获取标签列表入参定义
export interface LabelListRequest {
  tenantId: string;
}
//根据标签uuid查询组织列表入参定义
export interface OrganizationListRequest {
  labelUuid: string;
}
//根据关键字搜索组织机构入参定义
export interface FilterOrganizationListRequest {
  organizationName?: string;
}
//查询用户列表入参定义
export interface UserListRequest {
  /**
   * 是否包含所有子层级的账户下的账户， 0:否，只获取直接子级的账户下的账户 | 1:是，获取所有子层级的账户下的账户，默认：1
   */
  includeAllSubLevels: number;
  /**
   * 查询关键字，支持账户名、显示名称、账户手机号、账户邮箱模糊查询
   */
  keywords?: string;
  /**
   * 每页数据个数
   */
  limit?: number;
  /**
   * 分页数据偏移量，注意不是页数
   */
  offset?: number;
  /**
   * 父级组织机构编号或外部编号，多个组织时逗号分开
   */
  parentOrganizationId?: string;
}

// 标签列表内单个数据格式
export interface LabelListItem {
  /**
   * 创建人员
   */
  createBy?: null | string;
  /**
   * 描述
   */
  description?: null | string;
  id?: number | null;
  /**
   * 标签名称
   */
  name?: null | string;
  /**
   * 包含组织个数
   */
  organizationNum?: number | null;
  /**
   * 排序
   */
  sort?: number | null;
  tenantId?: null | string;
  /**
   * type，暂时不填
   */
  type?: number | null;
  uuid?: string;
}
// 组织列表内单个数据格式
export interface OrganizationListItem {
  /**
   * 部门节点下的人员数量信息，包含子级部门的人员总和
   */
  accountCount?: number | null;
  /**
   * 子级列表
   */
  children?: OrganizationListItem[] | null;
  /**
   * 租户/组织/部门/账户组外部唯一编号
   */
  externalId?: null | string;
  /**
   * 层级
   */
  level?: number | null;
  /**
   * 名称
   */
  name?: null | string;
  /**
   * 上级编号
   */
  parentId?: null | string;
  /**
   * 节点类型，0：租户(主体) 1：组织(公司) 2：部门 3:账户组
   */
  type?: number | null;
  /**
   * 人员还是组织，1：人员 | 组织
   */
  userOrOrg?: number | null;
  /**
   * 租户/组织/部门/账户组唯一编号
   */
  uuid?: null | string;
}
// 搜索组织机构列表内单个数据格式
export interface FilterOrganizationListItem {
  uuid?: string;
  name?: string;
  parentPathName?: string;
}

//用户列表内单个数据格式
export interface UserListItem {
  /**
   * 账户名称
   */
  accountName?: null | string;
  /**
   * 账户密码
   */
  accountPassword?: null | string;
  /**
   * 账户类型，普通账户/管理员账户/开发者账户
   */
  accountType?: number | null;
  /**
   * 账户创建时间
   */
  createTime?: null | string;
  /**
   * 数据来源
   */
  dataSource?: number | null;
  /**
   * 账户邮箱
   */
  email?: null | string;
  /**
   * 账户过期时间
   */
  expirationTime?: null | string;
  /**
   * 账户过期状态（0/1）
   */
  expireState?: number | null;
  /**
   * 外部ID
   */
  externalId?: null | string;
  /**
   * 是否删除
   */
  isDelete?: number | null;
  /**
   * 是否是挂载，一个员工可以挂载在不同的企业
   */
  mount?: number | null;
  /**
   * 挂载的组织机构编号,多个组织机构编号间使用','隔开
   */
  mountOrganizationId?: null | string;
  /**
   * 显示名称
   */
  name?: null | string;
  /**
   * 隶属组织机构完整路径
   */
  parentOrganizationFullPath?: null | string;
  /**
   * 隶属组织机构编号
   */
  parentOrganizationId?: null | string;
  /**
   * 隶属组织机构名称
   */
  parentOrganizationName?: null | string;
  /**
   * 父级完整目录名称，不包含当前账户名称
   */
  parentPathName?: null | string;
  /**
   * 账户描述
   */
  remark?: null | string;
  /**
   * 排序
   */
  sort?: number | null;
  /**
   * 账户手机号
   */
  telephone?: null | string;
  /**
   * 租户编号
   */
  tenantId?: null | string;
  /**
   * 账户修改时间
   */
  updateTime?: null | string;
  /**
   * 账户唯一编号
   */
  uuid?: null | string;
  /**
   * 是否启用（0/1)
   */
  valid?: number | null;
  /**
   * 账户版本信息
   */
  version?: number | null;
}


// 标签列表接口返回值格式
export type LabelListResponse = ApiResponseData<LabelListItem[]>
// 组织列表接口返回值格式
export type OrganizationListResponse = ApiResponseData<OrganizationListItem[]>
// 搜索组织列表接口返回值格式
export type FilterOrganizationListResponse = ApiResponseData<FilterOrganizationListItem[]>
// 用户列表接口返回值格式
export type UserListResponse = ApiResponseData<UserListItem[]>

