<!--
  功能：文件上传组件
  作者：ligw
  时间：2024年08月08日 15:48:51
  版本：v1.0
  修改记录：
  修改内容：
  修改人员：
  修改时间：
-->
<template>
  <base-upload
    ref="uploadRef"
    class="file-upload-container"
    :class="{limit: fileList.length > limit}"
    v-model:file-list="fileList"
    :params="dataParams"
    :limit="limit"
    :accept="accept"
    :before-upload="handleBeforeUpload"
    :before-remove="handleBeforeRemove"
    :on-remove="handleRemove"
    :on-success="handleSuccess"
    :on-error="handleError"
    v-bind="$attrs"
  >
    <template #default>
      <slot></slot>
      <ElButton
        v-if="!$slots.default" 
        :loading="loading" 
        plain
      >
      <slot name="button"></slot>
      <span v-if="!$slots.button" >
        <el-icon><Upload /></el-icon>
        &nbsp;{{ title }}
      </span>
      </ElButton>
    </template>
  </base-upload>
</template>

<script lang="ts" setup>
import { ref, defineExpose, onMounted, computed } from 'vue'
import { MessageBox, Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BaseUpload from './base-upload.vue';

import type { UploadProps, UploadInstance, UploadFile, UploadFiles, UploadRawFile } from 'element-plus'
import Moment from 'moment';
import { deleteFile, generateFileUuid } from '@/api/file'
import { envConfig } from '@/config'
import { judgeFileType, formatPartParams } from '@/utils/file'

const model = defineModel()

export type FileResponse = ApiResponseData<FileItem[]> 
interface Props {
  accept?: string; // 文件类型，多个用英文,分隔
  limit?: number; //最大允许上传文件数量
  size?: number; //单文件最大限制，单位MB
  title?: string; // 上传按钮文案
  sucess?: Function; // 上传成功方法
  max?: number; //文件分片大小单位B，如3*1024*1024（3M）
}

const props = withDefaults(defineProps<Props>(), {
  files: () => [],
  limit: 3,
  accept: '.png',
  title: '点击上传'
})
const uploadRef = ref<UploadInstance>()
const dataParams = ref<ChunkParam>()
// 上传按钮加载状态，防止重复点击
const loading = ref(false)
onMounted(() => {
  initData()
})
const initData = () => {
  const data = model.value
  if (Array.isArray(data)) {
    fileList.value = data.map((item,index) => {
      const {fileName, fileAccessPath,uid} = item
      return {
        name: fileName,
        status: 'success',
        uid:  uid || Moment().valueOf(),
        url: envConfig.VITE_BASE_FILE_SATIC_PATH + fileAccessPath,
        response: {
          code: 0,
          data: [{ ...item }],
          message:''
        },
      }
    })
  } else {
    fileList.value = []
  } 
}
// 上传文件列表
const fileList = ref<UploadFile[]>([])

/**
 * 设置model数据
 */
const setModel = () => {
  model.value = fileList.value.map(item => {
    const response = item.response as FileResponse
    const result = response.data[0]
    return {
      file: item,
      ...result,
      uid: item.raw?.uid || Moment().valueOf(),
      mime: item.raw?.type
    }
  })
  
}

/**
 * 上传成功更新model
 * @param response 
 * @param uploadFile 
 * @param uploadFiles 
 */
const handleSuccess: UploadProps['onSuccess'] = (response: FileResponse, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  if (response?.code === 0) {
    setModel()
    if (props?.sucess) {
      props.sucess(response,uploadFile,uploadFiles)
    }
  } else {
    ElMessage.error(response?.message || '上传失败！')
    fileList.value?.pop()
  }
}

const handleError: UploadProps['onError'] = (error:Error, uploadFile: UploadFile) => {
  ElMessage.error(error?.message || '服务异常，请联系管理员！')
}

/**
 * 上传前校验文件类型、大小
 * @param file 
 */
const handleBeforeUpload: UploadProps['beforeUpload'] = async(file: UploadRawFile) => {
  const fileType = judgeFileType(file.type)
  if (fileType === "image" || fileType === 'video') {
    const imgType = file.type.split('/')[1]
    if (!props.accept.toLocaleLowerCase().includes(imgType.toLocaleLowerCase())) {
      ElMessage.warning(`请上传后缀为${props.accept}类型文件！`)
      return false
    }
  } else {
    const otherType = file.name.slice(file.name.lastIndexOf('.') + 1)
    if (!props.accept.toLocaleLowerCase().includes(otherType.toLocaleLowerCase())) {
      ElMessage.warning(`请上传后缀为${props.accept}类型文件！`)
      return false
    }
  }
  if (props.size) {
    const limitSize = file.size / 1024 / 1024
    if (limitSize>props.size) {
      ElMessage.warning(`上传文件不能超过${props.size}MB!`)
      return false
    }
  }
  // const res = await generateFileUuid() as ApiResponseData<string[]>
  // const fileUuid = res?.data?.[0]
   
  // dataParams.value = formatPartParams(file,fileUuid)
  return true
}

/**
 * 删除文件
 * @param file 
 * @param uploadFiles 
 */
const handleRemove: UploadProps['onRemove'] = (file, uploadFiles) => {
  setModel()
}

/**
 * 删除文件前确认操作，确认后调用远程服务删除已上传文件
 * @param uploadFile 
 * @param uploadFiles 
 */
const handleBeforeRemove: UploadProps['beforeRemove'] = (uploadFile: UploadFile, uploadFiles) => {
  if (uploadFile && uploadFile.status == 'success') { 
    return ElMessageBox.confirm('确定要删除吗？', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
    })
      .then(async () => {
        const response = uploadFile.response as FileResponse
        if (response?.code === 0) { 
          const result  = await deleteFile(response?.data[0]?.fileUuid)  as ApiResponseData<any>
          if (result?.code === 0) {
            ElMessage.success('删除成功！')
          }
          emit('delete', uploadFile)
          return result?.code === 0
        } else {
          return true
        }
      })
      .catch(() => false);
  } else {
    return true
  }
}
const emit = defineEmits<{
	(ev: 'delete', response: any): void;
}>();

defineExpose({
  uploadRef,
  initData
})
</script>
<style lang="scss" scoped>
  .file-upload-container{
    :deep(.el-upload-list__item){
      transition-duration: 0ms;
    }
   
  }
  .limit{
    :deep(.el-upload){
      display: none;
    }
  }
</style>

