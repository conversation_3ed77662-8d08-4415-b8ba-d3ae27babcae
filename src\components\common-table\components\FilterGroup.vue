<template>
	<div>
		<el-button ref="buttonRef" v-click-outside="onClickOutside">
			<IconFont type="icon-filter-line" />
			<span class="filter-btn-text">筛选</span>
		</el-button>
		<el-popover ref="popoverRef" :virtual-ref="buttonRef" trigger="click" width="800" virtual-triggering>
			<el-form ref="formRef" :model="dynamicValidateForm" label-width="auto" class="demo-dynamic">
				<div v-for="(childItem, childIndex) in dynamicValidateForm" :key="childIndex">
					<el-form-item
						v-for="(domain, index) in childItem.domains"
						:key="domain.key"
						:prop="'domains.' + index + '.value'"
						:rules="{
							required: true,
							message: 'domain can not be null',
							trigger: 'blur',
						}"
						class="custom-form-item"
					>
						<IconFont @click.prevent="removeDomain(domain, childItem.domains)" class="delete-icon" type="icon-indeterminate-circle-line" />
						<el-select :teleported="false" class="label-select" v-model="domain.value" placeholder="请选择">
							<el-option v-for="item in sortOptions" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
						<el-select :teleported="false" class="searchRule-select" v-model="domain.value" placeholder="请选择">
							<el-option v-for="item in sortOptions" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
						<el-select :teleported="false" class="value-box" v-model="domain.value" placeholder="请选择">
							<el-option v-for="item in sortOptions" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
					<el-form-item class="custom-add-form-item">
						<IconFont @click="addDomain(childItem.domains)" class="add-icon" type="icon-indeterminate-circle-line" />
						<span @click="addDomain(childItem.domains)" class="add-text">添加条件</span>
					</el-form-item>
					<div class="line-box">
						<div class="line-border"></div>
						<div class="line-text">或</div>
						<div class="line-border"></div>
					</div>
				</div>

				<el-form-item>
					<el-button type="primary" @click="handleAddFilterGroup()">添加条件组</el-button>
					<el-button type="primary" @click="submitForm(formRef)">Submit</el-button>
					<el-button @click="addDomain">New domain</el-button>
					<el-button @click="resetForm(formRef)">Reset</el-button>
				</el-form-item>
			</el-form>
		</el-popover>
	</div>
</template>

<script lang="tsx" setup>
import { ref, reactive, unref } from 'vue';
import { ClickOutside as vClickOutside } from 'element-plus';
import type { FormInstance } from 'element-plus';

// 打开弹出窗
const buttonRef = ref();
const popoverRef = ref();
const onClickOutside = () => {
	unref(popoverRef).popperRef?.delayHide?.();
};

// 动态表单
const formRef = ref<FormInstance>();
const dynamicValidateForm = reactive<
	[
		{
			domains: DomainItem[];
		},
	]
>([
	{
		domains: [
			{
				key: 1,
				value: '',
			},
		],
	},
]);

const sortOptions = [
	{
		value: 'desc',
		label: '倒序',
	},
	{
		value: 'asc',
		label: '正序',
	},
];

interface DomainItem {
	key: number;
	value: string;
}

const removeDomain = (item: DomainItem, list: DomainItem[]) => {
	const index = list.indexOf(item);
	if (index !== -1) {
		list.splice(index, 1);
	}
};

const addDomain = (item) => {
	item.push({
		key: Date.now(),
		value: '',
	});
};

const submitForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	formEl.validate((valid) => {
		if (valid) {
			console.log('submit!');
		} else {
			console.log('error submit!');
		}
	});
};

const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	formEl.resetFields();
};

// 添加条件组
function handleAddFilterGroup() {
	console.log('handleAddFilterGroup');

	dynamicValidateForm.push({
		domains: [
			{
				key: Date.now(),
				value: '',
			},
		],
	});
}
</script>
<style lang="scss" scoped>
.filter-btn-text {
	font-family: MicrosoftYaHei;
	font-size: 14px;
	color: #1d2129 !important;
}

.custom-add-form-item {
	margin-bottom: 0 !important;
	.add-icon {
		font-size: 16px;
		margin-right: 8px;
		cursor: pointer;
		color: var(--el-color-primary);
	}

	.add-text {
		cursor: pointer;
		font-family: MicrosoftYaHei;
		font-size: 14px;
		color: var(--el-color-primary);
		line-height: 22px;
	}
}

.custom-form-item {
	.delete-icon {
		font-size: 16px;
		margin-right: 8px;
		cursor: pointer;
	}

	.label-select {
		width: 144px;
		margin-right: 10px;
	}

	.searchRule-select {
		width: 144px;
		margin-right: 8px;
	}

	.value-box {
		width: 300px;
	}
}

.line-box {
	height: 22px;
	width: 100%;
	display: flex;
	align-items: center;
	margin-bottom: 12px;

	.line-text {
		font-family: MicrosoftYaHei;
		font-size: 14px;
		color: #1d2129;
		line-height: 22px;
		margin: 0 20px;
	}

	.line-border {
		height: 1px;
		flex: 1;
		background-color: #e5e6eb;
	}
}
</style>
