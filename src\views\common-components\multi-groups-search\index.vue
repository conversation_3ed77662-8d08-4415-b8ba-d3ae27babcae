<template>
	<div class="example-box">
		<multi-groups-search
			ref="categorySearchRef"
			v-model:categoryList="categoryList"
			v-model:selectList="selectList"
			v-model:height="multiGroupsHeight"
			:total="0"
			:showTotal="false"
			@select="selectItem"
			@clear="clearFilter"
		></multi-groups-search>
		<div
			class="example-content"
			:style="{ height: `calc(100% - ${multiGroupsHeight}px - 40px)` }"
		>
			我是内容区域
		</div>
	</div>
</template>

<script setup lang="ts">
import type * as CategoryTypes from '@/components/multi-groups-search/utils/types';
import { ref, watch, computed, Component, onMounted, nextTick } from 'vue';
import { getYearListApi, getMonthListApi, getBusinessListApi, getReportFormListApi } from './utils/mock';

const multiGroupsHeight = ref<number>(136);
const categorySearchRef = ref();
const categoryList = ref<CategoryTypes.CategoryListItem[]>([
	{ label: '事业部', type: 'business', list: [], showAll: true },
	{ label: '报表', type: 'reportForm', list: [], showAll: true },
	{ label: '年份', type: 'year', list: [], showAll: true },
	{ label: '月份', type: 'month', list: [], showAll: true },
]);
const selectList = ref<CategoryTypes.SelectListItem[]>([
	{ label: '事业部', type: 'business', name: '', code: '' },
	{ label: '报表', type: 'reportForm', name: '', code: '' },
	{ label: '年份', type: 'year', name: '', code: '' },
	{ label: '月份', type: 'month', name: '', code: '' },
]);

watch(multiGroupsHeight, (val) => {
	console.log(val, 'multiGroupsHeight========multiGroupsHeight=======multiGroupsHeight');
});
onMounted(() => {
	getCategoryList();
});
const selectItem = (item: CategoryTypes.SelectListItem, list: CategoryTypes.SelectListItem[]) => {
	console.log(item, list, '======selectItem======selectItem=======selectItem====111111111111111111111');
};
const clearFilter = (item: CategoryTypes.SelectListItem | null, list: CategoryTypes.SelectListItem[]) => {
	console.log(item, list, '======clearFilter======clearFilter=======clearFilter====111111111111111111111');
};
const getCategoryList = async () => {
	getYearList(null, '2015');
	getMonthList(null, '2');
	getBusinessList(null, 'business-18');
	getReportFormList(null, 'report-form-15');
};

const getBusinessList = async (params: any, selectCode: string) => {
	getBusinessListApi().then((res) => {
		if (res.code === 0) {
			categoryList.value[0].list = res.data;
			categorySearchRef.value.setRowSelect({ type: 'business', code: selectCode });
		}
	});
};
const getReportFormList = async (params: any, selectCode: string) => {
	getReportFormListApi().then((res) => {
		if (res.code === 0) {
			categoryList.value[1].list = res.data;
			categorySearchRef.value.setRowSelect({ type: 'reportForm', code: selectCode });
		}
	});
};
const getYearList = (params: any, selectCode: string) => {
	getYearListApi().then((res) => {
		if (res.code === 0) {
			categoryList.value[2].list = res.data;
			categorySearchRef.value.setRowSelect({ type: 'year', code: selectCode });
		}
	});
};
const getMonthList = (params: any, selectCode: string) => {
	getMonthListApi().then((res) => {
		if (res.code === 0) {
			categoryList.value[3].list = res.data;
			categorySearchRef.value.setRowSelect({ type: 'month', code: selectCode });
		}
	});
};
</script>

<style lang="scss" scoped>
.example-box {
	width: 100%;
	height: 100%;
}
.example-content {
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #e5e6eb;
	margin: 20px;
}
</style>
