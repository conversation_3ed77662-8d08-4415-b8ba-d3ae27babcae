// // prettier-ignore
module.exports = {
	printWidth: 135,
	tabWidth: 3,
	singleQuote: true, // 使用单引号代替双引号
	useTabs: true,
	semi: true, // 句尾添加分号
	singleAttributePerLine: true,
	arrowParens: 'always', //  (x) => {} 箭头函数参数只有一个时是否要有小括号。Expected "always" or "avoid", avoid：省略括号
	trailingComma: 'all', // 在对象或数组最后一个元素后面是否加逗号（在ES5中加尾逗号）
	bracketSpacing: true, // 在对象，数组括号与文字之间加空格 "{ foo: bar }"
	tslintIntegration: false, // 不让prettier使用tslint的代码格式进行校验
	eslintIntegration: false, //不让prettier使用eslint的代码格式进行校验
};
