<template>
	<div class="i-descriptions-box" ref="refDom" :style="{ 'background-color': gracyBackColor ? '#f0f2f5' : '#ffffff' }">
		<slot></slot>
	</div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
interface Props {
	column?: number; // 一行 Descriptions Item 的数量
	labelWidth?: number | string; // label 的宽度 暂时没用上，
	gracyBackColor?: boolean; // 是否使用灰色背景
}
const props = withDefaults(defineProps<Props>(), {
	column: 3,
	labelWidth: 'auto',
	gracyBackColor: true,
});
const refDom = ref<HTMLDivElement>();
onMounted(() => {
	reSizeLabel();
});
function reSizeLabel() {
	if (!refDom.value) return;
	let w_ = props.labelWidth;
	if (typeof props.labelWidth === 'number') {
		w_ = `${props.labelWidth}px`;
	}
	refDom.value.style.setProperty('--label-width', w_ as string);
	refDom.value.style.setProperty('--item-width', `${Math.floor((100 / props.column) * 100000) / 100000}%`);
}
watch(
	() => props.labelWidth,
	(val) => {
		reSizeLabel();
	},
	{
		immediate: true,
	},
);
</script>

<style lang="scss" scoped>
.i-descriptions-box {
	display: flex;
	align-items: stretch;
	flex-wrap: wrap;
	background-color: var(--el-fill-color);
	padding: 8px 0;
}
</style>
