<template>
	<el-dialog
		@closed="closeDialog"
		v-model="dialogVisible"
		title="示例标题"
		class="xirui-custom-common-dialog xirui-medium-dialog"
	>
		<div>
			<div
				v-for="o in 20"
				:key="o"
				class="text item"
			>
				{{ '列表内容 ' + o }}
			</div>
		</div>
		<template #footer>
			<el-button
				@click="onSumbit"
				type="primary"
				:loading="btnLoading"
				>保存</el-button
			>
			<el-button @click="dialogVisible = false">取消</el-button>
		</template>
	</el-dialog>
</template>

<script lang="tsx" setup>
import { watch, ref, defineProps, defineEmits } from 'vue';
// #region 参数定义
interface Props {
	// 弹窗显隐
	visible: boolean;
}
const props = withDefaults(defineProps<Props>(), {
	visible: false,
});

const emit = defineEmits<{
	(e: 'update:visible', value: boolean): void;
}>();

watch(
	() => props.visible,
	(val) => {
		dialogVisible.value = val || false;
	},
);
// #endregion

// 控制弹窗显隐
const dialogVisible = ref<boolean>(false);

// 提交按钮loading
const btnLoading = ref<boolean>(false);

const closeDialog = () => {
	emit('update:visible', false);
};

// 提交保存
const onSumbit = () => {
	closeDialog();
};
</script>
<style lang="scss" scoped></style>
