<template>
	<div
		class="button-tabs-box"
		:class="{ 'button-tabs-box-middle': buttonType === 'middle' }"
	>
		<div
			v-for="item in buttonList"
			:key="item.key"
			class="button-item"
			:class="{ 'button-item-active': activeBtn === item.key, 'button-item-middle': buttonType === 'middle' }"
			@click="changeBtn(item)"
		>
			{{ item.label }}
		</div>
	</div>
</template>
<script lang="ts" setup>
import { computed, ref } from 'vue';

type buttonType = 'middle' | 'default'; //middle 按钮平分,default按钮按照文字尺寸自动撑开
export interface ButtonItem {
	label?: string; //按钮名称
	key?: string | number; //按钮唯一标识
}
interface ButtonTabsProps {
	activeButton: string | number; //绑定值，按钮选中的 key
	buttonType?: buttonType;
	buttonList?: ButtonItem[];
}
const props = withDefaults(defineProps<ButtonTabsProps>(), {
	activeButton: '',
	buttonType: 'default',
	buttonList: () => {
		return [];
	},
});
type EmitProps = {
	(e: 'update:activeButton', val: string | number): void;
	(e: 'button-change', val: string | number): void;
};
const emits = defineEmits<EmitProps>();
const activeBtn = computed({
	get() {
		return props.activeButton;
	},
	set(val) {
		emits('update:activeButton', val);
	},
});
const changeBtn = (item: ButtonItem) => {
	console.log(activeBtn.value, item, '111122222222222===========999999999999');
	if (activeBtn.value === item.key) {
		return;
	}
	const newActiveBtn = item.key!;
	emits('update:activeButton', newActiveBtn);
	emits('button-change', newActiveBtn);
};
</script>
<style lang="scss" scoped>
.button-tabs-box {
	width: auto;
	height: auto;
	background: #e5e6eb;
	border-radius: 3px;
	padding: 3px;
	display: inline-flex;
	align-items: center;
	.button-item {
		cursor: pointer;
		height: 26px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #1d2129;
		line-height: 26px;
		padding: 0 10px;
		transition: all 0.28s;
		text-align: center;
	}
	.button-item-active {
		background: #ffffff;
		box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);
		border-radius: 3px;
	}
	.button-item-middle {
		width: 50%;
	}
}
.button-tabs-box-middle {
	display: flex;
}
</style>
