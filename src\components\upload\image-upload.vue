<!--
  功能：图片上传组件
  作者：ligw
  时间：2024年08月08日 15:48:51
  版本：v1.0
  修改记录：
  修改内容：
  修改人员：
  修改时间：
-->
<template>
  <base-upload
    class="image-upload-container"
    v-model:file-list="fileList"
    list-type="picture-card"
    :limit="limit"
    :accept="accept"
    :before-upload="handleBeforeUpload"
    :on-preview="handlePictureCardPreview"
    :before-remove="handleBeforeRemove"
    :on-remove="handleRemove"
    :on-success="handleSuccess"
  >
    <el-icon><Plus /></el-icon>
  </base-upload>
  <!-- <preview ref="previewRef" :files="model" /> -->
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue'
import { MessageBox, Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BaseUpload from './base-upload.vue';
import preview from './preview.vue';

import type { UploadProps, UploadUserFile, UploadFile, UploadFiles } from 'element-plus'
import Moment from 'moment';
import { deleteFile, generateFileUuid } from '@/api/file'
import { envConfig } from '@/config'

const model = defineModel()
export interface FileItem{
  fileUuid: string,
  fileName: string,
  fileType: string,
  fileAccessPath: string,
  fileStoragePath?: string,
  fileCreateTime?: string,
  fileSize?: number,
  uid?: number
}
export type FileResponse = ApiResponseData<FileItem[]> 
interface Props {
  // files?: FileItem[]; //初始文件列表渲染
  accept?: string; // 图片类型，多个用英文,分隔
  limit?: number; //最大允许上传文件数量
  size?: number; //单文件最大限制，单位MB
}

const props = withDefaults(defineProps<Props>(), {
  files: () => [],
  limit: 1,
  accept: '.jpg,.png,.jpeg,.gif,.bmp'
})
const previewRef = ref<PreviewInstance>()

onMounted(() => {
  initData()
})
const initData = () => {
  const data = model.value
  if (Array.isArray(data)) {
    fileList.value = data.map((item,index) => {
      const {fileName, fileAccessPath,uid} = item
      return {
        name: fileName,
        status: 'success',
        uid:  uid || Moment().valueOf(),
        url: envConfig.VITE_BASE_FILE_SATIC_PATH + fileAccessPath,
        response: {
          code: 0,
          data: [{ ...item }],
          message:''
        },
      }
    })
  } else {
    fileList.value = []
  } 
}
// 上传文件列表
const fileList = ref<UploadFile[]>([])

/**
 * 设置model数据
 */
const setModel = () => {
  model.value = fileList.value.map(item => {
    const response = item.response as FileResponse
    const result = response.data[0]
    return {
      ...result,
      uid: item.raw?.uid || Moment().valueOf(),
      fileType: item.raw?.type || `image/${result.fileType}`
    }
  })
  
}

/**
 * 上传成功更新model
 * @param response 
 * @param uploadFile 
 * @param uploadFiles 
 */
const handleSuccess: UploadProps['onSuccess'] = (response: FileResponse, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  if (response?.code === 0) {
  setModel()
  } else {
    ElMessage.error(response?.message || '上传失败！')
    fileList.value?.pop()
  }
}


/**
 * 上传前校验文件类型、大小
 * @param file 
 */
const handleBeforeUpload: UploadProps['beforeUpload'] = (file) => {
  const imgType = file.type.split('/')[1]
  if (!props.accept.toLocaleLowerCase().includes(imgType.toLocaleLowerCase())) {
    ElMessage.warning(`请上传后缀为${props.accept}类型图片！`)
    return false
  }
  if (props.size) {
    const limitSize = file.size / 1024 / 1024
    if (limitSize>props.size) {
      ElMessage.warning(`上传图片不能超过${props.size}MB!`)
      return false
    }
  }
  return true
}

/**
 * 删除文件
 * @param file 
 * @param uploadFiles 
 */
const handleRemove: UploadProps['onRemove'] = (file, uploadFiles) => {
  // fileList.value = fileList.value.filter(item=>item.uid !== file.uid)
  setModel()
}

/**
 * 删除文件前确认操作，确认后调用远程服务删除已上传文件
 * @param uploadFile 
 * @param uploadFiles 
 */
const  handleBeforeRemove: UploadProps['beforeRemove'] = (uploadFile: UploadFile, uploadFiles) => {
  if (uploadFile && uploadFile.status == 'success') {
    return ElMessageBox.confirm('确定要删除吗？', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
    })
      .then(async () => {
        const response = uploadFile.response as FileResponse
        if (response?.code === 0) {
          const result = await deleteFile(response?.data[0]?.fileUuid) as ApiResponseData<any>
          if (result?.code === 0) {
            ElMessage.success('删除成功！')
          }
          return result?.code === 0
        } else {
          return true
        }
      })
      .catch(() => false);
  } else {
    return false
  }
}

const handlePictureCardPreview: UploadProps['onPreview'] = (file) => {
  // dialogImageUrl.value = upload
  const index = model.value?.findIndex(item => item?.uid === file.uid)
  previewRef?.value?.show(index)
}
</script>
<style lang="scss" scoped>
  .image-upload-container{
    :deep(.el-upload-list__item-status-label),:deep(.el-icon--close-tip){
      display: none !important;
    }
    :deep(.el-upload-list__item),:deep(.el-upload--picture-card){
      width: 80px;
      height: 80px;
    }
    :deep(.el-upload-list__item-actions span+span){
      margin-left: 10px;
    }
  }
</style>

