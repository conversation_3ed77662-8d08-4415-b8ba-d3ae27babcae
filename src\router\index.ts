import { RouteLocationNormalized, RouteR<PERSON><PERSON>R<PERSON>, Router, createRouter, createWebHistory } from 'vue-router';
// import http from '@/utils/axios';

import { BASE_URL, isDev, setTitle } from '@/config';
import { commonRoutes, mainRouteList } from './routes';
import { cloneDeep, isArray } from 'lodash-es';
import { setStoreMenus, storeMenus } from '@/store';
import { getPageTitle } from '@/utils';
import { getMenus } from '@/api/user';

// 路由实例
export let routerInstance: Router;

export function initRouter(): Router {
	routerInstance = createRouter({
		history: createWebHistory(BASE_URL),
		routes: [],
	});
	createRouterGuards(routerInstance);
	return routerInstance;
}

// 重置所有路由的重定向，重定向到第一个子路由中，
function recursionTree(list: RouteRecordRaw[]) {
	list.forEach((item) => {
		const { redirect, children } = item;
		if (children?.length) {
			if (!redirect) {
				item.redirect = {
					name: children[0].name,
				};
			}
			recursionTree(children);
		}
	});
}

/**
 * 创建路由守卫
 * @param router
 */
function createRouterGuards(router: Router) {
	router.beforeEach(async (to, from, next) => {
		const { title } = to.meta || {};
		document.title = getPageTitle(title as string);
		setTitle(title);
		// http.cancelAllPendingByRoute();
		if (!storeMenus.value.length) {
			try {
				let serviceRouters = (await getMenus()) || [];
				console.log(serviceRouters, 'serviceRouters');
				setMainRoute(serviceRouters);
				if (hasRoute(to, router)) {
					next(to);
				} else {
					//有权限时访问不存在的路由
					if (serviceRouters.length > 0) {
						next({ name: '404' });
					} else {
						next({ name: '403' });
					}
				}
			} catch (error) {
				next({ name: '404' });
			}
		} else {
			next();
		}
	});
}

function hasRoute(to: RouteLocationNormalized, router: Router) {
	// 如果存在name 说明是页面内跳转过来的，不存在说明是刷新页面进来的
	if (to.name) {
		return router.hasRoute(to.name);
	} else {
		return !!router.getRoutes().find((item) => item.path === to.path);
	}
}

// 设置  MainRoute的 children
function setMainRoute(permission: ResponseMenusRouters[]) {
	function flattenTreeRecursive(tree) {
		let result:any[] = [];

		for (const node of tree) {
			result.push(node);
			if (node.children && node.children.length) {
				result = result.concat(flattenTreeRecursive(node.children));
			}
		}

		return result;
	}
	const newRoutes = permission.length > 0 ? getChildren(flattenTreeRecursive(permission)) : [];
	console.log(newRoutes,'newRoutes')
	const constantRoutes = cloneDeep(commonRoutes);
	constantRoutes.forEach((item) => {
		newRoutes.push(item);
	});
	console.log(newRoutes, 'newRoutes');
	recursionTree(newRoutes);
	setStoreMenus(newRoutes);
	const objRoot = constantRoutes.find((item) => item.name === 'root')!;
	if (permission.length > 0) {
		//有权限时默认进入第一个菜单
		objRoot.redirect = newRoutes[0].redirect || { name: '404' };
	} else {
		objRoot.redirect = { name: '403' };
	}
	routerInstance.addRoute(objRoot);
	newRoutes.forEach((item) => {
		routerInstance.addRoute(item);
	});
}
// 根据 后台返回的数据，获取main的子路由
function getChildren(routers: ResponseMenusRouters[]): RouteRecordRaw[] {
	const mainChildren: RouteRecordRaw[] = []; // main 的子路由
	function self(list: RouteRecordRaw[], newList: RouteRecordRaw[]) {
		list.forEach((item) => {
			const { children, meta = {}, ...other } = item;
			let title = ''; // 菜单的名字，如果存在 说明需要显示该路由
			if (meta.authCode === '' || (meta.isDev && isDev)) {
				// 如果 meta.authCode 存在且为 '' 说明 不需要权限就可以查看的菜单  或者正在开发下的路由在开发模式下也可以直接显示
				title = meta.title as string;
			} else {
				const obj = routers.find((item_) => {
					//  如果不存在 authCode 使用name  authCode 可能是数组
					const key_ = (meta.authCode as string[] | string) || (item.name as string);
					return item_.name === key_ || (isArray(key_) && key_.includes(item_.name));
				});
				if (obj) {
					title = meta.titleLock ? (meta.title as string) : obj.name;
				}
			}

			if (title) {
				const newChildren: RouteRecordRaw[] = [];
				const meta_ = Object.assign(meta, { title: title });
				const newObj: RouteRecordRaw = {
					...other,
					meta: meta_,
					children: newChildren,
				};
				if (children && children.length) {
					// 将当前路由重定向到第一个子路由中
					self(children, newChildren);
					if (newChildren.length)
						newObj.redirect = {
							name: newChildren[0].name,
						};
				}
				newList.push(newObj);
			}
		});
	}
	self(mainRouteList, mainChildren);
	return mainChildren;
}
