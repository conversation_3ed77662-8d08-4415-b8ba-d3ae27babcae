<template>
	<div class="list-navigation">
		<div class="header-view">
			<el-input
				v-model="searchText"
				placeholder="搜索"
				:prefix-icon="Search"
				clearable
			/>
		</div>
		<div class="main-view">
			<el-tree
				ref="treeRef"
				:data="listData"
				:props="treeProps"
				@node-click="handleNodeClick"
				:node-key="keyMap.nodeKey"
				:current-node-key="activedKey"
				:default-expand-all="false"
				:expand-on-click-node="false"
				:filter-node-method="nodeFilter"
				icon="ArrowRight"
			>
				<template #default="{ node, data }">
					<div class="custom-tree-node">
						<div class="left-content">
							<el-icon
								@click="toggleChild(node)"
								class="node-icon"
								v-if="data[keyMap.childrenKey] && data[keyMap.childrenKey].length !== 0"
							>
								<ArrowRight
									class="arrow-t"
									:class="{ 'actived-s': node.expanded }"
								/>
							</el-icon>

							<span class="node-label">{{ data[keyMap.labelKey] }}</span>
						</div>
						<slot
							name="tree-node-right"
							v-bind="data || {}"
						>
							<div
								v-if="showAllBadge || data[keyMap.badgeKey]"
								class="node-badge"
							>
								{{ data.num > 100 ? '99+' : data.num }}
							</div>
						</slot>
					</div>
				</template>
			</el-tree>
		</div>
	</div>
</template>
<script setup lang="tsx">
import { ref, reactive, onMounted, watchEffect, watch } from 'vue';
import { ArrowRight, Search, ArrowDown } from '@element-plus/icons-vue';

/** 定义 */
type StringFunc = (data, node) => string;
export interface KeyMap {
	labelKey?: StringFunc | string;
	nodeKey?: string | StringFunc;
	childrenKey?: string;
	disabledKey?: string | StringFunc;
	badgeKey?: string | StringFunc;
}

export interface Props {
	listData: object[];
	defaultActiveIndex?: number; // 默认活跃下标
	keyMap?: KeyMap;
	labelKey?: string; // label key值
}

const defaultKeyMap: KeyMap = {
	labelKey: 'label',
	nodeKey: 'value',
	childrenKey: 'children',
	disabledKey: 'disabled',
	badgeKey: 'num',
};

const props = withDefaults(defineProps<Props>(), {
	defaultActiveIndex: 0,
	keyMap: {},
});
const emit = defineEmits<{
	// (ev: 'update:value', value: string): void;
}>();
/** 初始化 */
const keyMap = Object.assign(defaultKeyMap, props.keyMap); // 初始化keyMap
const treeProps = {
	label: keyMap.labelKey,
	children: keyMap.childrenKey,
	disabled: keyMap.disabledKey,
};

const activedNode = defineModel(); // 初始化model
const activedKey = ref('');
// 点击节点事件
const handleNodeClick = (data: any, node: any) => {
	// console.log(data, node);
	activedNode.value = node;
	activedKey.value = data[keyMap.nodeKey];
};
// 树节点展开收起
const toggleChild = (node) => {
	console.log('child click');
	node.expanded = !node.expanded;
};

/** 树节点过滤 */
const searchText = ref('');
const treeRef = ref<InstanceType<typeof ElTree>>();

function nodeFilter(value: string, data: any) {
	if (!value) return true;
	return data[keyMap.labelKey].includes(value);
}

watch(searchText, (val) => {
	treeRef.value!.filter(val);
});
</script>
<style lang="scss" scoped>
:deep .el-tree {
	.el-tree-node__content {
		height: 40px;
		display: flex;
		align-items: center;
		// padding: 0 20px !important;
		.el-tree-node__expand-icon {
			display: none;
		}
	}

	.el-tree-node__children {
		// .el-tree-node__content {
		// 	padding-left: 36px !important;
		// }
	}

	.el-tree-node__content:hover {
		background: #f2f3f5 !important;
	}
	.node-icon:hover {
		color: #0075c2 !important;
	}

	.el-tree-node:focus > .el-tree-node__content {
		background-color: #f1f9ff !important;
		.node-label,
		.node-icon {
			color: #0075c2 !important;
		}
	}

	.el-tree-node.is-current > .el-tree-node__content {
		background-color: #f1f9ff !important;
		.node-label,
		.node-icon {
			color: #0075c2 !important;
		}
	}
}

.list-navigation {
	padding: 8px 0;
	.header-view {
		padding: 0 20px;
		margin-bottom: 8px;
	}

	.main-view {
		.custom-tree-node {
			height: 100%;
			width: 100%;
			padding: 0 20px;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.left-content {
				display: flex;
				align-items: center;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				flex: 1;
				min-width: 0;
				margin-right: 8px;
			}

			.node-icon {
				font-size: 16px;
			}

			.node-label {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #1d2129;
				margin-left: 8px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.node-badge {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 38px;
				height: 22px;
				background: #f2f3f5;
				border-radius: 4px;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #86909c;
				line-height: 20px;
			}
		}
	}
}

.arrow-t {
	transition: transform 0.3s ease-in-out;
	&.actived-s {
		transform: rotate(90deg);
	}
}
</style>
