import { envConfig, isDev, rootO<PERSON>in, rootUri } from '@/config';
import { cloneDeep } from 'lodash-es';
import { isArray, isObject } from './is';
import { ElMessage, ElMessageBox } from 'element-plus';
import { storeAuthorities } from '@/store';
/**
 * 获取标题
 */
export function getPageTitle(pageTitle?: string) {
	return pageTitle ? `${envConfig.documentTitle} - ${pageTitle}` : envConfig.documentTitle;
}
/**
 * 设置url地址中的参数
 */
export function setUrlParam(data: any, encode: boolean = true): string {
	data = data || {};
	const param: string[] = [];
	for (let key in data) {
		if (data[key] !== undefined && data[key] !== null && data[key] !== '') {
			if (encode) {
				param.push(key + '=' + encodeURIComponent(data[key]));
			} else {
				param.push(key + '=' + data[key]);
			}
		}
	}
	if (param.length) {
		return '?' + param.join('&');
	} else {
		return '';
	}
}

/**
 * 比较两个变量是否相等 当相等的时候 返回 true
 * @param oldVal 旧值
 * @param newVal 新值
 */
export const diff = (oldVal: any, newVal: any): boolean => {
	if (isObject(oldVal)) {
		if (!isObject(newVal)) return false;
		if (Object.keys(oldVal).length !== Object.keys(newVal).length) return false;
		return Object.keys(oldVal).every((key) => {
			return diff(oldVal[key], newVal[key]);
		});
	} else if (isArray(oldVal)) {
		if (!isArray(newVal)) return false;
		if (oldVal.length !== newVal.length) return false;
		return oldVal.every((item, index) => {
			return diff(item, newVal[index]);
		});
	} else {
		return oldVal === newVal;
	}
};
/**
 * 给对象赋值   只接受第一个值的参数 只能赋值第一层，不支持深度赋值
 * 相比 Object.assign 不会增加额外的属性
 * @param obj
 * @param newObj
 */
export function setObjValue<T = Record<string, any>>(obj: T, newObj: Partial<T>, defaultObj?: T) {
	newObj = newObj ?? ({} as T);
	if (defaultObj) {
		Object.keys(obj as object).forEach((key) => {
			if (key == 'assessmentReportFile') {
				obj['assessmentReportFiles'] = [newObj[key]];
			}
			obj[key] = newObj[key] ?? cloneDeep(defaultObj[key]);
		});
	} else {
		Object.keys(obj as object).forEach((key) => {
			obj[key] = newObj[key];
		});
	}
}
// 如果新值没有的话，就继续使用旧值
export function setObjValue2<T = Record<string, any>>(obj: T, newObj: Partial<T>) {
	newObj = newObj ?? ({} as T);
	Object.keys(obj as object).forEach((key) => {
		obj[key] = newObj[key] ?? obj[key];
	});
}

//新打开一个路由页面
export function openNewWindow(url: string, param?: Record<string, any>) {
	window.open(`${rootOrigin}${url}${setUrlParam(param)}`);
}
//跳转构力三方
export function openNewWindowUri(url: string, param: Record<string, any>) {
	window.open(`${rootUri}${url}${setUrlParam(param)}`);
}

export function getTreeDataByKey(tree, key) {
	let res: any = [];
	const recuFun = (arr) => {
		if (!Array.isArray(arr)) return;
		if (!arr.length) return;
		let checked = arr.filter((c) => c[key]);
		res = [...res, ...checked];
		arr.forEach((ch) => recuFun(ch.childList));
	};
	recuFun(tree);
	return res;
}

/**
 * 前台本地预览用户上传的文件
 * @param file  用户上传的文件对象
 */
export function getFileURLByFile(file: File): string {
	try {
		let url = '';
		if (window.webkitURL) {
			url = window.webkitURL.createObjectURL(file);
		} else {
			url = window.URL.createObjectURL(file);
		}

		return url;
	} catch (error) {
		ElMessage.error('文件对象不存在，请刷新重试！');
		return '';
	}
}
//根据文件大小选择合适的单位
export function getFileSize(value: number): string {
	if (!value) return '';
	const units = ['B', 'KB', 'MB', 'GB', 'TB'];
	for (let i = 0; i < units.length; i++) {
		let rel = value / Math.pow(1024, i);
		if (rel < 1024) {
			return Math.round(rel * 100) / 100 + ' ' + units[i]; // 如果是MB以上的单位，则取小数点后两位
		}
	}
	return value + ' ' + units[0];
}
export function commonConfirm(message: string) {
	return ElMessageBox({
		type: 'warning',
		title: '提示',
		message: message,
		showCancelButton: true,
		showClose: false,
	});
}

// 保存静态资源到本地
export function downloadPublicFile(fileName: string) {
	let url = location.pathname;
	const BASE_URL = rootOrigin;
	if (url.includes('/index.html')) {
		// 说明是 Hash 路由
		url = url.replace('index.html', fileName);
	} else {
		if (BASE_URL === '/') {
			url = '/' + fileName;
		} else {
			url = BASE_URL + '/' + fileName;
		}
	}
	const a = document.createElement('a');
	a.download = fileName;
	a.href = url;
	a.click();
}
/**
 * 获取权限
 */
export function getAuth(auth?: string | string[]): boolean {
	if (!auth) return true; // 没有时，默认true
	if (typeof auth === 'string') return storeAuthorities.value.includes(auth);
	return auth?.some((key) => storeAuthorities.value.includes(key));
}

interface LineListItem {
	parentId: string;
	uuid: string;
	[key: string]: any;
}
interface TreeListItem {
	children: TreeListItem[];
	parentId: string;
	uuid: string;
	[key: string]: any;
}
export function lineListToTree(list: LineListItem[] = []): TreeListItem[] {
	const stairIds: string[] = []; // 顶级的id，默认展开用
	// 先将所有id取出来，用来判断是否有 父级id
	const uuids = list.map((item) => item.uuid);
	const group: Record<string, TreeListItem[]> = {};
	const rootTree: TreeListItem[] = [];
	list.forEach((item) => {
		const { parentId, uuid } = item;
		const newItem: TreeListItem = {
			...item,
			children: [],
		};
		// 所有的item都有 parentId 通过 parentId 去 uuids里找，如果没有找到，说明自己没有父亲，是顶级
		if (parentId && !uuids.includes(parentId)) {
			stairIds.push(uuid);
			rootTree.push(newItem);
		} else {
			group[parentId] = group[parentId] || [];
			group[parentId].push(newItem);
		}
	});
	const queue: TreeListItem[] = [...rootTree];
	while (queue.length) {
		const node: TreeListItem = queue.shift()!;
		const uuid = node.uuid;
		const children = group[uuid] && group[uuid].length ? group[uuid] : null;
		if (children) {
			node.children = children;
			queue.push(...children);
		}
	}
	return rootTree;
}
export function getFileUrl(file?: ResponseFileExtend | null): string {
	if (!file) return '';
	return envConfig.VITE_BASE_FILE_SATIC_PATH + file.fileAccessPath;
}
interface RegionObj {
	regionNameProvince: string;
	regionNameCity: string;
	regionName: string;
	// [key: string]: any;
}
export function getRegionFullName(obj?: RegionObj | null): string {
	if (!obj) return '';
	let fullName: string[] = [];
	obj.regionNameProvince && fullName.push(obj.regionNameProvince);
	obj.regionNameCity && fullName.push(obj.regionNameCity);
	obj.regionName && fullName.push(obj.regionName);
	return fullName.join('/');
}
