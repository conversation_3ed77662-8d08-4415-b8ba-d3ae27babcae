# 推荐 vs code 插件

Prettier - Code formatter
Vue-Official

## 镜像管理

### 方式一

-  安装 node：http://nodejs.cn/download/
-  切换镜像（以淘宝镜像举例）： npm config set registry http://registry.npm.taobao.org
-  默认镜像：npm config set registry https://registry.npmjs.org/

### 方式二 引入 nrm 管理工具

-  全局安装 npm install -g nrm
-  查看安装版本 nrm -V
-  查看所有配置源 nrm ls
-  新增配置 nrm add [源名称] [源地址]，例如 nrm add verdaccio http://172.16.30.20:4873/
-  切换源 nrm use [源名称]，例如 nrm use verdaccio

## icon font

https://www.iconfont.cn/invite?type=project&token=nN10p26bMI5cyjf6#邀请你加入「海南智慧农机」
https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4549513

## 发布地址

### 测试环境

#### 发布地址

172.16.30.81 root/root
/home/<USER>/data/front/static/zhnj-gov
/home/<USER>/data/front/static/zhnj-user

#### 访问地址

http://172.16.30.81/zhnj-gov
http://172.16.30.81/zhnj-user
