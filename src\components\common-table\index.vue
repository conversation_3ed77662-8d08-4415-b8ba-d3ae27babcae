<template>
	<div
		class="i-common-table-box"
		:class="{ 'full-table': fillScreen }"
		:key="soleKey"
		ref="refDomBox"
	>
		<div
			class="t-header"
			:class="{ 't-header-min-height': !headerEleIsEmpty }"
			ref="refHeader"
			v-if="hideHeader === false || $slots.header"
		>
			<div
				class="t-header-left"
				ref="refHeaderLeft"
			>
				<el-dropdown
					class="add-m-r-16"
					split-button
					type="primary"
					v-if="createButtonText && createButtonOptions && createButtonOptions.length > 0"
					@click="handleClickCreateBtn('create')"
					@command="handleClickCreateBtn"
				>
					<IconFont
						class="plus-icon"
						type="icon-add-line"
					/>
					{{ createButtonText }}
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item
								v-for="(item, index) in createButtonOptions"
								:key="index"
								:command="item.value"
								>{{ item.label }}</el-dropdown-item
							>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
				<el-button
					v-if="createButtonText && !createButtonOptions"
					@click="handleClickCreateBtn('create')"
					type="primary"
					class="add-m-r-16"
				>
					<IconFont
						class="plus-icon"
						type="icon-add-line"
					/>
					{{ createButtonText }}
				</el-button>
				<span
					v-if="total && total > 0 && showTotal"
					class="table-total-num"
					>共 {{ total }} 项</span
				>
				<slot name="header-left"></slot>
			</div>
			<div
				class="t-header-right"
				ref="refHeaderRight"
			>
				<collapse-subitem v-if="collapseSubitemSwitch" />
				<high-sort
					:columns="columns"
					v-model:sortGroupList="sortGroupList"
					v-if="highSortSwitch"
				/>
				<group-by-filter v-if="groupByFilterSwitch" />
				<common-input-query
					v-if="inputSearchSwitch"
					:inputSearchPlaceholder="inputSearchPlaceholder"
					@onSearch="onCommonInputSearch"
				/>
				<multiple-select
					v-if="batchOperationSwitch"
					:columnOptions="batchOperationOptions"
					@multipleSelect="onMultipleSelect"
				/>
				<slot name="header-right"></slot>
			</div>
		</div>
		<div
			class="search-list-box"
			ref="refSearchList"
			v-if="queryList && queryList.length > 0"
		>
			<div class="query-result">共 {{ total }} 条符合条件的结果：</div>
			<div
				v-for="(item, index) in queryList"
				:key="index"
				class="query-cell"
			>
				<span class="cell-text">{{ item.label }}</span>
				<IconFont
					@click="clearQueryItem(item.value)"
					class="cell-icon"
					type="icon-close-line"
				></IconFont>
			</div>
			<div
				@click="clearQueryList"
				class="query-clear-all"
			>
				清空条件
			</div>
		</div>
		<el-table
			v-loading="loading_"
			v-bind="$attrs"
			:height="height"
			@selection-change="handleSelectionChange"
			@cell-click="handleCellClick"
			ref="refTable"
			:showOverflowTooltip="showOverflowTooltip"
			:size="tableSize"
			:row-key="rowKey"
			empty-text=" "
			class="custom-common-table"
			header-cell-class-name="common-table-header-cell"
			row-class-name="common-table-row"
			:span-method="spanMethod"
		>
			<el-table-column
				type="expand"
				fixed="left"
				v-if="showExpandCell"
			>
				<template #default="props">
					<slot
						name="expand-cell"
						v-bind="props || {}"
					>
					</slot>
				</template>
			</el-table-column>
			<el-table-column
				v-if="(showCheckBox || selectable) && !hideColumnKeys.includes('_select_')"
				:selectable="selectable"
				:reserve-selection="true"
				type="selection"
				fixed="left"
				align="center"
				width="50"
			/>
			<el-table-column
				v-if="showIndex && !hideColumnKeys.includes('_index_')"
				type="index"
				:index="renderIndex"
				fixed="left"
				align="center"
				class-name="t-cell-index"
				:width="70"
				label="序号"
			/>
			<template v-if="defaultSlotIndex && $slots.default">
				<template v-for="item in $slots.default()">
					<component
						v-if="!item.props?.prop || !hideColumnKeys.includes(item.props.prop)"
						:is="item"
					></component>
				</template>
			</template>
			<template v-if="columns && columns.length">
				<template
					v-for="{
						slotName,
						searchType,
						searchRule,
						selectType,
						openSort,
						prop,
						key_,
						slotHeaderName,
						hide,
						formatter,
						filters,
						hoverHighLight,
						...column_
					} in columns"
				>
					<el-table-column
						:key="key_"
						:class-name="formatCellClassName(hoverHighLight)"
						v-if="!checkHide(hide) && (!prop || !hideColumnKeys.includes((prop as string)))"
						:prop="(prop as string)"
						:formatter="(formatter as any)"
						v-bind="column_"
					>
						<!-- 输入框模式搜索 -->
						<template
							v-if="searchType == 'input' && prop"
							#header
						>
							<div class="t-search-header">
								<el-popover
									:ref="setItemRef(prop)"
									placement="bottom-start"
									:width="218"
									trigger="click"
									popper-class="custom-search-popover"
								>
									<search-sort
										v-if="openSort"
										:titleText="column_.label"
										v-model:value="sortGroupList[prop as string].value"
										@search="onSearchSort({ prop })"
									/>
									<search-input
										:titleText="column_.label"
										:searchRule="searchRule"
										v-model:value="filterGroupList[prop as string].value"
										v-model:rule="filterGroupList[prop as string].rule"
										@search="onSearchInput({ prop, searchType, label: column_.label })"
									/>
									<template #reference>
										<div class="w-full">
											{{ column_.label }}
											<el-icon
												class="t-search-icon"
												:class="{ 't-search': judgeHighlightSearchIcon(prop) }"
											>
												<CaretBottom />
											</el-icon>
										</div>
									</template>
								</el-popover>
							</div>
						</template>
						<!-- 下拉框模式搜索 -->
						<template
							v-if="searchType == 'select' && prop"
							#header
						>
							<div class="t-search-header">
								<el-popover
									:ref="setItemRef(prop)"
									placement="bottom-start"
									:width="218"
									trigger="click"
									popper-class="custom-search-popover"
								>
									<search-sort
										v-if="openSort"
										:titleText="column_.label"
										v-model:value="sortGroupList[prop as string].value"
										@search="onSearchSort({ prop })"
									/>
									<search-select
										:searchRule="searchRule"
										:titleText="column_.label"
										:filters="filters"
										:selectType="selectType"
										v-model:value="filterGroupList[prop as string].value"
										v-model:rule="filterGroupList[prop as string].rule"
										@search="onSearchSelect($event, { prop, searchType, label: column_.label })"
									/>
									<template #reference>
										<div class="w-full">
											{{ column_.label }}
											<el-icon
												class="t-search-icon"
												:class="{ 't-search': judgeHighlightSearchIcon(prop) }"
											>
												<CaretBottom />
											</el-icon>
										</div>
									</template>
								</el-popover>
							</div>
						</template>
						<!-- 日期框模式搜索 -->
						<template
							v-if="searchType == 'date' && prop"
							#header
						>
							<div class="t-search-header">
								<el-popover
									:ref="setItemRef(prop)"
									placement="bottom-start"
									:width="230"
									trigger="click"
									popper-class="custom-search-popover"
								>
									<search-sort
										v-if="openSort"
										:titleText="column_.label"
										v-model:value="sortGroupList[prop as string].value"
										@search="onSearchSort({ prop })"
									/>
									<search-date
										:searchRule="searchRule"
										:titleText="column_.label"
										v-model:value="filterGroupList[prop as string].value"
										v-model:rule="filterGroupList[prop as string].rule"
										@search="onSearchDate({ prop, searchType, label: column_.label })"
									/>
									<template #reference>
										<div class="w-full">
											{{ column_.label }}
											<el-icon
												class="t-search-icon"
												:class="{ 't-search': judgeHighlightSearchIcon(prop) }"
											>
												<CaretBottom />
											</el-icon>
										</div>
									</template>
								</el-popover>
							</div>
						</template>
						<!-- 排序模式搜索 -->
						<template
							v-if="openSort && !searchType && prop"
							#header
						>
							<div class="t-search-header">
								<el-popover
									:ref="setItemRef(prop)"
									placement="bottom-start"
									:width="218"
									trigger="click"
									popper-class="custom-search-popover"
								>
									<search-sort
										:titleText="column_.label"
										v-model:value="sortGroupList[prop as string].value"
										customClass="add-p-b-10"
										@search="onSearchSort({ prop })"
									/>
									<template #reference>
										<div class="w-full">
											{{ column_.label }}
											<el-icon
												class="t-search-icon"
												:class="{'t-search':!!sortGroupList[prop as string].value}"
											>
												<CaretBottom />
											</el-icon>
										</div>
									</template>
								</el-popover>
							</div>
						</template>
						<template
							v-if="slotName"
							v-slot="slotProps"
						>
							<slot
								:name="slotName"
								v-bind="slotProps || {}"
							>
							</slot>
						</template>
						<template
							v-if="slotHeaderName"
							v-slot:header="slotProps"
						>
							<slot
								:name="slotHeaderName"
								v-bind="slotProps || {}"
							>
							</slot>
						</template>
					</el-table-column>
				</template>
			</template>
			<template v-if="!defaultSlotIndex && $slots.default">
				<template v-for="item in $slots.default()">
					<component
						v-if="!item.props?.prop || !hideColumnKeys.includes(item.props.prop)"
						:is="item"
					></component>
					<!-- :show-overflow-tooltip="
							item.props?.showOverflowTooltip ?? item.props?.['show-overflow-tooltip'] ?? showOverflowTooltip
						" -->
				</template>
			</template>
			<el-table-column
				v-if="getBtns"
				:width="operateWidth"
				:label="operateOther?.label || '操作'"
				:prop="operateOther?.prop || 'operates'"
				:fixed="operateOther?.fixed || 'right'"
				v-bind="operateOther || {}"
			>
				<template #default="{ row, column, $index }">
					<i-btns
						:key="row"
						:btns="getBtns_(row, column)"
						:moreNumber="btnsMoreNumber"
						:data="row"
					></i-btns>
				</template>
			</el-table-column>

			<template v-slot:empty="slotProps">
				<div
					class="empty-box"
					v-if="!loading_"
				>
					<img
						class="empty-img"
						src="@/assets/images/common/no-record.png"
						alt=""
					/>
					<slot
						name="empty"
						v-bind="slotProps || {}"
					>
						<div class="empty-text">暂无数据</div>
					</slot>
				</div>
			</template>
			<template v-slot:append="slotProps">
				<slot
					name="append"
					v-bind="slotProps || {}"
				></slot>
			</template>
		</el-table>
		<el-pagination
			v-if="pagination_"
			ref="refPagination"
			class="t-pagination"
			v-bind="pagination_"
			@change="onPageChange"
			@current-change="onPageCurrentChange"
			@size-change="onPageSizeChange"
		/>
		<div
			class="batch-operation-view"
			ref="batchOperationEle"
			v-if="showBatchOperation"
		>
			<div>
				<p style="font-size: 14px">
					已选择 <span class="checked-tip">{{ multipleSelection.length }}</span> 项
				</p>
			</div>
			<div>
				<el-button @click="onCancelBatchOperation">取消</el-button>
				<el-button
					:disabled="multipleSelection.length == 0"
					@click="onSubmitBatchOperation"
					type="primary"
					>{{ batchOperationBtnText }}</el-button
				>
			</div>
		</div>
	</div>
</template>

<script lang="tsx" setup>
import { innerH } from '@/store';
import { TableInstance, ElTooltipProps } from 'element-plus';
import { onMounted, onUnmounted, ref, useSlots, watch, computed } from 'vue';
import { RefreshRight, Search, CaretBottom, Plus } from '@element-plus/icons-vue';
import ISetting from './setting.vue';
import type { StatesItem, TypeStatesColor } from './type';
import Moment from 'moment';
import CommonInputQuery from './components/CommonInputQuery.vue';
import { defaultFormatAll, defaultFormatDate, defaultFormatMonth, defaultFormatTime, defaultText } from '@/config';
defineOptions({ inheritAttrs: false });
interface Props extends /* @vue-ignore */ ElTableProps {
	// interface Props {
	rowKey?: string; // 行数据的 Key
	hideHeader?: boolean;
	columns?: CommonTableColumn<any>[];
	// border?: boolean;
	pagination?: ElPaginationProps | boolean; // 是否开启分页
	total?: number; //	总条目数
	pageSize?: number; //每页显示条目个数
	pageNumber?: number; // 当前页数  key可以根据后台定义的字段改
	showTotal?: boolean; //是否展示总数
	loading?: undefined | boolean;
	showIndex?: boolean;
	showCheckBox?: boolean;
	selectable?: any;
	height?: string | number; // Table 的高度， 默认为自动高度。 如果 height 为 number 类型，单位 px；如果 height 为 string 类型，则这个高度会设置为 Table 的 style.height 的值，Table 的高度会受控于外部样式。
	fillScreen?: boolean; // 是否充满整个屏幕
	otherHeight?: number; // 首屏中除了表格以外其它内容的高度 首屏撑满的表格可以用这个 表格高度为：首屏高度 - otherHeight
	getBtns?: OptionBtn[] | ((row: any, column: CommonTableColumn<any>) => OptionBtn[]); // 操作按钮 列中的按钮
	operateWidth?: string | number | undefined; // 操作按钮列的宽度
	operateOther?: ElTableColumnProps; // 操作按钮列的其它参数
	getList?: (val: CommonTableParams) => Promise<any>;
	defaultSlotIndex?: boolean; // 默认插入的插槽的位置 true 在 columns 之前 false  在columns 之后
	soleKey?: string; // 唯一的key  ISetting 组件中使用
	btnsMoreNumber?: number; // i-btns 组件中的参数 按钮超过多少个后，多出的按钮放到 ... 中
	showOverflowTooltip?: boolean | Partial<ElTooltipProps>; // 全局默认
	showExpandCell?: boolean; // 是否展示展开行

	// ====== 新增按钮相关逻辑 start ======
	createButtonText?: string; // 创建按钮文本，控制创建按钮展示
	createButtonOptions?: CommonTableOptions[]; // 创建按钮选项列表。适用于导入等创建操作
	// ====== 新增按钮相关逻辑 end ======

	// ====== 折叠子项相关逻辑 start ======
	collapseSubitemSwitch?: boolean; // 折叠子项开关
	// ====== 折叠子项相关逻辑 end ======

	// ====== 高级排序相关逻辑 start ======
	highSortSwitch?: boolean; // 高级排序开关
	// ====== 高级排序相关逻辑 end ======

	// ====== 分组筛选相关逻辑 start ======
	groupByFilterSwitch?: boolean; // 分组筛选开关
	groupByFilterOptions?: CommonTableOptions[]; // 分组筛选下拉列表
	// ====== 分组筛选相关逻辑 end ======

	// ====== 分组筛选相关逻辑 start ======
	batchOperationSwitch?: boolean; // 批量操作开关
	batchOperationOptions?: CommonTableOptions[]; // 批量操作菜单列表
	// ====== 分组筛选相关逻辑 end ======

	// ====== 输入框搜索相关逻辑 start ======
	inputSearchSwitch?: boolean;
	inputSearchPlaceholder?: string;
	// ====== 输入框搜索相关逻辑 end ======

	// 合并行或列
	spanMethod?: (data: { row: any; rowIndex: number; column: any; columnIndex: number }) => {
		rowspan: number;
		colspan: number;
	};
}
const props = withDefaults(defineProps<Props>(), {
	rowKey: 'uuid',
	hideHeader: false,
	showIndex: true,
	size: 'small',
	height: '100%',
	// border: false,
	// pageNumber: 1,
	// pageSize: 20,
	operateWidth: 150,
	fillScreen: true,
	loading: undefined,
	pagination: undefined,
	defaultSlotIndex: false, // 默认在 columns 之后插入
	btnsMoreNumber: 2,
	showExpandCell: false,
	collapseSubitemSwitch: false,
	highSortSwitch: false,
	groupByFilterSwitch: false,
	inputSearchSwitch: false,
	inputSearchPlaceholder: '搜索',
	showTotal:true,
	showOverflowTooltip: () => ({
		showAfter: 1000,
	}), // 全局默认的
	spanMethod: () => ({
		rowspan: 1,
		colspan: 1,
	}),
});
const emit = defineEmits<{
	(ev: 'update:value', value: string): void;
	(ev: 'update:pageSize', value: number): void;
	(ev: 'update:pageNumber', value: number): void;
	(ev: 'update:showCheckBox', value: boolean): void;
	(ev: 'pageCurrentChange', val: number): void; // current-page 改变时触发
	(ev: 'pageSizeChange', val: number): void; //  page-size 改变时触发
	(ev: 'pageChange', pageNumber: number, pageSize: number): void; // 对应 element-plus Pagination 组件中的 change 事件 	current-page 或 page-size 更改时触发

	(ev: 'onSearch', val: any): void; // 表头搜索-回调事件
	(ev: 'onSort', val: any): void; // 表头排序-回调事件
	(ev: 'onClearAllSearch'): void; // 清空筛选条件

	(ev: 'cellClick', row: any): void; // 单元格点击

	// ====== 新增按钮相关逻辑 start ======
	(ev: 'onClickCreateBtn', type: string): void; // 点击新增按钮
	// ====== 新增按钮相关逻辑 end ======

	// #region 批量操作回调
	(ev: 'selectionChange', list: any): void; // 当选择项发生变化时会触发该事件
	(ev: 'onSubmitBatchSelectionList', type: string, list: any): void; // 提交批量选中数据
	// #endregion

	// #region 通用输入框搜索
	(ev: 'onCommonInputSearch', type: string): void;
	// #endregion
}>();
const slots = useSlots();
const refTable = ref<TableInstance>();
const tableSize = ref(props.size); //  'large' | 'default' | 'small'
const hideColumnKeys = ref<string[]>([]); // 隐藏的column的 keys 之所以用隐藏的key 是因为判断起来更稳妥 新增的列也不用重新
const columnOptions = ref<OptionItem[]>([]);
const mapColumnSearch = ref<Record<string, any>>({}); // 储存 搜索框 的输入值

const filterGroupList = ref<Record<string, any>>({}); // 条件过滤，数据集合
const sortGroupList = ref<Record<string, any>>({}); // 数据排序，数据集合

const refDomBox = ref<HTMLDivElement>();
const refHeader = ref<HTMLDivElement>();
const refSearchList = ref<HTMLDivElement>();
const refPagination = ref();
const loading_ = ref(props.loading || false);

// 查询筛选相关参数
const queryList = ref<CommonTableOptions[]>([]); // 查询条件，仅供展示

const ruleTextObj = {
	// 规则文本集合
	contain: '包含',
	noContain: '不包含',
	equal: '等于',
	noEqual: '不等于',
	greaterOrEqual: '大于等于',
	lessOrEqual: '小于等于',
	between: '介于',
	asc: '正序',
	desc: '倒序',
};

const pageSize_ = 20; // 默认的
// 默认的分页器参数
const defaultPagination: ElPaginationProps = {
	small: true,
	layout: 'sizes, prev, pager, next',
	background: true,
	pageSizes: [20, 50, 100],
};

// 分页器的一些参数
const pagination_ = ref<ElPaginationProps | null>(null);

watch(
	() => props.loading,
	(val) => {
		loading_.value = val || false;
	},
);

watch(
	() => props.columns,
	(list) => {
		const map = {};
		const filterGroupQuery = {};
		const sortGroupQuery = {};
		list?.forEach((item) => {
			(item as any).filterPlacement = item.filterPlacement || item['filter-placement'] || 'bottom-end';
			(item as any).columnKey = item.columnKey || item.prop;
			item.key_ = item.key || item.columnKey || item.label || '';
			if (item.prop) {
				// 没有prop的，不放到选项中
				const obj = columnOptions.value.find((item_) => item_.value === item.prop);
				if (!obj) {
					columnOptions.value.push({
						value: item.prop as string,
						label: item.label || '',
					});
				}
				if (item.searchType) {
					map[item.prop] = map[item.prop] || '';
				}

				// DOING 将搜索内容抽离为一个集合
				if (item.searchType) {
					filterGroupQuery[item.prop] = {
						label: item.prop,
						type: item.searchType,
						value: '',
						rule: item.searchRule,
					};
				}
				if (item.openSort) {
					sortGroupQuery[item.prop] = {
						label: item.prop,
						value: item.defaultSortChecked ? (item.defaultSortRule ? item.defaultSortRule : 'desc') : '',
						time: '',
					};
					if (sortGroupQuery[item.prop].value) {
						sortGroupQuery[item.prop].time = Moment().format('YYYY-MM-DD HH:mm:ss');
					}
				}
			}
		});
		mapColumnSearch.value = map;
		filterGroupList.value = filterGroupQuery;
		sortGroupList.value = sortGroupQuery;
	},
	{ deep: true, immediate: true },
);
watch(
	() => props.showIndex,
	(val) => {
		if (val) {
			const obj = columnOptions.value.find((item) => item.value === '_index_');
			if (obj) return true; // 已经存在了，就不添加进去了
			columnOptions.value.unshift({
				value: '_index_',
				label: '序号',
			});
		}
	},
	{ immediate: true },
);

// #region 表格 Header区域逻辑
const refHeaderLeft = ref<HTMLDivElement>(); // 表格头部-左侧区域
const refHeaderRight = ref<HTMLDivElement>(); // 表格头部-右侧区域

// 表头header区域，是否为空
const headerEleIsEmpty = computed<boolean>(() => {
	// 获取元素高度
	let leftHeight = refHeaderLeft.value?.offsetHeight as number;
	let rightHeight = refHeaderRight.value?.offsetHeight as number;
	/**
	 * 为适配前端展示，如何表格头部操作区域存在元素，就添加最低高度
	 * 判断元素高度是否大于0，有高度代表有内容
	 * 判断总数字段是否大于0（总数为接口传递，异步获取）
	 */
	return leftHeight + rightHeight > 0 || (props.total ?? 0) > 0 ? false : true;
});
// #endregion

onMounted(() => {
	if (slots.default) {
		const list = slots.default() || [];
		list.forEach((item) => {
			if (item.props) {
				const props_ = item.props;
				// 存在prop属性 label为表头名称
				if (props_.prop) {
					const obj = columnOptions.value.find((item_) => item_.value === props_.prop);
					if (!obj) {
						columnOptions.value.push({
							value: props_.prop,
							label: props_.label,
						});
					}
				}
			}
		});
	}
});

function getBtns_(row: any, column: any) {
	if (typeof props.getBtns === 'function') {
		return props.getBtns(row, column);
	}
	return props.getBtns || [];
}

function renderIndex(index: number) {
	if (!pagination_.value) {
		return index + 1;
	}
	return (pagination_.value.currentPage! - 1) * pagination_.value.pageSize! + index + 1;
}
function onPageCurrentChange(val: number) {
	if (pagination_.value) {
		pagination_.value.currentPage = val;
	}
	emit('update:pageNumber', val);
	emit('pageCurrentChange', val);
}
function onPageSizeChange(val: number) {
	if (pagination_.value) {
		pagination_.value.pageSize = val;
	}
	emit('update:pageSize', val);
	emit('pageSizeChange', val);
}
function onPageChange(pageNumber: number, pageSize: number) {
	if (pagination_.value) {
		pagination_.value.currentPage = pageNumber;
		pagination_.value.pageSize = pageSize;
	}
	emit('update:pageNumber', pageNumber);
	emit('update:pageSize', pageSize);
	emit('pageChange', pageNumber, pageSize);
}

watch(
	() => props.pagination,
	(val) => {
		// 为false时，说明用户明确指出，不使用分页
		if (props.pagination === false) {
			pagination_.value = null;
		} else if (typeof props.pagination === 'boolean') {
			pagination_.value = { ...defaultPagination };
		} else if (typeof props.pagination === 'object') {
			pagination_.value = {
				...defaultPagination,
				...props.pagination,
			};
		}
	},
	{ immediate: true },
);
watch(
	[() => props.pageNumber, () => props.total, () => props.pageSize],
	() => {
		// 为false时，说明用户明确指出，不使用分页
		if (props.pagination === false) return;
		if (props.pageNumber !== undefined || props.total !== undefined || props.pageSize !== undefined) {
			pagination_.value = pagination_.value || { ...defaultPagination };
			pagination_.value.total = props.total ?? pagination_.value.total;
			pagination_.value.pageSize = props.pageSize ?? (pagination_.value.pageSize || pageSize_);
			pagination_.value.currentPage = props.pageNumber ?? (pagination_.value.currentPage || 1);
		}
	},
	{ immediate: true },
);

function checkHide(hide: CommonTableColumn['hide']) {
	if (typeof hide === 'boolean') return hide;
	if (typeof hide === 'function') return hide();
	return false;
}

function getRef() {
	return refTable.value!;
}

// 通过 handle 方法 调用 el-table上的方法，
function handle() {
	if (refTable.value) {
		const [type, ...other] = arguments;
		if (typeof refTable.value[type] === 'function') {
			return refTable.value[type](...other);
		} else {
			throw new TypeError(`el-table不存在 ${type} 方法`);
		}
	} else {
		throw new TypeError(`el-table不存在`);
	}
}

// ====== 新增按钮相关逻辑 start ======
function handleClickCreateBtn(type) {
	emit('onClickCreateBtn', type);
}
// ====== 新增按钮相关逻辑 end ======

// #region 通用输入框搜索逻辑
const onCommonInputSearch = (keyword: string) => {
	emit('onCommonInputSearch', keyword);
};
// #endregion

// 单元格的 className 的回调方法
function formatCellClassName(val) {
	// 为列添加移入高亮类名
	if (val === true) {
		return 'hover-high-light-cell';
	} else {
		return '';
	}
}

// 单元格点击事件
function handleCellClick(data, config) {
	// 判断当前列是否配置“移入高亮”字段，不配置此字段不向外发散事件
	let column = props.columns?.filter((item) => {
		return item.prop == config.columnKey;
	});
	if (column && column.length > 0 && column[0].hoverHighLight) {
		emit('cellClick', data);
	}
}

// #region 表头搜索逻辑

// 表头-输入框搜索
function onSearchInput(column: any) {
	let text =
		column.label +
		' ' +
		ruleTextObj[filterGroupList.value[column.prop].rule] +
		' “' +
		filterGroupList.value[column.prop].value +
		'”';
	handleFormatQueryList(column, text);
}

// 表头-下拉框搜索
function onSearchSelect(event: any, column: any) {
	let labelArr = event.map((item) => item.label);
	let text = column.label + ' ' + ruleTextObj[filterGroupList.value[column.prop].rule] + ' “' + labelArr.join() + '”';

	handleFormatQueryList(column, text);
}

// 表头-日期框搜索
function onSearchDate(column: any) {
	let dateArr = filterGroupList.value[column.prop].value.split(',');
	let dateText = '';
	if (dateArr.length > 1) {
		dateText = dateArr[0] + ' 至 ' + dateArr[1];
	} else {
		dateText = dateArr[0];
	}

	let text = column.label + ' ' + ruleTextObj[filterGroupList.value[column.prop].rule] + dateText;
	handleFormatQueryList(column, text);
}

// 表头-排序搜索
function onSearchSort(column: any) {
	// DOING 添加time字段，是为了适配后期排序弹窗逻辑。排序弹窗中可以添加若干个排序条件，且存在先后顺序
	sortGroupList.value[column.prop].time = Moment().format('YYYY-MM-DD HH:mm:ss');

	emit('onSort', sortGroupList.value[column.prop]);

	// 关闭弹窗
	onClosePopover(column.prop);
}

/**
 * @description 构造搜索条件
 * @param column 字段列，相关配置
 * @param text 搜索提示文本
 */
function handleFormatQueryList(column, text) {
	if (filterGroupList.value[column.prop].value) {
		// 判断是否在查询列表中
		const index = queryList.value.findIndex((item) => item.value === column.prop);
		// 无则添加
		if (index == -1) {
			queryList.value.push({
				value: column.prop,
				label: text,
			});
		} else {
			// 有则更新
			queryList.value[index] = {
				value: column.prop,
				label: text,
			};
		}
	} else {
		// 从查询条件列表删除
		const index = queryList.value.findIndex((item) => item.value === column.prop);
		queryList.value.splice(index, 1);
	}

	// 触发回调函数
	emit('onSearch', filterGroupList.value[column.prop]);

	// 关闭弹窗
	onClosePopover(column.prop);
}

// 判断是否展示高亮搜索图标
function judgeHighlightSearchIcon(prop) {
	let highlight = false;
	if (filterGroupList.value[prop as string] && filterGroupList.value[prop as string].value) {
		highlight = true;
	}
	if (sortGroupList.value[prop as string] && sortGroupList.value[prop as string].value) {
		highlight = true;
	}
	return highlight;
}

// 清除单个条件
function clearQueryItem(value) {
	queryList.value = queryList.value.filter((item) => item.value !== value);
	filterGroupList.value[value].value = '';

	emit('onSearch', filterGroupList.value[value]);
}

// 清除所有条件
function clearQueryList() {
	console.log('clearQueryList');
	queryList.value.forEach((item) => {
		let key = item.value;
		if (key) {
			filterGroupList.value[key].value = '';
		}
	});
	queryList.value = [];

	emit('onClearAllSearch');
}
// #endregion

// #region 表头搜索控件，popover控制显隐

// ref集合
const itemRefs = ref({});

// 绑定搜索组件popover中的ref
const setItemRef = (id) => {
	return (el) => {
		if (el) {
			itemRefs.value[id] = el;
		} else {
			delete itemRefs.value[id];
		}
	};
};

// 关闭搜索弹出框
function onClosePopover(prop) {
	const popover = itemRefs.value[prop];
	if (popover) {
		popover.hide();
	}
}
// #endregion

// #region 批量操作逻辑

// 展示底部批量操作按钮
const showBatchOperation = ref<boolean>(false);

// 底部操作栏元素。后续用于控制表格高度
const batchOperationEle = ref<HTMLDivElement>();

// 批量操作按钮文本
const batchOperationBtnText = ref<string>('');

// 批量操作选中类型
var batchOperationType = '';

// 勾选选中列表
const multipleSelection = ref<any[]>([]);

// 点击批量操作回调
const onMultipleSelect = (item) => {
	formatBatchOperationData(item.label, item.value, true, []);
};

// 点击取消按钮
const onCancelBatchOperation = () => {
	formatBatchOperationData('', '', false, []);
};

/**
 * @description 构造批量操作数据
 * @param btnText 按钮文字
 * @param type 批量操作类型
 * @param isShowBatchOperation 是否显示批量操作
 * @param multipleData 多选数据
 */
const formatBatchOperationData = (btnText, type, isShowBatchOperation, multipleData) => {
	batchOperationBtnText.value = btnText;
	batchOperationType = type;
	showBatchOperation.value = isShowBatchOperation;
	emit('update:showCheckBox', isShowBatchOperation);
	multipleSelection.value = multipleData;

	// 关闭批量操作时。清除表格选择内容
	refTable.value?.clearSelection();
};

// 点击提交按钮
const onSubmitBatchOperation = () => {
	let title = '是否' + batchOperationBtnText.value + '选中的数据？';
	ElMessageBox.confirm(title, '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
	})
		.then(() => {
			emit('onSubmitBatchSelectionList', batchOperationType, multipleSelection.value);
			onCancelBatchOperation();
		})
		.catch(() => {});
};

// 多选操作回调，可在
const handleSelectionChange = (data: any[]) => {
	multipleSelection.value = data;
	emit('selectionChange', data);
};
// #endregion

defineExpose({
	handle: handle as Function,
	getRef: getRef,
});
</script>
<style lang="scss" scoped>
// 调整斑马纹样式
::v-deep .el-table {
	.el-table__row--striped {
		box-shadow: inset 0px -1px 0px 0px #e5e6eb;
		.el-table__cell {
			background: #f2f3f5;
		}
	}
	.el-table__row:hover {
		cursor: pointer;
		.el-table__cell {
			background-color: #f1f9ff;
		}
	}
	.el-table-column--selection {
		.cell {
			display: block !important;
		}
	}
}

.full-table {
	// height: 100%;
	flex: 1;
	max-height: 100%;
	min-height: 0;
}

.i-common-table-box {
	width: 100%;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	:deep(.el-table__column-filter-trigger) {
		margin-left: 4px;
	}
}

.t-header-min-height {
	min-height: 48px;
}

.t-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-shrink: 0;
	padding: 0 20px;
	position: relative;

	.t-header-left {
		display: flex;
		align-items: center;
		flex-grow: 1;

		.plus-icon {
			font-size: 16px;
			color: #ffffff;
			margin-right: 4px;
		}

		.table-total-num {
			font-family: MicrosoftYaHei;
			font-size: 14px;
			color: #86909c;
			line-height: 22px;
		}

		.add-m-r-16 {
			margin-right: 16px;
		}

		// 特殊，header下 tabs 的样式，
		> :deep(.el-tabs) {
			&:before {
				content: '';
				position: absolute;
				bottom: -1px;
				left: 0;
				right: 0;
				height: 2px;
				background-color: var(--el-border-color-light);
			}
		}
	}

	.t-header-right {
		display: flex;
		align-items: center;

		& div {
			margin-right: 8px;
		}
	}
}

.search-list-box {
	min-height: 48px;
	background: #ffffff;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	padding: 0 20px;
	.query-result {
		font-family: MicrosoftYaHei;
		font-size: 14px;
		color: #1d2129;
		line-height: 22px;
	}
	.query-cell {
		height: 28px;
		background: #ffffff;
		border-radius: 3px;
		border: 1px solid #e5e6eb;
		margin-right: 8px;
		padding: 4px 8px;
		box-sizing: border-box;
		display: flex;
		align-items: center;

		.cell-text {
			font-family: MicrosoftYaHei;
			font-size: 12px;
			color: #1d2129;
		}

		.cell-icon {
			font-size: 16px;
			cursor: pointer;
		}
	}
	.query-clear-all {
		font-family: MicrosoftYaHei;
		font-size: 14px;
		color: var(--el-color-primary);
		line-height: 22px;
		cursor: pointer;
	}
}

.t-refresh-icon {
	flex-shrink: 0;
	cursor: pointer;
	justify-items: flex-end;
	margin-left: 20px;
}
:deep(.t-cell-index) {
	.cell {
		padding: 0;
	}
}
.t-pagination {
	justify-content: flex-end;
	padding: 8px 12px;
	:deep(.el-pagination__rightwrapper) {
		flex-grow: 0;
	}
}
.t-search-header {
	display: inline-flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	cursor: pointer;
}
.t-search-icon {
	margin-left: 4px;
	&.t-search {
		color: var(--el-color-primary);
	}
}
.t-states-box {
	.t-states-icon {
		display: inline-block;
		height: 10px;
		width: 10px;
		border-radius: 50%;
		margin-right: 3px;
	}
}

.batch-operation-view {
	.checked-tip {
		color: var(--el-color-primary);
	}
	height: 48px;
	border-top: 1px solid #e5e6eb;
	margin-top: 10px;
	display: flex;
	align-items: center;
	padding: 0 20px;
	justify-content: space-between;
}

.custom-common-table {
	transition: height 0.3s ease;
}
</style>
<style lang="scss">
.common-table-header-cell {
	background-color: #f2f3f5 !important;
	border-right: 2px solid #ffffff !important;
	font-family: PingFangSC, PingFang SC;
	font-weight: 600;
	font-size: 15px;
	color: #1d2129;
	line-height: 24px;
	padding: 0 !important;
	height: 34px;
	.cell {
		padding: 0 7px;
	}
}

.common-table-row {
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	font-size: 14px;
	color: #1d2129;
	line-height: 22px;
	height: 34px;
	.el-table__cell {
		padding: 0;
		.cell {
			padding: 0 7px;
		}
	}
}

.custom-search-popover {
	padding: 0 !important;
}

.empty-box {
	display: flex;
	flex-direction: column;
	align-items: center;

	.empty-img {
		width: 120px;
		height: 120px;
		margin-bottom: 16px;
	}

	.empty-text {
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #86909c;
		line-height: 22px;
	}
}
</style>
