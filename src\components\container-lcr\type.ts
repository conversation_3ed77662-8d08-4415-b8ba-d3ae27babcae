import ContainerLcr from './index.vue';
declare global {
	type ContainerLcrInstance = InstanceType<typeof ContainerLcr>;
	interface ToggleContainerRightParams {
		data?: any; // 这个data 可以随意传入 会添加到right-box组件的插槽作用域中
		title?: string;
		isEdit?: boolean; // 是否是编辑状态， 如果是的话，下次再打开抽屉时，如果当前抽屉处于打开且编辑状态 就提示 默认false
		tips?: boolean | string; // 打开右侧时，如果右侧处于打开且编辑状态，是否需要提示 默认  '正在编辑中，确定放弃吗？'
		// handleType?: 'add' | 'view' | 'edit' | string; //
		[key: string]: any;
	}
	type ToggleContainerRight = (show?: boolean, data?: ToggleContainerRightParams) => void;
}
