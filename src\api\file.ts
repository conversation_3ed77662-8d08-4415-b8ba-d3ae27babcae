import { request } from '@/utils/service';

export const Api: any = {
	deleteFile: `/open-api/v1/file/delete`, //  测试地址11
	generate: `/open-api/v1/file/generate/uuid`,
};

/**
 * 删除文件
 * @param fileUuid
 * @returns
 */
export const deleteFile = (fileUuid: string) => {
	const url: string = Api.deleteFile;
	return request({
		url,
		method: 'delete',
		serviceName: 'VITE_BASE_FILE_API',
		params: { fileUuid },
	});
};

/**
 * 生成文件唯一标识
 * @returns
 */
export const generateFileUuid = () => {
	return request({
		url: Api.generate,
		serviceName: 'VITE_BASE_FILE_API',
	});
};
