<template>
	<el-input
		@change="onSearch"
		clearable
		:prefix-icon="Search"
		v-model="search"
		:placeholder="inputSearchPlaceholder"
		class="custom-query-input"
	>
	</el-input>
</template>

<script lang="tsx" setup>
import { Search } from '@element-plus/icons-vue';
import { ref } from 'vue';
const search = ref('');
interface Props {
	inputSearchPlaceholder: string;
}
const props = withDefaults(defineProps<Props>(), {
	inputSearchPlaceholder: '搜索',
});

const emit = defineEmits<{
	(e: 'onSearch', value: string): void;
}>();

const onSearch = () => {
	emit('onSearch', search.value);
};
</script>
<style lang="scss" scoped>
.custom-query-input {
	width: 200px;
}
</style>
