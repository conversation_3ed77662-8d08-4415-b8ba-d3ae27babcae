<template>
	<div class="container">
		<el-row>
			<el-col :span="24">
				<el-card class="card-view">
					<template #header>
						<div class="card-header">
							<span>全局状态管理</span>
						</div>
					</template>

					<h3>
						pinia（vue3中推荐使用pinia，类似vuex）
						<a
							href="https://pinia.web3doc.top/introduction.html"
							target="_blank"
							>pinia官网</a
						>
					</h3>
					<el-button @click="setTestInfo"> 设置testInfo </el-button>
					<div class="add-m-t-10">
						testInfo：
						{{ testStore.getTestInfo }}
					</div>
				</el-card>
			</el-col>
			<el-col :span="24" class="add-m-t-10">
				<el-card class="card-view">
					<template #header>
						<div class="card-header">
							<span>使用全局变量</span>
						</div>
					</template>

					<h4>在@/store/index.ts中定义响应式变量并导出，可以在任何模块中使用，并触发响应式更新。</h4>
					<el-button @click="setUserInfo"> 设置userInfo </el-button>
					<div class="add-m-t-10">当前登录用户信息： {{ storeUserInfo.username }}</div>
				</el-card>
			</el-col>
		</el-row>
	</div>
</template>

<script lang="ts" setup>
import { setStoreUserInfo, storeUserInfo } from '@/store';
import { useTestStore } from '@/store/modules/test';
import { watch } from 'vue';
const testStore = useTestStore();

function setTestInfo() {
	testStore.setTestInfo({
		a: Math.random() + '3434',
		b: Math.random(),
	});
}
watch(
	() => testStore.getTestInfo,
	(val) => {
		console.log('111111111111 testStore.getTestInfo', testStore.getTestInfo);
	},
);

function setUserInfo() {
	setStoreUserInfo({
		username: '用户名' + Math.random(),
	});
}
watch(
	() => storeUserInfo.username,
	(val) => {
		console.log('111111111111 storeUserInfo.userName', storeUserInfo.username);
	},
);
</script>
<style lang="scss" scoped>
.container {
	padding: 20px;
}

.add-m-t {
	margin-top: 100px;
}

.add-m-t-10 {
	margin-top: 10px;
}
</style>
