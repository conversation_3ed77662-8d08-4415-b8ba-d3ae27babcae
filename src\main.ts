import { createApp } from 'vue';
import { createPinia } from 'pinia';
import axios from 'axios';
import http from '@/utils/axios';
import App from './App.vue';
import { initRouter } from '@/router';
import moment from 'moment';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import { envConfig, isDev, rootOrigin } from '@/config';
import { reSize } from '@/store';
import 'element-plus/es/components/message/style/css';
import 'element-plus/es/components/message-box/style/css';
import { imports } from '@/utils/imports';

axios.get(`${rootOrigin}/config.json?v_=${Math.random()}`).then(async (info) => {
	Object.assign(envConfig, info?.data || {});
	const app = createApp(App);
	let routerInstance: any = null;
	routerInstance = initRouter();

	// 导入所有图标并进行全局注册
	for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
		app.component(key, component);
	}

	app.use(routerInstance);
	app.use(createPinia());
	if (isDev) {
		imports(app);
	}
	reSize();
	app.config.globalProperties.$moment = moment;
	http.setOriginHref(isDev ? '' : envConfig.VITE_BASE_API);
	http.setOriginHrefIDAAS(isDev ? '' : envConfig.VITE_BASE_API2);
	routerInstance.isReady().then(() => app.mount('#app'));
	setTimeout(function () {
		G_loading.value = false;
	}, 0);
});
