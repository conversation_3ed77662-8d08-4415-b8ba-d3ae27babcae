<template>
	<div class="container">
		<el-card style="width: 480px">
			<template #header>
				<div class="card-header">
					<span>确认消息</span>
				</div>
			</template>
			<div>
				<el-button
					type="primary"
					@click="onOperateConfirm"
					>重要操作确认</el-button
				>
				<el-button
					@click="onDeleteConfirm"
					type="primary"
					>删除确认</el-button
				>
			</div>
		</el-card>
		<el-card style="width: 480px; margin-left: 20px">
			<template #header>
				<div class="card-header">
					<span>消息提示</span>
				</div>
			</template>
			<div>
				<el-button
					@click="onSuccessMsgTip"
					type="primary"
					>成功提示</el-button
				>
				<el-button @click="onErrorMsgTip" type="primary">失败提示</el-button>
			</div>
		</el-card>
	</div>
</template>
<script lang="tsx" setup>
import { operateConfirm, deleteConfirm, successMsgTip, errorMsgTip } from '@/utils/confirm';

// 操作确认
const onOperateConfirm = () => {
	let title = '确认操作吗？';
	operateConfirm(title)
		.then((res) => {
			console.log('点击了确认');
		})
		.catch((err) => {
			console.log('点击了取消');
		});
};

// 删除确认
const onDeleteConfirm = () => {
	let title = '确认删除吗？';
	deleteConfirm(title)
		.then((res) => {
			console.log('点击了确认');
		})
		.catch((err) => {
			console.log('点击了取消');
		});
};

// 成功消息提示
const onSuccessMsgTip = () => {
	successMsgTip();
};

// 失败消息提示
const onErrorMsgTip = () => {
	errorMsgTip();
};

</script>
<style lang="scss" scoped>
.container {
	padding: 20px;
	display: flex;
}
</style>
