<template>
	<el-popover placement="right" trigger="click" popper-class="custom-message-center-popover">
		<template #reference>
			<li class="el-menu-item">
				<img :src="imgUrl" />
				<span class="t-text" title="消息通知">消息通知</span>
			</li>
		</template>
		<div class="message-list-content">
			<div class="header">消息通知</div>
			<div class="main">
				<el-tabs v-model="activeName" class="message-center-tabs" @tab-click="handleClick">
					<el-tab-pane label="系统通知" name="first">
						<div class="item-cell" v-for="(item, index) in options" :key="index">
							<div class="item-title">{{ item.label }}</div>
							<div class="item-time">{{ item.time }}</div>
						</div>
					</el-tab-pane>
					<el-tab-pane label="任务进度" name="second">Config</el-tab-pane>
					<el-tab-pane label="成员消息" name="third">Role</el-tab-pane>
				</el-tabs>
			</div>
		</div>
	</el-popover>
</template>
<script lang="tsx" setup>
import { PropType, ref, watch } from 'vue';
import type { TabsPaneContext } from 'element-plus';
const props = defineProps({
	systemTheme: {
		type: String,
	},
});

const options = [
	{
		time: '2024-05-12 12:21:00',
		label: '成员权限变更',
	},
	{
		time: '2024-05-12 12:21:00',
		label: '成员权限变更：张三 已将 李四 的项目角色调整为成员权限变更：张三 已将 李四 的项目角色调整为成员权限变更：张三 已将 李四 的项目角色调整为',
	},
	{
		time: '2024-05-12 12:21:00',
		label: '成员权限变更：张三 已将 李四 的项目角色调整为',
	},
	{
		time: '2024-05-12 12:21:00',
		label: '成员权限变更：张三 已将 李四 的项目角色调整为',
	},
	{
		time: '2024-05-12 12:21:00',
		label: '成员权限变更：张三 已将 李四 的项目角色调整为',
	},
	{
		time: '2024-05-12 12:21:00',
		label: '成员权限变更：张三 已将 李四 的项目角色调整为',
	},
	{
		time: '2024-05-12 12:21:00',
		label: '成员权限变更：张三 已将 李四 的项目角色调整为',
	},
];

var imgUrl = ref('');
// 设置菜单图标
watch(
	() => props.systemTheme,
	(val) => {
		imgUrl.value = new URL(`/src/assets/images/menu/${val}-message-center.png`, import.meta.url).href;
	},
	{
		immediate: true,
	},
);

const activeName = ref('first');
const handleClick = (tab: TabsPaneContext, event: Event) => {
	console.log(tab, event);
};
</script>
<style lang="scss" scoped>
.message-list-content {
	width: 500px;
	height: 338px;
	display: flex;
	flex-direction: column;
	.header {
		height: 48px;
		padding: 12px 20px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 16px;
		color: #1d2129;
		line-height: 24px;
		border-bottom: 1px solid #e5e6eb;
		box-sizing: border-box;
	}

	.main {
		flex: 1;
		overflow: auto;
		min-height: 0;

		.item-cell {
			height: 48px;
			background: #ffffff;
			cursor: pointer;
			display: flex;
			padding: 12px 20px;
			justify-content: space-between;
			box-sizing: border-box;
			&:hover {
				background: #f2f3f5;
			}
			.item-title {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #303133;
				line-height: 22px;
				flex: 1;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				margin-right: 10px;
			}

			.item-time {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 14px;
				color: #86909c;
				line-height: 22px;
			}
		}
	}
}
</style>
<style lang="scss">
.custom-message-center-popover {
	width: auto !important;
	padding: 0 !important;
}

.message-center-tabs {
	.el-tabs__nav {
		padding: 0 20px !important;
		.el-tabs__item {
			height: 48px;
			line-height: 48px;
		}
	}
	.el-tabs__nav-wrap:after {
		height: 1px !important;
	}
}
</style>
