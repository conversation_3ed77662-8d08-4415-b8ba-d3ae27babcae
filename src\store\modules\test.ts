import { defineStore } from 'pinia';
import { resolveFunc } from '@/utils/axios';

interface TestInfo {
	a: string;
	b: number;
}
interface TestState {
	a: string;
	testInfo: Nullable<TestInfo>;
}

export const useTestStore = defineStore({
	id: 'app-test',
	state: (): TestState => {
		return {
			a: '1111',
			testInfo: {
				a: '111',
				b: 111,
			},
		};
	},
	getters: {
		getTestInfo(): Nullable<TestInfo> {
			return this.testInfo;
		},
	},
	actions: {
		setTestInfo(info: TestInfo) {
			this.testInfo = Object.assign({}, this.testInfo, info);
		},
		resetTestInfo() {
			this.testInfo = null;
		},

		async setTestInfoAsync() {
			try {
				this.testInfo = await resolveFunc(
					{
						a: this.testInfo?.a + '22',
						b: (this.testInfo?.b || 10) + 10,
					},
					3000,
				);
			} catch (error) {
				console.log('setTestInfoAsync error', error);
				return error;
			}
		},
	},
});
