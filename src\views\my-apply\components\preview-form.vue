<template>
	<div class="edit-form">
		<div class="main">
			<el-tabs
				v-model="activeName"
				class="demo-tabs"
				@tab-click="handleClick"
			>
				<el-tab-pane
					label="表单信息"
					name="first"
				>
				</el-tab-pane>
				<el-tab-pane
					label="审批进程"
					name="second"
					><common-table
						:key="tableKey"
						showIndex
						:loading="loading"
						:columns="tableColumnsCommon"
						:fill-screen="true"
						:data="tableData2"
						:show-total="false"
						hideHeader
					>
						<template #slotName1="{ row, $index }">
							<div class="approval-name">
								<div class="left-name">
									{{ row.executorName }}
								</div>
								<div
									class="right-moniter"
									v-if="
										(row.moniterNum && isTimeDifferenceMoreThanTwoHours(row.endDate)) ||
										(row.isFirstTwenty && !row.moniterNum)
									"
									@click="handleClickMoniter(row, $index)"
								>
									<div class="image7"></div>
									<div class="text">催一下</div>
								</div>
								<div
									v-else-if="row.moniterNum"
									class="moniter-text"
								>
									<div class="image6"></div>
									<div class="text">已催{{ row.moniterNum }}次</div>
								</div>
							</div>
						</template>
						<template #slotName2="{ row }">
							<div class="approval-status">
								<div
									class="icon"
									:class="{
										image1: row.actionTypeId === '10',
										image2: row.actionTypeId === '30',
										image3: row.actionTypeId === '50',
										image4: row.actionTypeId === '20',
										image5: row.actionTypeId === '40',
									}"
								></div>
								<div
									class="type-name"
									:class="{
										color1: row.actionTypeId === '10',
										color2: row.actionTypeId === '30',
										color3: row.actionTypeId === '50',
										color4: row.actionTypeId === '20',
										color5: row.actionTypeId === '40',
									}"
								>
									{{ row.actionTypeName }}
								</div>
							</div>
						</template>
					</common-table></el-tab-pane
				>
				<el-tab-pane
					label="会签表"
					name="third"
					>会签表</el-tab-pane
				>
			</el-tabs>
		</div>
		<div class="footer">
			<div class="footer-view">
				<el-button>取消</el-button>
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
import { reactive, ref, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules, TabsPaneContext } from 'element-plus';
import { processArray, isTimeDifferenceMoreThanTwoHours, markFirstTwenty } from '../api/common';
import { api_postApprovalRemind, getTableDataInfoApi } from '../api';

interface Props {
	rowData: any;
}
const props = withDefaults(defineProps<Props>(), {});
const activeName = ref('first');
// 表格列名
const tableColumnsCommon: CommonTableColumn<any>[] = [
	{
		label: '节点名称',
		prop: 'executorRole',
	},
	{
		label: '审批人',
		prop: 'executorName',
		slotName: 'slotName1',
	},
	{
		label: '审批结果',
		prop: 'actionTypeName',
		slotName: 'slotName2',
		// formatter(row, column, cellValue, index) {
		// 	return cellValue == '30' ? '通过' : cellValue == '40' ? '退回' : cellValue == '50' ? '撤回' : '待审批';
		// },
	},
	{
		label: '审批意见',
		prop: 'opinion',
	},
	{
		label: '审批时间',
		prop: 'approvalDate',
	},
];
const loading = ref(false);
const tableData2 = ref<any[]>([]);
const tableKey = ref(0);
const actveRowInfo = ref<any>();
//详情附件tab类型
const handleClick = (tab: TabsPaneContext, event: Event) => {
	console.log(tab, event, activeName.value);
	switch (tab.props.name) {
		case 'first':
			break;
		case 'second':
			tableData2.value = markFirstTwenty(processArray(actveRowInfo.value.extra_flowRecords) || []);
			tableKey.value++;
			break;
		default:
			break;
	}
};
//催办
function handleClickMoniter(row: any, index: number) {
	api_postApprovalRemind(actveRowInfo.value.rowID).then((res) => {});
}
watch(
	() => props.rowData,
	(value) => {
		console.log(value);
		getTableDataInfoApi({
			id: props.rowData.rowID,
			isPopFlowRecords: 1,
		}).then((res) => {
			actveRowInfo.value = res[0];
			switch (activeName.value) {
				case 'first':
					break;
				case 'second':
					tableData2.value = markFirstTwenty(processArray(res[0].extra_flowRecords) || []);
					tableKey.value++;
					break;
				default:
					break;
			}
		});
	},
	{
		immediate: true,
		deep: true,
	},
);
</script>
<style lang="scss" scoped>
.edit-form {
	display: flex;
	flex-direction: column;
	height: 100%;
	.main {
		flex: 1;
		min-height: 0;
		overflow: auto;

		.form-view {
			width: 777px;
			margin: 20px auto;
		}
		.demo-tabs {
			:deep(.el-tabs__nav-wrap) {
				height: 48px;
				padding-left: 20px;
			}

			:deep(.el-tabs__nav) {
				height: 48px;
			}

			:deep(.el-tabs__active-bar) {
				height: 3px;
			}
		}

		.approval-name {
			display: flex;

			.left-name {
				font-size: 14px;
				color: #000000;
				font-weight: 400;
				margin-right: 12px;
			}

			.right-moniter {
				border-left: 2px solid rgba(229, 230, 235, 1);
				padding-left: 12px;
				display: flex;
				align-items: center;

				.image7 {
					width: 13.33px;
					height: 15px;
					background-image: url('@/assets/images/my-apply/image7.png');
					background-size: 100% 100%;
					background-position: center;
					background-repeat: no-repeat;
				}

				.text {
					color: rgba(47, 60, 244, 1);
					margin-left: 6px;
				}
			}

			.moniter-text {
				border-left: 2px solid rgba(229, 230, 235, 1);
				padding-left: 12px;
				display: flex;
				align-items: center;

				.image6 {
					width: 13.33px;
					height: 15px;
					background-image: url('@/assets/images/my-apply/image6.png');
					background-size: 100% 100%;
					background-position: center;
					background-repeat: no-repeat;
				}

				.text {
					margin-left: 6px;
					color: rgba(134, 144, 156, 1);
				}
			}
		}

		.approval-status {
			display: flex;
			align-items: center;

			.icon {
				width: 16.2px;
				height: 16px;
				background-size: 100% 100%;
				background-position: center;
				background-repeat: no-repeat;
			}

			.image1 {
				background-image: url('@/assets/images/my-apply/image1.png');
			}

			.image2 {
				background-image: url('@/assets/images/my-apply/image2.png');
			}

			.image3 {
				background-image: url('@/assets/images/my-apply/image3.png');
			}

			.image4 {
				background-image: url('@/assets/images/my-apply/image4.png');
			}

			.image5 {
				background-image: url('@/assets/images/my-apply/image5.png');
			}

			.color1 {
				color: #0060c1;
			}

			.color2 {
				color: rgba(25, 190, 107, 1);
			}

			.color3 {
				color: rgba(255, 153, 0, 1);
			}

			.color4 {
				color: rgba(45, 183, 245, 1);
			}

			.color5 {
				color: rgba(237, 64, 20, 1);
			}

			.type-name {
				margin-left: 4px;
				font-size: 14px;
				font-weight: 400;
			}
		}
	}
	.footer {
		border-top: 1px solid #e5e6eb;
		height: 64px;
		display: flex;
		align-items: center;
		.footer-view {
			width: 777px;
			margin: 0 auto;
		}
	}
}
</style>
<style lang="scss">
.custom-example-descriptions {
	.el-descriptions__label {
		width: 120px;
	}
}
</style>
