<template>
	<div class="h-full">
		<header-main v-if="viewType == 'list'">
			<template #header-right>
				<el-button type="">导出</el-button>
				<el-button
					type="primary"
					@click="createButtonClick"
					>发起申请</el-button
				>
			</template>
			<template #main>
				<div class="main-out-wrap">
					<div
						class="top-table-wrap"
						:style="{ height: tableHeight }"
					>
						<common-table
							:pagination="true"
							showIndex
							:loading="loading"
							:columns="tableColumns"
							:fill-screen="true"
							:data="tableData"
							:btnsMoreNumber="3"
							:getBtns="btns"
							:total="pageInfo.total"
							:pageSize="pageInfo.pageSize"
							:pageNumber="pageInfo.pageNumber"
							:show-total="false"
							:height="(tableHeight! - 100) || '100%'"
							@row-click="handleClickRow"
							highlight-current-row
							ref="refTable"
						>
							<template #header-left>
								<div class="left-type-wrap">
									<div
										class="type-item"
										:class="{ active: activeTypeIndex == 20 }"
										@click="activeTypeIndex = 20"
									>
										进行中
									</div>
									<div
										class="type-item"
										:class="{ active: activeTypeIndex == 30 }"
										@click="activeTypeIndex = 30"
									>
										已生效
									</div>
									<div
										class="type-item"
										:class="{ active: activeTypeIndex == 10 }"
										@click="activeTypeIndex = 10"
									>
										全部
									</div>
								</div>
							</template>
							<template #header-right> 右边插槽</template>
							<template #slotStatus="{ row }">
								<div
									class="state-card"
									:style="{
										color: statusMap[row.gState].color,
										borderColor: statusMap[row.gState].color,
										backgroundColor: statusMap[row.gState].backgroundColor,
										width: row.gState == 40 ? '100px' : '52px',
									}"
								>
									{{ statusMap[row.gState].label }}
								</div>
							</template>
						</common-table>
					</div>
					<div class="bottom-table-wrap">
						<div
							class="resizeBtn"
							@mousedown="resizeDown"
							@mouseup="mouseup"
						>
							<div class="line"></div>
							<span class="resize-span"><i class="el-icon-d-caret"></i></span>
						</div>

						<el-tabs
							v-model="activeName"
							class="demo-tabs"
							@tab-click="handleClick"
						>
							<el-tab-pane
								label="附件列表"
								name="first"
								><common-table
									:key="tableKey"
									showIndex
									:loading="loading"
									:columns="tableColumnsCommon"
									:fill-screen="true"
									:data="tableData2"
									:btnsMoreNumber="3"
									:getBtns="btns2"
									:show-total="false"
									hideHeader
									:height="(tableHeight2! - 140) || '100%'"
								>
								</common-table
							></el-tab-pane>
							<el-tab-pane
								label="审批进程"
								name="second"
								><common-table
									:key="tableKey"
									showIndex
									:loading="loading"
									:columns="tableColumnsCommon"
									:fill-screen="true"
									:data="tableData2"
									:show-total="false"
									hideHeader
									:height="(tableHeight2! - 140) || '100%'"
								>
									<template #slotName1="{ row, $index }">
										<div class="approval-name">
											<div class="left-name">
												{{ row.executorName }}
											</div>
											<div
												class="right-moniter"
												v-if="
													(row.moniterNum && isTimeDifferenceMoreThanTwoHours(row.endDate)) ||
													(row.isFirstTwenty && !row.moniterNum)
												"
												@click="handleClickMoniter(row, $index)"
											>
												<div class="image7"></div>
												<div class="text">催一下</div>
											</div>
											<div
												v-else-if="row.moniterNum"
												class="moniter-text"
											>
												<div class="image6"></div>
												<div class="text">已催{{ row.moniterNum }}次</div>
											</div>
										</div>
									</template>
									<template #slotName2="{ row }">
										<div class="approval-status">
											<div
												class="icon"
												:class="{
													image1: row.actionTypeId === '10',
													image2: row.actionTypeId === '30',
													image3: row.actionTypeId === '50',
													image4: row.actionTypeId === '20',
													image5: row.actionTypeId === '40',
												}"
											></div>
											<div
												class="type-name"
												:class="{
													color1: row.actionTypeId === '10',
													color2: row.actionTypeId === '30',
													color3: row.actionTypeId === '50',
													color4: row.actionTypeId === '20',
													color5: row.actionTypeId === '40',
												}"
											>
												{{ row.actionTypeName }}
											</div>
										</div>
									</template>
								</common-table></el-tab-pane
							>
							<el-tab-pane
								label="会签表"
								name="third"
								>会签表</el-tab-pane
							>
						</el-tabs>
					</div>
				</div>
			</template>
		</header-main>

		<header-main
			v-if="viewType == 'operate'"
			viewTitle="创建"
			@close="closeDetailPage"
			:showCloseBtn="true"
		>
			<template #main>
				<EditForm />
			</template>
		</header-main>
		<header-main
			v-if="viewType == 'preview'"
			viewTitle="单据详情"
			@close="closeDetailPage"
			:showCloseBtn="true"
		>
			<template #main>
				<PreviewForm :row-data="activeRow" />
			</template>
		</header-main>
		<apply-type-dialog
			v-model:visible="typeDialogVisible"
			:dataList="typeDialogData"
			@next="typeDialogNext"
		/>

		<contract-form
			v-if="viewType == 'CB021'"
			:type="formType"
			:row-i-d="formRowID"
			@close="closeDetailPage"
			@success="dataChange"
		/>
		<certificate-form
			v-if="viewType == 'CB024'"
			:type="formType"
			:row-i-d="formRowID"
			@close="closeDetailPage"
			@success="dataChange"
		/>
		<pay-form
			v-if="viewType == 'CB025'"
			:type="formType"
			:row-i-d="formRowID"
			@close="closeDetailPage"
			@success="dataChange"
		/>
		<procurement-form
			v-if="viewType == 'CB029'"
			:type="formType"
			:row-i-d="formRowID"
			@close="closeDetailPage"
			@success="dataChange"
		/>
		<out-form
			v-if="viewType == 'CB031'"
			:type="formType"
			:row-i-d="formRowID"
			@close="closeDetailPage"
			@success="dataChange"
		/>
		<RebackDialog
			:title="'撤回'"
			v-model:visible="RebackDialogShow"
			@sure="handleSubmitBack"
		></RebackDialog>
	</div>
</template>
<script setup lang="tsx">
import EditForm from './components/edit-form.vue';
import PreviewForm from './components/preview-form.vue';
import { ref, reactive, onMounted, watch, watchEffect } from 'vue';
import { useRoute } from 'vue-router';
import ApplyTypeDialog from './components/apply-type-dialog.vue';
import ContractForm from './components/contract-form.vue';
import CertificateForm from './components/certificate-form.vue';
import payForm from './components/pay-form.vue';
import procurementForm from './components/procurement-form.vue';
import outForm from './components/out-form.vue';
import { getApprovalCategoryListApi } from '@/views/business-type/api';
import {
	api_postApprovalDelete,
	api_postApprovalRemind,
	api_postApprovalSubmit,
	api_postApprovalWithdraw,
	getTableDataApi,
	getTableDataInfoApi,
} from './api/index';
import { statusMap } from '@/api/common';
import type { TabsPaneContext } from 'element-plus';
import { nextTick } from 'vue';
import { processArray, isTimeDifferenceMoreThanTwoHours, markFirstTwenty } from './api/common';
const route = useRoute();

onMounted(() => {
	console.log('111111111111');
	console.log(route);
});
const loading = ref(false);
const RebackDialogShow = ref(false);
interface TreeDataItem {
	label: string; // 标题名称
	isCollect?: boolean; // 是否为收藏
	num?: string;
	children?: TreeDataItem[]; // 子级
}

// 发起申请弹窗显隐
const typeDialogVisible = ref<boolean>(false);
const typeDialogData = ref<any[]>([]);

// 表单类型（增加/修改合同表单、增加/修改增加证照使用表单、增加/修改修改付款申请表单、增加/修改招采需求表单、增加/修改出差申请表单、增加/修改差旅报销表单）
const formType = ref<string>('add');
// 表单id（增加/修改合同表单、增加/修改增加证照使用表单、增加/修改修改付款申请表单、增加/修改招采需求表单、增加/修改出差申请表单、增加/修改差旅报销表单）
const formRowID = ref<string>('');
// // 增加/修改合同表单
// const contractFormType = ref<string>('add');
// // const contractFormTitle = ref<string>('增加合同审批');
// // 增加/修改增加证照使用表单
// const certificateFormType = ref<string>('add');
// // const certificateFormTitle = ref<string>('增加增加证照使用');
// // 增加/修改修改付款申请表单
// const payFormType = ref<string>('add');
// // const payFormTitle = ref<string>('增加付款申请');
// // 增加/修改招采需求表单
// const procurementFormType = ref<string>('add');
// // const procurementFormTitle = ref<string>('增加招采需求');
// // 增加/修改出差申请表单
// const outFormType = ref<string>('add');
// // const outFormTitle = ref<string>('增加出差申请');
// // 增加/修改差旅报销表单
// const travelFormType = ref<string>('add');
// // const travelFormTitle = ref<string>('增加差旅报销');

// 发起申请弹窗下一步
function typeDialogNext(val) {
	enterFormPage(val, 'add');
}
// 进入表单页
function enterFormPage(val, type = 'add') {
	console.log('下一步', val);
	setFormConfig(val, type);
	let tempType = 'list';
	if (
		val.cNumber === 'CB021' ||
		val.cNumber === 'CB024' ||
		val.cNumber === 'CB025' ||
		val.cNumber === 'CB029' ||
		val.cNumber === 'CB031' ||
		val.cNumber === 'CB032'
	) {
		tempType = val.cNumber;
	}

	viewType.value = tempType;
}

// 设置表单type和title
function setFormConfig(val, type) {
	// // let prefixTitle = '增加';
	// // if (type === 'edit') {
	// // 	prefixTitle = '修改';
	// // }
	// contractFormType.value = type;
	// // contractFormTitle.value = prefixTitle + '合同审批';
	// certificateFormType.value = type;
	// // certificateFormTitle.value = prefixTitle + '证照使用';
	// payFormType.value = type;
	// // payFormTitle.value = prefixTitle + '付款申请';
	// procurementFormType.value = type;
	// // procurementFormTitle.value = prefixTitle + '招采需求';
	// outFormType.value = type;
	// // outFormTitle.value = prefixTitle + '出差申请';
	// travelFormType.value = type;
	// // travelFormTitle.value = prefixTitle + '差旅报销';
	if (type === 'add') {
		formRowID.value = '';
	} else {
		formRowID.value = val.rowID;
	}
	formType.value = type;
}
// 分页数据
const pageInfo = reactive<PageInfo>({
	total: 3,
	pageNumber: 1,
	pageSize: 20,
});
//获取列表数据
function getList() {
	loading.value = true;
	getTableDataApi({
		actionType: 10,
		pageNumber: pageInfo.pageNumber,
		pageSize: pageInfo.pageSize,
		states: states.value,
	})
		.then((res) => {
			tableData.value = res[0].records;
			pageInfo.total = res[0].totalCount;
		})
		.finally(() => {
			loading.value = false;
		});
}
// 右侧表格数据
const tableData = ref<any[]>([]);
const tableData2 = ref<any[]>([]);
const activeTypeIndex = ref(20);
const states = ref('');
const types = ref('');
watch(
	() => activeTypeIndex.value,
	async (val) => {
		switch (val) {
			case 20:
				states.value = '20';
				break;
			case 30:
				states.value = '30,40';
				break;
			case 10:
				states.value = '10,20,30,40,15,18';
				break;
		}
		await getList();
	},
	{
		immediate: true,
	},
);
watchEffect(() => {
	if (tableData.value.length > 0) {
		nextTick(() => {
			// handleClickRow(tableData.value[0]);
			// 方法2：通过DOM触发点击事件（如果需要样式效果）
			const firstRow = refTable.value?.$el.querySelector('.el-table__body tr');
			firstRow?.click();
		});
	}
});
const refTable = ref<any>();
const activeRow = ref<any>();
const tableHeight = ref();
const tableHeight2 = ref();
function resizeDown(e) {
	mouseResizeDown(e, 'bottom-table-wrap', 'top-table-wrap', 140, 100);
}
function mouseResizeDown(e, class1, class2, num1 = 140, num2 = 100) {
	console.log('mousedown');
	var targetDiv = document.getElementsByClassName(class1)[0] as any;
	var targetDiv1 = document.getElementsByClassName(class2)[0] as any;
	var targetDivHeight = targetDiv.offsetHeight;
	var targetDivHeight1 = targetDiv1.offsetHeight;
	tableHeight.value = targetDivHeight1;
	tableHeight2.value = targetDivHeight;
	var height = targetDivHeight + targetDivHeight1;
	var startY = e.clientY;
	document.onmousemove = function (e) {
		e.preventDefault();
		//得到鼠标拖动的宽高距离：取绝对值
		var distY = Math.abs(e.clientY - startY);
		//往上方拖动：
		if (e.clientY < startY) {
			if (targetDiv1.style.height === num2 + 'px') return false;
			targetDiv.style.height = targetDivHeight + distY + 'px';
			targetDiv1.style.height = targetDivHeight1 - distY + 'px';
		}
		//往下方拖动：
		if (e.clientY > startY) {
			if (targetDiv.style.height === num1 + 'px') return false;
			targetDiv.style.height = targetDivHeight - distY + 'px';
			targetDiv1.style.height = targetDivHeight1 + distY + 'px';
		}
		//设置最大限制高度
		if (parseInt(targetDiv.style.height) >= height - num2) {
			targetDiv.style.height = height - num2 + 'px';
			targetDiv1.style.height = num2 + 'px';
		}
		//限制最小限制高度
		if (parseInt(targetDiv.style.height) <= num1) {
			targetDiv.style.height = num1 + 'px';
			targetDiv1.style.height = height - num1 + 'px';
		}
		let top = document.getElementsByClassName(class2)[0] as any;
		let topHeight = top.offsetHeight;
		let bottom = document.getElementsByClassName(class1)[0] as any;
		let bottomHeight = bottom.offsetHeight;
		tableHeight.value = topHeight;
		tableHeight2.value = bottomHeight;
	};
	document.onmouseup = function () {
		document.onmousemove = null;
		let top = document.getElementsByClassName(class2)[0] as any;
		let topHeight = top.offsetHeight;
		let bottom = document.getElementsByClassName(class1)[0] as any;
		let bottomHeight = bottom.offsetHeight;
		tableHeight.value = topHeight;
		tableHeight2.value = bottomHeight;
	};
}
function mouseup() {
	console.log('mouseup');
	var targetDiv1 = document.getElementsByClassName('top-table-wrap')[0] as any;
	var targetDivHeight1 = targetDiv1.offsetHeight;
	tableHeight.value = targetDivHeight1;
	var targetDiv12 = document.getElementsByClassName('bottom-table-wrap')[0] as any;
	var targetDivHeight2 = targetDiv12.offsetHeight;
	tableHeight2.value = targetDivHeight2;
}

// 表格列名
const tableColumns: CommonTableColumn<any>[] = [
	{
		label: '所属业务',
		prop: 'cName',
	},
	{
		label: '内容',
		prop: 'bDescription',
	},
	{
		label: '状态',
		prop: 'gState',
		slotName: 'slotStatus',
	},
	{
		label: '单位/部门',
		prop: 'companyName',
		formatter(row, column, cellValue, index) {
			return row.companyName + '/' + row.deptName;
		},
	},
	{
		label: '创建时间',
		prop: 'createDate',
	},
	{
		label: '提交时间',
		prop: 'submitDate',
	},
	{
		label: '生效时间',
		prop: 'validDate',
	},
];

//操作按钮
const btns: OptionBtn<any>[] = [
	{
		label: '详情',
		auth: '',
		onClick(row) {
			console.log('点击了按钮', row);
			activeRow.value = row;
			viewType.value = 'preview';
		},
	},
	{
		label: '修改',
		auth: '',
		hide: (row) => {
			return row.gState !== 10 && row.gState !== 15 && row.gState !== 18;
		},
		onClick(row) {
			console.log('点击了按钮', row);
			enterFormPage(row, 'edit');
		},
	},
	{
		label: '提交',
		auth: '',
		hide: (row) => {
			return row.gState !== 10 && row.gState !== 15 && row.gState !== 18;
		},
		onClick(row) {
			console.log('点击了按钮', row);
			api_postApprovalSubmit(row.rowID).then((res) => {
				getList();
			});
		},
	},
	{
		label: '撤回',
		auth: '',
		hide: (row) => {
			return row.gState !== 20;
		},
		onClick(row) {
			console.log('点击了按钮', row);
			activeRow.value = row;
			RebackDialogShow.value = true;
		},
	},
	{
		label: '催办',
		auth: '',
		hide: (row) => {
			return row.gState !== 20;
		},
		onClick(row) {
			console.log('点击了按钮', row);
			api_postApprovalRemind(row.rowID).then((res) => {
				getList();
			});
		},
	},
	{
		label: '删除',
		auth: '',
		hide: (row) => {
			return row.gState !== 10 && row.gState !== 15 && row.gState !== 18;
		},
		onClick(row) {
			console.log('点击了按钮', row);
			api_postApprovalDelete(row.rowID).then((res) => {
				getList();
			});
		},
	},
];
// 表格列名
const tableColumnsCommon = ref<any[]>([]);
// 表格列名
const tableColumns1: CommonTableColumn<any>[] = [
	{
		label: '文件名称',
		prop: 'title',
		formatter(row, column, cellValue, index) {
			return cellValue.split('.')[0] || '-';
		},
	},
	{
		label: '文件类型',
		prop: 'extendName',
	},
	{
		label: '文件大小',
		prop: 'size',
		formatter(row, column, cellValue, index) {
			if (cellValue < 1024) {
				return cellValue + 'B';
			} else if (cellValue < 1024 * 1024) {
				let temp = cellValue / 1024;
				temp = Number(temp.toFixed(2));
				return temp + 'kB';
			} else if (cellValue < 1024 * 1024 * 1024) {
				let temp1 = cellValue / (1024 * 1024);
				temp1 = Number(temp1.toFixed(2));
				return temp1 + 'MB';
			} else {
				let temp2 = cellValue / (1024 * 1024 * 1024);
				temp2 = Number(temp2.toFixed(2));
				return temp2 + 'GB';
			}
		},
	},
	{
		label: '上传时间',
		prop: 'creationDate',
	},
];
const tableColumns2: CommonTableColumn<any>[] = [
	{
		label: '节点名称',
		prop: 'executorRole',
	},
	{
		label: '审批人',
		prop: 'executorName',
		slotName: 'slotName1',
	},
	{
		label: '审批结果',
		prop: 'actionTypeName',
		slotName: 'slotName2',
		// formatter(row, column, cellValue, index) {
		// 	return cellValue == '30' ? '通过' : cellValue == '40' ? '退回' : cellValue == '50' ? '撤回' : '待审批';
		// },
	},
	{
		label: '审批意见',
		prop: 'opinion',
	},
	{
		label: '审批时间',
		prop: 'approvalDate',
	},
];

//操作按钮
const btns2: OptionBtn<any>[] = [
	{
		label: '打印',
		auth: '',
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
	{
		label: '下载',
		auth: '',
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
	{
		label: '预览',
		auth: '',
		onClick(row) {
			console.log('点击了按钮', row);
		},
	},
];
// 展示详细页面
const viewType = ref<string>('list'); // 页面类型。【list 列表】【preview 预览】【operate 操作（新增/编辑）】

function onPageCurrentChange(val: number) {
	pageInfo.pageNumber = val;
}

function createButtonClick(type) {
	// viewType.value = 'operate';
	typeDialogVisible.value = true;
}

// 关闭详情页面
function closeDetailPage() {
	viewType.value = 'list';
	typeDialogVisible.value = false;
}

// 预览
const onPreview = () => {
	viewType.value = 'preview';
};

// 数据变化，刷新页面
function dataChange() {
	closeDetailPage();
	// 刷新页面
}

// 初始化数据
onMounted(() => {
	getTypeList();
	let targetDiv1 = document.getElementsByClassName('top-table-wrap')[0] as any;
	var targetDivHeight1 = targetDiv1.offsetHeight;
	tableHeight.value = targetDivHeight1;
	tableHeight2.value = targetDivHeight1;
});

// 获取业务类型列表
const getTypeList = () => {
	// { types: 'generic_approval' }
	getApprovalCategoryListApi({})
		.then((res: any) => {
			console.log('typeDialogData', res);
			typeDialogData.value = res;
		})
		.finally(() => {});
};
const activeName = ref('first');
const actveRowInfo = ref<any>();
const tableKey = ref(0);
//详情附件tab类型
const handleClick = (tab: TabsPaneContext, event: Event) => {
	console.log(tab, event, activeName.value);
	switch (tab.props.name) {
		case 'first':
			tableColumnsCommon.value = tableColumns1;
			tableData2.value = actveRowInfo.value.extra_files || [];
			tableKey.value++;
			break;
		case 'second':
			tableColumnsCommon.value = tableColumns2;
			tableData2.value = markFirstTwenty(processArray(actveRowInfo.value.extra_flowRecords) || []);
			tableKey.value++;
			break;
		default:
			break;
	}
};
//获取详情
function handleClickRow(row: any) {
	console.log(row);
	getTableDataInfoApi({
		id: row.rowID,
		isPopFlowRecords: 1,
	}).then((res) => {
		actveRowInfo.value = res[0];
		switch (activeName.value) {
			case 'first':
				tableColumnsCommon.value = tableColumns1;
				tableData2.value = res[0].extra_files || [];
				tableKey.value++;
				break;
			case 'second':
				tableColumnsCommon.value = tableColumns2;
				tableData2.value = markFirstTwenty(processArray(res[0].extra_flowRecords) || []);
				tableKey.value++;
				break;
			default:
				break;
		}
	});
}
//催办
function handleClickMoniter(row: any, index: number) {
	api_postApprovalRemind(actveRowInfo.value.rowID).then((res) => {
		handleClickRow(actveRowInfo.value);
	});
}
//撤回
function handleSubmitBack(reason: string) {
	api_postApprovalWithdraw(activeRow.value.rowID, reason).then((res) => {
		getList();
	});
}
</script>
<style lang="scss" scoped>
.left-type-wrap {
	width: 165px;
	height: 30px;
	background-color: rgba(229, 230, 235, 1);
	display: flex;
	border-radius: 2px 2px 2px 2px;
	align-items: center;

	.type-item {
		font-size: 14px;
		color: #1d2129;
		border-radius: 2px 2px 2px 2px;
		padding: 2px 0;
		margin: 0 2px;
		height: 26px;
		flex: 1;
		text-align: center;
		cursor: pointer;
	}

	.type-item.active {
		background-color: rgba(255, 255, 255, 1);
	}
}

.main-out-wrap {
	height: 100%;
	display: flex;
	flex-direction: column;

	.top-table-wrap {
		height: 50%;

		.state-card {
			border-radius: 2px 2px 2px 2px;
			border: 1px solid;
			text-align: center;
		}
	}

	.bottom-table-wrap {
		height: 50%;
		position: relative;
		display: flex;
		overflow: hidden;
		flex-direction: column;
		padding-top: 14px;

		.resizeBtn {
			.line {
				-ms-flex-negative: 0;
				flex-shrink: 0;
				width: 100%;
				height: 1px;
				position: absolute;
				top: 10px;
				background-color: #e5e6eb;
			}

			flex-shrink: 0;
			width: 100%;
			position: absolute;
			top: 0;
			height: 14px;
			cursor: ns-resize;
			text-align: center;

			.resize-span {
				color: #ccc;
				font-size: 14px;
				display: inline-block;
				width: 60px;
				height: 14px;
				margin-top: -17px;
				line-height: 14px;
				border-radius: 4px;
				background-color: #ebeef5;
			}

			&:hover {
				.resize-span,
				.line {
					background-color: #0052d9;
					color: #fff;
				}

				.line {
					height: 3px;
				}
			}
		}

		.demo-tabs {
			:deep(.el-tabs__nav-wrap) {
				height: 48px;
				padding-left: 20px;
			}

			:deep(.el-tabs__nav) {
				height: 48px;
			}

			:deep(.el-tabs__active-bar) {
				height: 3px;
			}
		}

		.approval-name {
			display: flex;

			.left-name {
				font-size: 14px;
				color: #000000;
				font-weight: 400;
				margin-right: 12px;
			}

			.right-moniter {
				border-left: 2px solid rgba(229, 230, 235, 1);
				padding-left: 12px;
				display: flex;
				align-items: center;

				.image7 {
					width: 13.33px;
					height: 15px;
					background-image: url('@/assets/images/my-apply/image7.png');
					background-size: 100% 100%;
					background-position: center;
					background-repeat: no-repeat;
				}

				.text {
					color: rgba(47, 60, 244, 1);
					margin-left: 6px;
				}
			}

			.moniter-text {
				border-left: 2px solid rgba(229, 230, 235, 1);
				padding-left: 12px;
				display: flex;
				align-items: center;

				.image6 {
					width: 13.33px;
					height: 15px;
					background-image: url('@/assets/images/my-apply/image6.png');
					background-size: 100% 100%;
					background-position: center;
					background-repeat: no-repeat;
				}

				.text {
					margin-left: 6px;
					color: rgba(134, 144, 156, 1);
				}
			}
		}

		.approval-status {
			display: flex;
			align-items: center;

			.icon {
				width: 16.2px;
				height: 16px;
				background-size: 100% 100%;
				background-position: center;
				background-repeat: no-repeat;
			}

			.image1 {
				background-image: url('@/assets/images/my-apply/image1.png');
			}

			.image2 {
				background-image: url('@/assets/images/my-apply/image2.png');
			}

			.image3 {
				background-image: url('@/assets/images/my-apply/image3.png');
			}

			.image4 {
				background-image: url('@/assets/images/my-apply/image4.png');
			}

			.image5 {
				background-image: url('@/assets/images/my-apply/image5.png');
			}

			.color1 {
				color: #0060c1;
			}

			.color2 {
				color: rgba(25, 190, 107, 1);
			}

			.color3 {
				color: rgba(255, 153, 0, 1);
			}

			.color4 {
				color: rgba(45, 183, 245, 1);
			}

			.color5 {
				color: rgba(237, 64, 20, 1);
			}

			.type-name {
				margin-left: 4px;
				font-size: 14px;
				font-weight: 400;
			}
		}
	}
}
</style>
