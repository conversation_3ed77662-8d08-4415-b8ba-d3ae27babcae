<template>
	<el-select v-model="value_" :placeholder="placeholder" @change="onChange">
		<component :is="slots.default"></component>
	</el-select>
</template>

<script lang="ts" setup>
import { isArray } from 'lodash-es';
import { computed, ref, useSlots, watch } from 'vue';

interface Props {
	modelValue: number | string;
	label?: string;
	placeholder?: string;
}
const props = withDefaults(defineProps<Props>(), {
	placeholder: '请选择',
});

const emit = defineEmits<{
	(ev: 'update:modelValue', value: number | string): void;
	(ev: 'update:label', value?: string): void;
}>();

const slots = useSlots();

function has_<T = any>(val: unknown): val is T {
	if (val === null || val === undefined || val === '') {
		return false;
	}
	return true;
}

const optionsVal = computed(() => {
	console.log('1111111111111111   optionsVal');
	if (!slots.default) return [];
	let list_: OptionItem[] = [];
	const list1 = slots.default() || [];
	// 经过测试 如果外部使用for循环时，会合成为一个数据 type 为 Symbol(v-fgt) 需要判断 children
	list1.forEach((item: any) => {
		if ((item as any).type?.name === 'ElOption' && item.props) {
			// 说明是 ElOption 取 props
			list_.push(item.props);
		}
		if (isArray(item.children)) {
			item.children.forEach((item: any) => {
				if ((item as any).type?.name === 'ElOption' && item.props) {
					list_.push(item.props);
				}
			});
		}
	});
	console.log('1111111111111111   optionsVal', list_);
	return list_;
});
const value_ = computed({
	get() {
		console.log('1111111111111111   computed  get  props.modelValue', props.modelValue);
		// 如果 不存在 value ,但是存在label 的情况下，显示label
		// 如果不存在 label 直接返回 value  无论value 是否有值
		if (!has_(props.label)) return props.modelValue;
		// 存在 label 时 ，判断 modelValue 是否在 optionsVal 中
		// 如果 optionsVal 没有数据，返回 label
		// if (!optionsVal.value.length) return props.label as string;
		// 如果 optionsVal 没有数据，可能是数据还没加载回来，暂时先返回原来的值，
		if (!optionsVal.value.length) return props.modelValue;
		const obj = optionsVal.value.find((item) => item.value === props.modelValue);
		// 如果不在 optionsVal 中，也返回 label
		if (!obj) return props.label as string;
		// 其它情况 返回原来的值
		return props.modelValue;
	},
	set(value) {
		emit('update:modelValue', value);
	},
});
const label_ = computed({
	get() {
		return props.label || '';
	},
	set(value) {
		emit('update:label', value);
	},
});
function onChange(val) {
	if (props.label === undefined || props.label === null) return;
	console.log('1111111111111111', val, optionsVal.value);
	if (val === undefined) {
		label_.value = '';
		return;
	}
	const obj = optionsVal.value.find((item) => item.value === val);
	if (obj) {
		label_.value = obj.label;
	}
}
</script>
<style lang="scss" scoped></style>
