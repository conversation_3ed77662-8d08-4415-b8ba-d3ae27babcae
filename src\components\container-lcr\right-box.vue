<template>
	<div class="t-card-header" v-if="title || $slots.header">
		<slot name="header">
			<div class="t-title" :title="title">{{ title }}</div>
		</slot>
	</div>
	<div class="t-card-content" v-if="$slots.default">
		<slot></slot>
	</div>
	<div class="t-card-foot" v-if="$slots.foot">
		<slot name="foot"></slot>
	</div>
</template>

<script lang="ts" setup>
// 本组件需要在 container-lcr 组件中使用
defineOptions({ inheritAttrs: false });
interface Props {
	title?: string; // 标题
}
const props = withDefaults(defineProps<Props>(), {});
</script>

<style lang="scss" scoped>
.t-card-header {
	flex-shrink: 0;
	line-height: 46px;
	border-bottom: 1px solid #ebeef5;
	padding: 0 16px;
	.t-title {
		line-height: 46px;
		font-size: 14px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
}
.t-card-content {
	flex-grow: 1;
	overflow: auto;
	padding: 16px;
}
.t-card-foot {
	flex-shrink: 0;
	height: 46px;
	border-top: 1px solid #ebeef5;
	padding: 0 16px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
}
</style>
