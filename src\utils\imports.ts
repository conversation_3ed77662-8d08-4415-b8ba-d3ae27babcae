// 引入全局组件，因为 mac 系统自动引入组件时有问题，这里手动引入一下

import { App } from 'vue';

import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';

import CommonTable from '@/components/common-table/index.vue';
import ContainerLcr from '@/components/container-lcr/index.vue';
import RightBox from '@/components/container-lcr/right-box.vue';
import IBtns from '@/components/i-btns/index.vue';
import IDescriptions from '@/components/i-descriptions/index.vue';
import IDescriptionsItem from '@/components/i-descriptions/i-descriptions-item.vue';
import ILoading from '@/components/i-loading/index.vue';
import IScroll from '@/components/i-scroll/index.vue';
import IconFont from '@/components/icon-font/index.vue';
import MenuIconFont from '@/components/icon-font/index.vue';
// import VueQrcode from '@/components/vue-qrcode/index.vue';
// import DialogQrcode from '@/components/vue-qrcode/dialog-qrcode.vue';
// import SearchInput from '@/components/search/search-input.vue';
// import SearchSelect from '@/components/search/search-select.vue';
// import SearchSort from '@/components/search/search-sort.vue';
// import FormUpload from '@/components/form-item/form-upload.vue';
// import FormNumber from '@/components/form-item/form-number.vue';
// import FormImage from '@/components/form-item/form-image.vue';
// import WorkInfo from '@/components/work-info/index.vue';
// import ISelect from '@/components/select/i-select.vue';
import AsideMain from '@/components/container/aside-main.vue';
import HeaderMain from '@/components/container/header-main-back.vue'; 
import CustomIconButton from '@/components/button/custom-icon-button.vue';
import FormUpload from '@/components/upload/file-upload.vue';
export function imports(app: App) {
	app.use(ElementPlus);
	app.component('CommonTable', CommonTable);
	app.component('ContainerLcr', ContainerLcr);
	app.component('RightBox', RightBox);
	app.component('IBtns', IBtns);
	app.component('IDescriptions', IDescriptions);
	app.component('IDescriptionsItem', IDescriptionsItem);
	app.component('ILoading', ILoading);
	app.component('IScroll', IScroll);
	// app.component('IUpload', IUpload);
	app.component('IconFont', IconFont);
	app.component('MenuIconFont', MenuIconFont);
	// app.component('SearchInput', SearchInput);
	// app.component('SearchSelect', SearchSelect);
	// app.component('SearchSort', SearchSort);
	app.component('FormUpload', FormUpload);
	// app.component('FormNumber', FormNumber);
	// app.component('FormImage', FormImage);
	// app.component('VueQrcode', VueQrcode);
	// app.component('DialogQrcode', DialogQrcode);
	// app.component('WorkInfo', WorkInfo);
	// app.component('ISelect', ISelect);
	app.component('aside-main', AsideMain);
	app.component('header-main', HeaderMain);
	app.component('custom-icon-button', CustomIconButton);
	
}
