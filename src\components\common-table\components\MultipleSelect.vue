<template>
	<el-dropdown @command="handleClick">
		<el-button
			class="operation-btn"
			:icon="Setting"
		></el-button>
		<template #dropdown>
			<el-dropdown-menu>
				<el-dropdown-item
					v-for="(item, index) in columnOptions"
					:key="index"
					:command="item.value"
					>{{ item.label }}</el-dropdown-item
				>
			</el-dropdown-menu>
		</template>
	</el-dropdown>
</template>

<script lang="tsx" setup>
import { ref, watch, computed } from 'vue';
import { Setting } from '@element-plus/icons-vue';
// #region 参数定义
interface Props {
	// 下拉列表项
	columnOptions?: CommonTableOptions[];
}

const props = withDefaults(defineProps<Props>(), {});

const emit = defineEmits<{
	(ev: 'multipleSelect', item: CommonTableOptions): void;
}>();
// #endregion

// 点击切换菜单
const handleClick = (key: string) => {
	// 根据key值在列表中找到点击的项
	let itemData: CommonTableOptions = {};
	props.columnOptions?.forEach((item) => {
		if (item.value == key) {
			itemData = item;
		}
	});
	emit('multipleSelect', itemData);
};
</script>
<style lang="scss" scoped>
.operation-btn {
	width: 32px;
}
</style>
