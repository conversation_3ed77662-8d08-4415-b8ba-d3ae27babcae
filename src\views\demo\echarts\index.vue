<template>
	<div>
		<h1>图表</h1>
		<h4>
			用到的图表需要先在 @/utils/i-charts.ts 中按需引入
			<a
				target="_blank"
				href="https://echarts.apache.org/handbook/zh/basics/import#%E5%9C%A8-typescript-%E4%B8%AD%E6%8C%89%E9%9C%80%E5%BC%95%E5%85%A5"
				>官方示例</a
			>
		</h4>
		<h3>通过ref创建</h3>
		<div class="t-charts-box" ref="refChartsDom"></div>
		<h3>通过id创建</h3>
		<div class="t-charts-box" :id="elId"></div>
	</div>
</template>
<script lang="ts" setup>
import createCharts, { ECharts, ECOption } from '@/utils/i-charts';
import { onMounted, ref } from 'vue';

const elId = `id_charts_${String(Math.random()).slice(2)}`;
let myCharts1: ECharts;
let myCharts2: ECharts;
const refChartsDom = ref<HTMLDivElement>();
const option: ECOption = {
	xAxis: {
		type: 'category',
		data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
	},
	yAxis: {
		type: 'value',
	},
	series: [
		{
			data: [150, 230, 224, 218, 135, 147, 260],
			type: 'line',
		},
	],
};
onMounted(() => {
	// 因为类型提示问题，使用id时 要断言为any，否则报错
	myCharts1 = createCharts(elId as any);
	myCharts1.setOption(option);

	myCharts2 = createCharts(refChartsDom.value);
	myCharts2.setOption(option);
});
</script>

<style lang="scss" scoped>
.t-charts-box {
	width: 500px;
	height: 360px;
	border: 1px rgb(223, 223, 223) solid;
}
</style>
