<template>
	<div>
		<el-button v-if="!showQueryInput" @click="handleClick" class="query-btn" :icon="Search" />
		<div v-if="showQueryInput" class="query-box">
			<el-select class="custom-query-select" v-model="select" placeholder="Select">
				<el-option label="创建日期" value="1" />
				<el-option label="创建人" value="2" />
				<el-option label="驾驶员" value="3" />
			</el-select>
			<el-input @blur="handleBlurInput" clearable :prefix-icon="Search" autofocus v-model="input1" placeholder="请搜索" class="custom-query-input"> </el-input>
		</div>
	</div>
</template>

<script lang="tsx" setup>
import { Search } from '@element-plus/icons-vue';
import { ref } from 'vue';
const showQueryInput = ref(false);

const input1 = ref('');
const select = ref('1');

function handleClick() {
	if (!showQueryInput.value) {
		showQueryInput.value = true;
	}
}

function handleBlurInput() {
  console.log('11111');
  if(!input1.value) {
    showQueryInput.value = false;
  }
}
</script>
<style lang="scss" scoped>
.query-btn {
	width: 32px;
	height: 32px;
}

.query-box {
	display: flex;
	min-width: 300px;

	.custom-query-input {
		width: 200px;
	}
}
</style>
<style lang="scss">
.custom-query-select {
	flex: 1;
	.el-select__wrapper {
		box-shadow: 0 0 0 0;
		border-left: 1px solid #dcdfe6;
		border-top: 1px solid #dcdfe6;
		border-bottom: 1px solid #dcdfe6;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
	}

	.el-select__placeholder {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		display: inline-block;
	}
}

.custom-query-input {
	.el-input__wrapper {
		border-top-left-radius: 0;
		border-bottom-left-radius: 0;
	}
}
</style>
