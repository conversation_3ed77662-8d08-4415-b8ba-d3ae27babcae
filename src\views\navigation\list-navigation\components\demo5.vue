<template>
	<list-navigation
		v-model="navigationKey"
		:isShowBadge="isShowBadge"
		:listData="data"
		:isShowOperation="isShowOperation"
		:showTrigger="showTrigger"
		:activeTrigger="activeTigger"
		:operationList="operationList"
		@action="actionHandler"
	></list-navigation>
</template>

<script setup lang="tsx">
import { ref, reactive, watchEffect } from 'vue';
/** 左侧导航栏相关 */
import { ListItem, OperationItem } from '@/components/navigation/list-navigation.vue';
// 配置
const showAllBadge = false; // 为true时，即使num = 0也会显示
const data: ListItem[] = [
	{
		icon: 'icon-settings-4-line',
		title: '示例种类1',
		num: 12,
		key: 'key1',
	},
	{
		icon: 'icon-calendar-line',
		title: '示例种类2-超长示例超长示例超长示例超长示例超长示例',
		num: 102,
		key: 'key2',
	},
	{
		icon: 'icon-time-line',
		title: '示例种类3-函数禁用',
		num: 0,
		key: 'key3',
	},
	{
		icon: 'icon-time-line',
		title: '示例种类4-自定义操作',
		num: 0,
		key: 'key4',
		operationList: [
			// 可以为每个选项单独定义操作
			{
				actionCode: 'cus-action1',
				actionLabel: '自定义操作1',
			},
			{
				actionCode: 'cus-action2',
				actionLabel: '自定义操作2',
			},
		],
	},
];
const navigationKey = ref(data[0].key);
watchEffect(() => {
	console.log('左侧导航Key值变动:', navigationKey.value);
});

function handleBadgeClick(t) {
	console.log('badge 被点击：', t);
}

const isShowBadge = true;
const isShowOperation = true;
const showTrigger = 'hover'; // 可以为 normal， 即固定出现
const activeTigger = 'hover'; // 可以为 click

const operationList: OperationItem[] = [
	// 可以为每个选项单独定义处理器
	{
		actionCode: 'action1',
		actionLabel: '操作1',
		func: action1Handler,
	},
	// 可以使用顶层监听统一进行处理， 即不设置func参数
	{
		actionCode: 'action2',
		actionLabel: '操作2',
	},
	// 可以设置是否禁用
	{
		actionCode: 'action3',
		actionLabel: '操作3',
		isDisabled: true,
	},
	// isDisabled 可以接收一个 () => boolean 函数或者 boolean 值
	{
		actionCode: 'action4',
		actionLabel: '操作4',
		isDisabled: judgeIsDisabled,
	},

	// 可以设置是否有分割线
	{
		actionCode: 'action5',
		actionLabel: '操作5',
		isDivided: true,
	},
];

function action1Handler(item: ListItem) {
	console.log('action1 触发, 触发者:', item);
}

function actionHandler(data: object) {
	console.log('顶层事件监听：', data);
}

function judgeIsDisabled(item: ListItem) {
	if (item.key === 'key3') {
		return true;
	} else {
		return false;
	}
}
</script>
<style lang="scss" scoped></style>
