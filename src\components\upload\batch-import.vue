<!--
  功能：批量导入功能
  作者：ligw
  时间：2024年08月16日 15:29:24
  版本：v1.0
  修改记录：
  修改内容：
  修改人员：
  修改时间：
-->
<template>
  <div class="batch-import-container">
    <el-alert  type="info" :closable="false">
      <div class="alert-content">
        <div class="alert-title">请先下载示例文字模板，并完善表格内容</div>
        <FileExport :exportFun="exportFun" >
          <ElButton type="primary" text >
            <ElIcon><Download/></ElIcon>
            下载空的表格模板
          </ElButton>
        </FileExport>
      </div>
    </el-alert>
    <div class="btn-import-area">
      <BaseUpload 
        ref="btnUploadRef"
        :fileList="fileList"
        :tip="tip" 
        :accept="accept"
        :show-file-list="false" 
        :auto-upload="false"
        :on-success="handleSuccess"
        :on-change="handleFileChange"
        :on-error="handleError"
        :before-upload="handleBeforeUpload"
        :http-request="doUpload"
      >
      <ElButton
        plain
      >
        <span>
          <el-icon><Upload /></el-icon>&nbsp;选择文件
        </span>
      </ElButton>
        <!-- <template #default><el-icon><Upload /></el-icon>&nbsp;选择文件</template> -->
      </BaseUpload>
    </div>
    <div v-show="!props.multiple && (!fileList || fileList.length === 0)" class="drag-import-area">
      <FileUpload 
        ref="dragUploadRef"
        drag 
        :multiple="multiple" 
        :accept="accept"
        :show-file-list="false" 
        :auto-upload="false"
        :on-change="handleFileChange"
        >
        <el-icon  style="font-size: 22px;"><upload-filled /></el-icon>
        <div class="tip-text">
          上传文件
        </div>
        <div class="tip-text desc">
          可将文件拖拽到此处上传
        </div>
      </FileUpload>
    </div>
    <div class="file-list-area">
      <div v-if="!props.multiple && fileList && fileList?.length>0" class="sindle-file-area">
        <div  class="file-item">
          <div class="icon">X</div>
          <div class="name">
           <span :title="fileList?.[0]?.name"> {{ fileList?.[0]?.name }}</span>
            <span class="size">
              （{{ fileSize(fileList?.[0]?.size || 0) }}）
            </span>
          </div>
          <ElButton type="primary" text @click="reUpload" >
            重新上传
          </ElButton>
        </div>
        <div  class="error-tip">
  
          {{ errorTip }}
        </div>
      </div>
      <el-table v-if="props.multiple" :data="fileList" style="width: 100%">
          <el-table-column prop="name" label="文件" >
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80px">
            <template #default="scope">
              <el-tag 
                v-if="scope.row.status === 'ready'" 
                type="primary" effect="light">
                <el-icon><Refresh /></el-icon>等待
              </el-tag>
              <el-tag 
                v-if="scope.row.status === 'success'" 
                type="success" effect="light">
                <el-icon><SuccessFilled /></el-icon>完成
              </el-tag>
              <el-tag 
                v-if="scope.row.status === 'fail'" 
                type="danger" effect="light">
                <el-icon><CircleCloseFilled /></el-icon>错误
              </el-tag>
          </template>
          </el-table-column>
          <el-table-column  label="操作" width="100px">
            <template #default="scope">
              <el-button  link type="primary" size="small" @click="handleDelete(scope.row)">删除</el-button>
              <el-button v-if="preview" link type="primary" size="small" @click="handlePreview">
                预览
              </el-button>
            </template>
        
          </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <ElButton @click="handleCancel">取消</ElButton>
      <ElButton 
        :disabled="!!errorTip || !fileList || fileList?.length===0" type="primary"
        @click="handleSubmit"
      >导入</ElButton>
    </div>
  </div>
  <ImportResult ref="resultRef" :data="result" />
</template>
<script lang='ts' setup>
import { ref, computed } from 'vue'

import type {
  UploadFile, UploadInstance, UploadProps, UploadFiles,
  UploadRequestOptions
} from 'element-plus'

import { parseFileSize, judgeFileType } from '@/utils/file'


interface Props{
  preview?: boolean; //是否预览文件
	multiple?: boolean; // 是否多文件上传
  limit?: number; 
  size?: number; //单文件大小限制单位MB
  accept?: string; //文件类型限制
  // 导入方法-
  uploadFun: (files: FormData) => XMLHttpRequest | Promise<ImportResultResponse>;
  // 模板导出方法
  exportFun: (prams: Function) => void;
}
const props = withDefaults(defineProps<Props>(), {
  preview: true,
  multiple: true,
  limit: 1,
  accept: '.xls,.xlsx,.docx',
  size: 5
})

const resultRef = ref<ImportResult>()
const tip = computed(() => {
  const txt = props.accept?.split(',').join('、')
  return `支持不超过${props.size}MB的文件`
})
// 单文件上传错误信息提示
const errorTip = computed(() => {
  if (props.multiple) {
    return ''
  } else {
    const file = fileList.value?.[0]
    if (file) {
      const limitSize = (file?.size || 0) / 1024 / 1024
      if (limitSize > props.size) { 
        return `*文件过大，支持不超过${props.size}MB的文件`
      }
      const otherType = file.name.slice(file.name.lastIndexOf('.') + 1)
      if (!props.accept.toLocaleLowerCase().includes(otherType.toLocaleLowerCase())) {
        return `*表格格式错误，请使用正确模板上传！`
      }
    }
  }
})

const result = ref<ImportResultResponse>({
  code: 0,
  data: [{
    failedResultList: [],
    totalCount: 0,
    failedCount: 0
  }],
  message:''
})
const fileList = ref<UploadFile[]>()
const btnUploadRef = ref<UploadInstance>()
const dragUploadRef = ref<UploadInstance>()
const fileSize = (size:number) => {
  return parseFileSize(size || 0)
}
const handleFileChange = (file: UploadFile) => {
  const { status } = file
  if (status === 'ready') {
    if (props.multiple) {
      fileList.value ? fileList.value.push(file) : fileList.value = [file]
    } else {
      fileList.value = [file]
    }
    checkLimit(file)
  }
}

const checkLimit = (file: UploadFile) => {
  const boo = file.raw && handleBeforeUpload(file.raw)
  fileList.value = fileList.value?.map(item => {
    return {
      ...item,
      status: boo ? item.status : 'fail'
    }
  })
}

// 重新上传
const reUpload = () => {
  btnUploadRef?.value?.$el.querySelector('input').click()
}
const count = ref<number>(0)
/**
 * 上传成功
 * @param response 
 * @param uploadFile 
 * @param uploadFiles 
 */
const handleSuccess: UploadProps['onSuccess'] = (response: ImportResultResponse,
  uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  if (response?.code === 0) {
    if (!props.multiple) {
      result.value = response 
      resultRef.value?.open()
      emit('complete',response)
    } else {
      count.value++
      fileList.value = fileList.value?.map(item => {
        return {
          ...item,
          response,
          status: item.uid === uploadFile.uid ? uploadFile.status : item.status

        }
      })
      handleComplete()
    }
  }
}

const handleComplete = () => {
  if (count.value === fileList.value?.length) {
    emit('complete',fileList.value)
  }
}
/**
 * 上传前校验文件类型、大小
 * @param file 
 */
 const handleBeforeUpload: UploadProps['beforeUpload'] = (file) => {
  const fileType = judgeFileType(file.type)
  if (fileType === "image" || fileType === 'video') {
    const imgType = file.type.split('/')[1]
    if (!props.accept.toLocaleLowerCase().includes(imgType.toLocaleLowerCase())) {
      ElMessage.warning(`请上传后缀为${props.accept}类型文件！`)
      return false
    }
  } else {
    const otherType = file.name.slice(file.name.lastIndexOf('.') + 1)
    if (!props.accept.toLocaleLowerCase().includes(otherType.toLocaleLowerCase())) {
      ElMessage.warning(`请上传后缀为${props.accept}类型文件！`)
      return false
    }
  }
  if (props.size) {
    const limitSize = file.size / 1024 / 1024
    if (limitSize>props.size) {
      ElMessage.warning(`上传文件不能超过${props.size}MB!`)
      return false
    }
  }
  return true
}

// 触发上传业务 
const handleSubmit = () => {
  btnUploadRef.value?.uploadRef?.submit()
}
 
// 异常状态处理
const handleError = (error: Error, uploadFile: UploadFile) => {
  if (props.multiple) {
    count.value++
    fileList.value = fileList.value?.map(item => {
      return {
        ...item,
        status: item.uid === uploadFile.uid ? uploadFile.status : item.status
      }
    })
    handleComplete()
  }
}
// 文件导入操作
const doUpload = (options: UploadRequestOptions) => {
  const formData = new FormData()
  formData.append('file', options.file)
  return props.uploadFun(formData)
  
}
// 删除已上传文件
const handleDelete = (row: UploadFile) => {
  const {uid} = row
  fileList.value = fileList.value?.filter(item=>item.uid !== uid)
}

// 预览操作，由外部实现
const emit = defineEmits<{
  (ev: 'preview', value: UploadFile[] | undefined): void;
  (ev: 'complete', response: any): void;
  (ev: 'cancel'): void;
}>();

const handlePreview = () => {
  emit('preview', fileList.value);
}
const handleCancel = () => {
  emit('cancel')
}
</script>
<style scoped lang='scss'>
.batch-import-container{
  width: 520px;
  max-height: 655px;
  :deep(.el-alert__content){
      width: 100%;
    }
  .alert-content{
    display: flex;
    justify-content: space-between;
    
    :deep(.el-button){
      padding: 0;
      height: 24px;
    }
  }
  .btn-import-area{
    margin: 12px 0 14px 0;
    div:nth-child(1){
      display: flex;
      align-items: center;
      :deep(.el-upload__tip){
        margin-top: 0;
        padding-left: 8px;
      }
    }
  }
  .drag-import-area{
    :deep(.el-upload-dragger){
      padding: 20px 0;
      .el-icon{
        margin-bottom: 6px;
      }
      .tip-text{
        height: 22px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1D2129;
        line-height: 22px;
        font-style: normal;
        text-align: center;
      }
      .desc{
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        color: #86909C;
      }
    }
  }
  .file-list-area{
    margin-bottom: 20px;
    .sindle-file-area{
      .file-item{
        width: 100%;
        height: 72px;
        background: #F7F8FA;
        border-radius: 2px;
        border: 1px solid #E5E6EB;
        display: grid;
        padding: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        
        text-align: left;
        font-style: normal;
        grid-template-columns: 32px calc(100% - 90px)  56px;
        .icon{
          width: 32px;
          height: 32px;
          line-height: 32px;
          text-align: center;
          background: #00AE83;
          color: #fff;
        }
        .name{
          color: #1D2129;
          padding: 0 8px;
          display: flex;
          >span:nth-child(1){
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            white-space:nowrap;
            display: block;
            width: calc(100% - 90px);
          }
          .size{
            color: #86909C;
            min-width: 90px;
          }
        }
      }
      .error-tip{
        height: 22px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #E64264;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        margin-top: 8px;
      }
    }
  }
  .footer{
    width: 100%;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>
