<template>
	<div
		class="t-bar-box"
		ref="refDomBox"
	>
		<div
			class="t-scroll"
			ref="refDomScroll"
		>
			<div
				v-for="item in storeActiveRoutes.getRoutes"
				class="t-item"
				:class="{
					't-active': item.name === currentRoute.name,
				}"
				@click="onClick(item, $event)"
				@contextmenu.prevent.native="openMenu(item, $event)"
			>
				<span class="t-item-text">{{ item.meta!.title }}</span>
				<el-icon
					title="关闭"
					v-show="storeActiveRoutes.getRoutes.length > 1"
					class="t-close"
					@click.stop="onClose(item)"
				>
					<Close />
				</el-icon>
			</div>
			<div
				class="full-screen"
				v-if="!isFullScreen"
				@click="resizeScreen"
			>
				<IconFont type="icon-fullscreen" />
			</div>
			<div
				class="full-screen"
				v-else
				@click="resizeScreen"
			>
				<IconFont type="icon-notfullscreen" />
			</div>
		</div>
		<div
			class="t-menu-ul"
			ref="refDomMenu"
			v-show="menuShow"
		>
			<div
				class="t-li padding-4-8 g-ellipsis"
				@click="refreshSelf"
			>
				刷新
			</div>
			<div
				class="t-li padding-4-8 g-ellipsis"
				@click="closeSelf"
			>
				关闭
			</div>
			<div
				v-if="showCloseLeft"
				class="t-li padding-4-8 g-ellipsis"
				@click="closeLeft"
			>
				关闭左侧
			</div>
			<div
				v-if="showCloseRight"
				class="t-li padding-4-8 g-ellipsis"
				@click="closeRight"
			>
				关闭右侧
			</div>
			<div
				v-if="storeActiveRoutes.getRoutes.length > 1"
				class="t-li padding-4-8 g-ellipsis"
				@click="closeOther"
			>
				关闭其它
			</div>
			<div
				class="t-li padding-4-8 g-ellipsis"
				@click="closeAll"
			>
				关闭所有
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { onMounted, watch, ref, nextTick, computed } from 'vue';
import { RouteRecordRaw, useRoute } from 'vue-router';
import { useStoreActiveRoutes } from '@/store/modules/active-routes';
import { setStoreCommon, storeCommon, storeMenus } from '@/store';
import { routerInstance } from '@/router';
import { Close } from '@element-plus/icons-vue';
import { sendResizeScreenMsg } from '@/utils/message';

const storeActiveRoutes = useStoreActiveRoutes();

const refDomBox = ref<HTMLDivElement>();
const refDomScroll = ref<HTMLDivElement>();
const refDomMenu = ref<HTMLDivElement>();
const isFullScreen = ref<boolean>(false);

const menuShow = ref(false);
let nowClickItem: RouteRecordRaw; // 当前右键点击的item
const currentRoute = useRoute(); // 当前路由

onMounted(() => {
	watch(
		() => currentRoute.name,
		(val) => {
			const routers = routerInstance.getRoutes();
			const obj = routers.find((item) => item.name === val);
			!obj?.meta?.hideTag && storeActiveRoutes.setRoute(obj!);
			const { fullPath, name, params, path, query } = currentRoute;
			const routeParams = { fullPath, name, params, path, query };
			storeActiveRoutes.setRouteParams(routeParams);
		},
		{
			immediate: true,
		},
	);
	document.body.appendChild(refDomMenu.value!);
	document.addEventListener('click', hideMenuList);
});
watch(
	() => storeCommon.isLogined,
	(val) => {
		if (!val) {
			storeActiveRoutes.clear();
			storeActiveRoutes.clearCacheRoutes();
		}
	},
);
const getCurrentRouteIndex = computed(() => {
	let routeIndex: number = -1;
	if (storeActiveRoutes.getRoutes.length > 0) {
		routeIndex = storeActiveRoutes.getRoutes.findIndex((item) => item.name === currentRoute.name);
	}
	return routeIndex;
});
const getSelectTagIndex = computed(() => {
	let tagIndex: number = -1;
	if (menuShow.value) {
		tagIndex = storeActiveRoutes.getRoutes.findIndex((item) => item.name === nowClickItem!.name);
	}
	return tagIndex;
});
const showCloseLeft = computed(() => {
	let show: boolean = false;
	if (menuShow.value) {
		show = getSelectTagIndex.value >= 1;
	}
	return show;
});
const showCloseRight = computed(() => {
	let show: boolean = false;
	if (menuShow.value) {
		show = getSelectTagIndex.value < storeActiveRoutes.getRoutes.length - 1;
	}
	return show;
});
function hideMenuList() {
	menuShow.value = false;
}
function resizeScreen() {
	sendResizeScreenMsg(!isFullScreen.value);
	isFullScreen.value = !isFullScreen.value;
}
function onClick(item: RouteRecordRaw, ev) {
	if (currentRoute.name === item.name) {
		// 路由重复，调用刷新路由方法
		setStoreCommon({
			refresh: storeCommon.refresh + 1,
		});
		return;
	}
	const routeParams = storeActiveRoutes.getRouteParams;
	const queryObj = routeParams.find((item_) => item_.name === item.name);
	routerInstance.push({ ...item, ...queryObj });
}
function openMenu(item: RouteRecordRaw, ev: MouseEvent) {
	menuShow.value = false;
	nextTick(() => {
		nowClickItem = item;
		menuShow.value = true;
		refDomMenu.value!.style.top = ev.clientY + 'px';
		refDomMenu.value!.style.left = ev.clientX + 15 + 'px';
	});
}
function onClose(item: RouteRecordRaw) {
	menuShow.value = false;
	storeActiveRoutes.removeRoute(item);
	if (currentRoute.name === item.name) {
		routerInstance.push(storeActiveRoutes.getRoutes[storeActiveRoutes.getRoutes.length - 1]);
	}
}
function refreshSelf() {
	menuShow.value = false;
	storeActiveRoutes.clearCacheRoutesItem(nowClickItem!.name as string);
	const queryObj = storeActiveRoutes.getRouteParams.find((item_) => item_.name === nowClickItem!.name);
	routerInstance.replace({
		path: '/redirect',
		query: { fullPath: queryObj?.fullPath, ...queryObj?.query },
	});
}
function closeSelf() {
	menuShow.value = false;
	if (storeActiveRoutes.getRoutes.length <= 1) {
		const closeNameChildren = storeMenus.value[0].children!;
		if (closeNameChildren[0].name === nowClickItem?.name) {
			return;
		}
	}
	storeActiveRoutes.removeRoute(nowClickItem!.name as string);
	if (storeActiveRoutes.getRoutes.length === 0) {
		if (storeMenus.value.length > 0) {
			routerInstance.push(storeMenus.value[0]);
		}
	} else {
		if (currentRoute.name === nowClickItem?.name) {
			routerInstance.push(storeActiveRoutes.getRoutes[storeActiveRoutes.getRoutes.length - 1]);
		}
	}
}
function closeLeft() {
	if (getCurrentRouteIndex.value < getSelectTagIndex.value) {
		// 当前路由在操作项的左侧
		routerInstance.push(nowClickItem);
	}
	storeActiveRoutes.removeLeftRoute(nowClickItem!.name as string);
	menuShow.value = false;
}
function closeRight() {
	if (getCurrentRouteIndex.value > getSelectTagIndex.value) {
		// 当前路由在操作项的右侧
		routerInstance.push(nowClickItem);
	}
	storeActiveRoutes.removeRightRoute(nowClickItem!.name as string);
	menuShow.value = false;
}
function closeOther() {
	menuShow.value = false;
	storeActiveRoutes.removeOther(nowClickItem!.name as string);
	if (currentRoute.name !== nowClickItem?.name) {
		// 如果点击的不是当前路由，跳转到那个路由
		routerInstance.push(nowClickItem);
	}
}
function closeAll() {
	menuShow.value = false;
	if (storeActiveRoutes.getRoutes.length <= 1) {
		const closeNameChildren = storeMenus.value[0].children!;
		if (closeNameChildren[0].name === nowClickItem?.name) {
			return;
		}
	}
	storeActiveRoutes.clear();
	if (storeMenus.value.length > 0) {
		routerInstance.push(storeMenus.value[0]);
	}
}
</script>
<style lang="scss" scoped>
.t-bar-box {
	color: #4e5969;
	flex-shrink: 0;
	overflow: hidden;
	padding-right: 80px;
	position: relative;
	padding-top: 8px;
	box-sizing: border-box;
	// background-color: #fff;
	.t-icon-more {
		position: absolute;
		top: 11px;
		right: 4px;
		transform: rotate(90deg);
	}
	.full-screen {
		position: absolute;
		right: -60px;
		display: flex;
		align-items: center;
		height: 28px;
		cursor: pointer;
		font-size: 20px;
	}

	.t-scroll {
		display: flex;
		flex-wrap: nowrap;
		position: relative;
		align-items: end;
	}
	.t-item {
		margin-right: 4px;
		height: 28px;
		padding: 6px 16px;
		background: #f7f8fa;
		border-radius: 2px 2px 0px 0px;
		border: 1px solid #e5e6eb;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #1d2129;
		line-height: 14px;

		box-sizing: border-box;
		position: relative;
		cursor: pointer;
		white-space: nowrap;
		display: flex;
		align-items: center;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		flex-shrink: 1;
		.t-item-text {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		&.t-active {
			// background-color: #f2f3f5;
			// color: #1d2129;
			// border-color: #f2f3f5;
			background: var(--el-color-primary);
			color: #ffffff;
		}
		&:first-of-type {
			margin-left: 8px;
		}
	}
}
.t-close {
	margin-left: 4px;
	// 	position: absolute;
	// 	right: 4px;
	// 	top: 0;
	// 	bottom: 0;
	// 	margin: auto;
	// 	z-index: 2;
	// 	&:hover {
	// 		color: var(--el-color-error);
	// 	}
}

.t-menu-ul {
	position: absolute;
	background-color: #fff;
	box-shadow: var(--el-box-shadow-light);
	padding: 4px;
	z-index: 3;
	font-size: 14px;
}
.t-menu-ul,
.t-ul {
	.t-li {
		padding: 4px 24px 4px 8px;
		text-align: center;
		cursor: pointer;
		position: relative;
		border-bottom: 1px solid #f2f3f5;
		&:last-child {
			border-bottom: none;
		}
	}
	.padding-4-8 {
		padding: 4px 8px;
	}
}
</style>
