// 单位列表——返回对象
export interface ResponseUnitInfo {
	uuid: string; // 唯一标识
	name: string; // 名称
}

// 部门列表——返回对象
export interface ResponseDepartInfo {
	uuid: string; // 唯一标识
	name: string; // 名称
}

// 审批详情——返回对象
export interface ResponseApprovalDetailInfo {
	WorkflowID: string;
	bDescription: string;
	bFormFlag: number;
	cName: string;
	cNumber: string;
	companyName: string;
	companyNumber: string;
	createDate: string;
	creatorName: string;
	creatorNumber: string;
	deptName: string;
	deptNumber: string;
	extendField1: string;
	extendField2: string;
	extra_billDetails: any[];
	extra_files: any[];
	extra_flowRecords: any[];
	gState: number;
	number: string;
	phone: string;
	projectName: string;
	rowID: string; // 表单id
	sPRowID: string;
	signTableFlag: number;
	subProjectID: number;
	submitDate: string;
	title: string;
	validDate: string;
	workflowID: string;
	remark: string;
}

// 人员列表——返回对象
export interface ResponsePersonInfo {
	userNumber: string; // 唯一标识
	userName: string; // 名称
}

// 交通列表——返回对象
export interface ResponseTrafficInfo {
	costNumber: string; // 唯一标识
	costName: string; // 名称
}
