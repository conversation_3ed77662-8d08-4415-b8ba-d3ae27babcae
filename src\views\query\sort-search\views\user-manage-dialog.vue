<template>
	<el-dialog
		v-model="dialogVisible"
		:title="viewType == 'add' ? '创建用户' : viewType == 'edit' ? '修改用户' : '查看用户'"
		@closed="resetForm"
		width="30%"
	>
		<el-form
			ref="formRef"
			:model="formData"
			:rules="formRules"
			label-width="100px"
			label-position="left"
		>
			<el-form-item
				prop="username"
				label="用户名"
			>
				<el-input
					v-model="formData.username"
					placeholder="请输入用户名"
				/>
			</el-form-item>
			<el-form-item
				prop="password"
				label="密码"
				v-if="viewType == 'add'"
			>
				<el-input
					v-model="formData.password"
					placeholder="请输入密码"
				/>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="dialogVisible = false">取消</el-button>
			<el-button
				type="primary"
				@click="handleCreateOrUpdate"
				:loading="btnLoading"
				>确认</el-button
			>
		</template>
	</el-dialog>
</template>

<script lang="tsx" setup>
import { type FormInstance, type FormRules, ElMessage } from 'element-plus';
import { watch, ref, defineProps, defineEmits } from 'vue';
import { type CreateOrUpdateTableRequestData, type TableData } from '../utils/types';
import { cloneDeep } from 'lodash-es';
import { createTableDataApi, updateTableDataApi } from '../utils/api';
// #region 参数定义
interface Props {
	// 弹窗显隐
	visible: boolean;
	// 页面类型
	viewType: 'add' | 'edit' | 'view' | string;
	// 表单初始数据，编辑|预览场景下查看
	initFormData: TableData;
}
const props = withDefaults(defineProps<Props>(), {
	viewType: 'add',
	visible: false,
});

const emit = defineEmits<{
	(e: 'update:visible', value: boolean): void;
	(e: 'refreshList'): void;
}>();
// #endregion

watch(
	() => props.visible,
	(val) => {
		dialogVisible.value = val || false;
	},
);

watch(
	() => props.initFormData,
	(val) => {
		formData.value = val;
	},
);

// 默认表单数据
const DEFAULT_FORM_DATA: CreateOrUpdateTableRequestData = {
	id: undefined,
	username: '',
	password: '',
};

// 控制弹窗显隐
const dialogVisible = ref<boolean>(false);

// 提交按钮loading
const btnLoading = ref<boolean>(false);

// 表单元素
const formRef = ref<FormInstance | null>(null);

// 表单数据
const formData = ref<CreateOrUpdateTableRequestData>(cloneDeep(DEFAULT_FORM_DATA));

// 表单效验规则
const formRules: FormRules<CreateOrUpdateTableRequestData> = {
	username: [{ required: true, trigger: 'blur', message: '请输入用户名' }],
	password: [{ required: true, trigger: 'blur', message: '请输入密码' }],
};

// 提交表单
const handleCreateOrUpdate = () => {
	formRef.value?.validate((valid: boolean, fields) => {
		// 表单效验
		if (!valid) return console.error('表单校验不通过', fields);

		// 查看直接关闭弹窗
		if (props.viewType == 'add') {
			dialogVisible.value = false;
			return;
		}

		btnLoading.value = true;
		// 判断调用接口
		const api = props.viewType == 'add' ? createTableDataApi : updateTableDataApi;
		api(formData.value)
			.then(() => {
				ElMessage.success('操作成功');
				// 刷新列表
				emit('refreshList');
				dialogVisible.value = false;
			})
			.finally(() => {
				btnLoading.value = false;
			});
	});
};

// 重置表单
const resetForm = () => {
	formRef.value?.clearValidate();
	formData.value = cloneDeep(DEFAULT_FORM_DATA);
	emit('update:visible', false);
};
</script>
<style lang="scss" scoped></style>
