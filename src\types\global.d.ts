interface Loading {
	value: boolean;
}

declare type FormRules<T = unknown> = Partial<Record<keyof T, any>>;
declare const G_loading: Loading;
declare const T: any;
declare let G_myMap_jobPath: any;
declare const d3: any;
declare const TMAP_HYBRID_MAP: number;
declare interface Window {
	G_loading: Loading;
	T: any;
	G_myMap_jobPath: any;
	T: any;
	d3: any;
	TMAP_HYBRID_MAP: number;
}

declare interface EnvConfig {
	documentTitle: string; // 平台名称
	VITE_BASE_API: string; // 接口前缀 "http://************/jyjc-service",
	VITE_BASE_API2: string; // idaas接口前缀 "http://************/jyjc-service",
	VITE_APP_NAME: string; // "jyjc-pre-web",
	VITE_CAS_AUTH_URL: string; // "http://************/iip-tenant-cas-client",
	VITE_CAS_LOGOUT_URL: string; // "http://************/iip-tenant-sso/logout",
	VITE_BASE_FILE_API: string; // "http://************:8811/basic-service",
	VITE_BASE_FILE_SATIC_PATH: string; // "http://************:8811/wscp-file"
	VITE_SELECT_ORGANIZATION_MEMBER: string;//http://*************:8080
}
declare interface NODE_ENV {
	BASE_URL: '/' | './'; // "/"
	NODE_ENV: 'development' | 'production'; // "development"
}

declare type Nullable<T> = T | null;
