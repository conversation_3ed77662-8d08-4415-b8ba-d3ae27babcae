// import { isDev } from '@/config';
import http, { EnumAim, XMLHttpDownload } from '@/utils/axios';
import * as Types from './types';
import { storeUserInfo } from '@/store';

const aim = EnumAim.idaas;

// 用户id
export const USER_ID = storeUserInfo.number;
// 用户名
export const USER_NAME = storeUserInfo.name;

/**
 * 查询单个通用审批详情
 * @param id 通用审批id
 * @param 返回值
 */
export function api_getApprovalDetail(id: string): Promise<Types.ResponseApprovalDetailInfo[]> {
	const url = '/open-api/approval/v1/get';
	return http.get(
		url,
		{
			id,
		},
		{
			loading: true,
		},
	);
}

/**
 * 查询单位列表
 * @param 返回值
 */
export function api_getUnitsList(): Promise<Types.ResponseUnitInfo[]> {
	const url = '/open-api/idaas/v1/company/list';
	return http.get(url, null, {
		useIDAAS: true,
		aim,
		ignoreGlobalAim: true,
	});
}

/**
 * 查询部门列表
 * @param rootOrganizationId 单位id
 * @param 返回值
 */
export function api_getDepartsList(rootOrganizationId: string): Promise<Types.ResponseDepartInfo[]> {
	const url = '/open-api/idaas/v1/department/list';
	return http.get(
		url,
		{
			rootOrganizationId,
		},
		{
			useIDAAS: true,
			aim,
			ignoreGlobalAim: true,
		},
	);
}

/**
 * 查询行政区划列表
 * @param 返回值
 */
export function api_getRegionList(keywords): Promise<any[]> {
	const url = '/open-api/district/v1/list';
	return http.get(
		url,
		{ keywords, pageNumber: 1, pageSize: 10000 },
		{
			useIDAAS: true,
			aim,
			ignoreGlobalAim: true,
		},
	);
}

/**
 * 查询人员列表
 * @param 返回值
 */
export function api_getPersonList(): Promise<Types.ResponsePersonInfo[]> {
	const url = '/open-api/idaas/v1/account/v1/page/list';
	return http.get(
		url,
		{ limit: 1, offset: 10000 },
		{
			useIDAAS: true,
			aim,
			ignoreGlobalAim: true,
		},
	);
}

/**
 * 查询交通方式列表
 * @param id 通用审批id
 * @param 返回值
 */
export function api_getTrafficList(): Promise<Types.ResponseTrafficInfo[]> {
	const url = '/open-api/mass/v1/costDicLevel2/list';
	return http.get(
		url,
		{
			isXingChengFei: true,
			keywords: '',
			pageNumber: 1,
			pageSize: 10000,
		},
		{
			loading: true,
		},
	);
}

/**
 * 通用审批增加——保存
 * @param data 表单数据
 * @param 返回值
 */
export function api_postAddSave(data: any, successTips = true) {
	let resultData = {
		...data,
		creatorNumber: USER_ID,
		creatorName: USER_NAME,
	};
	return http.post('/open-api/approval/v1/add', resultData, { loading: true, successTips });
}
/**
 * 通用审批修改——保存
 * @param data 表单数据
 * @param 返回值
 */
export function api_postEditSave(data: any, successTips = true) {
	return http.post('/open-api/approval/v1/update', data, { loading: true, successTips });
}
/**
 * 通用审批——提交
 * @param id 表单id
 * @param 返回值
 */
export function api_postAddOrEditSubmit(id: string) {
	return http.post('/open-api/approval/v1/submit?id=' + id, null, { loading: true, successTips: true });
}
/**
 * 通用审批——删除
 * @param id 表单id
 * @param 返回值
 */
export function api_postApprovalDelete(id: string) {
	return http.post('/open-api/approval/v1/delete?id=' + id, null, { loading: true, successTips: true });
}

//查询列表
export function getTableDataApi(params: any) {
	return http.get('/open-api/approval/v1/list', params, { loading: true });
}

//查询单个单据详情
export function getTableDataInfoApi(params: any) {
	return http.get('/open-api/approval/v1/get', params, { loading: true });
}
//催办
export function api_postApprovalRemind(id: string) {
	return http.post('/open-api/approval/v1/remind?id=' + id, null, { loading: true, successTips: true });
}
//提交
export function api_postApprovalSubmit(id: string) {
	return http.post('/open-api/approval/v1/submit?id=' + id, null, { loading: true, successTips: true });
}
//撤回
export function api_postApprovalWithdraw(id: string, reason: string) {
	return http.post('/open-api/approval/v1/withdraw?id=' + id + '&reason=' + reason, null, { loading: true, successTips: true });
}
