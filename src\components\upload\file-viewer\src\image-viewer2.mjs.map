{"version": 3, "file": "image-viewer2.mjs", "sources": ["../../../../../../packages/components/image-viewer/src/image-viewer.vue"], "sourcesContent": ["<template>\n  <teleport to=\"body\" :disabled=\"!teleported\">\n    <transition name=\"viewer-fade\" appear>\n      <div\n        ref=\"wrapper\"\n        :tabindex=\"-1\"\n        :class=\"ns.e('wrapper')\"\n        :style=\"{ zIndex }\"\n      >\n        <div :class=\"ns.e('mask')\" @click.self=\"hideOnClickModal && hide()\" />\n\n        <!-- CLOSE -->\n        <span :class=\"[ns.e('btn'), ns.e('close')]\" @click=\"hide\">\n          <el-icon><Close /></el-icon>\n        </span>\n\n        <!-- ARROW -->\n        <template v-if=\"!isSingle\">\n          <span :class=\"arrowPrevKls\" @click=\"prev\">\n            <el-icon><ArrowLeft /></el-icon>\n          </span>\n          <span :class=\"arrowNextKls\" @click=\"next\">\n            <el-icon><ArrowRight /></el-icon>\n          </span>\n        </template>\n        <!-- ACTIONS -->\n        <div :class=\"[ns.e('btn'), ns.e('actions')]\">\n          <div :class=\"ns.e('actions__inner')\">\n            <el-icon @click=\"handleActions('zoomOut')\">\n              <ZoomOut />\n            </el-icon>\n            <el-icon @click=\"handleActions('zoomIn')\">\n              <ZoomIn />\n            </el-icon>\n            <i :class=\"ns.e('actions__divider')\" />\n            <el-icon @click=\"toggleMode\">\n              <component :is=\"mode.icon\" />\n            </el-icon>\n            <i :class=\"ns.e('actions__divider')\" />\n            <el-icon @click=\"handleActions('anticlockwise')\">\n              <RefreshLeft />\n            </el-icon>\n            <el-icon @click=\"handleActions('clockwise')\">\n              <RefreshRight />\n            </el-icon>\n          </div>\n        </div>\n        <!-- CANVAS -->\n        <div :class=\"ns.e('canvas')\">\n          <img\n            v-for=\"(url, i) in urlList\"\n            v-show=\"i === activeIndex\"\n            :ref=\"(el) => (imgRefs[i] = el as HTMLImageElement)\"\n            :key=\"url\"\n            :src=\"url\"\n            :style=\"imgStyle\"\n            :class=\"ns.e('img')\"\n            :crossorigin=\"crossorigin\"\n            @load=\"handleImgLoad\"\n            @error=\"handleImgError\"\n            @mousedown=\"handleMouseDown\"\n          />\n        </div>\n        <slot />\n      </div>\n    </transition>\n  </teleport>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  effectScope,\n  markRaw,\n  nextTick,\n  onMounted,\n  ref,\n  shallowRef,\n  watch,\n} from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { throttle } from 'lodash-unified'\nimport { useLocale, useNamespace, useZIndex } from '@element-plus/hooks'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { keysOf } from '@element-plus/utils'\nimport ElIcon from '@element-plus/components/icon'\nimport {\n  ArrowLeft,\n  ArrowRight,\n  Close,\n  FullScreen,\n  RefreshLeft,\n  RefreshRight,\n  ScaleToOriginal,\n  ZoomIn,\n  ZoomOut,\n} from '@element-plus/icons-vue'\nimport { imageViewerEmits, imageViewerProps } from './image-viewer'\n\nimport type { CSSProperties } from 'vue'\nimport type { ImageViewerAction, ImageViewerMode } from './image-viewer'\n\nconst modes: Record<'CONTAIN' | 'ORIGINAL', ImageViewerMode> = {\n  CONTAIN: {\n    name: 'contain',\n    icon: markRaw(FullScreen),\n  },\n  ORIGINAL: {\n    name: 'original',\n    icon: markRaw(ScaleToOriginal),\n  },\n}\n\ndefineOptions({\n  name: 'ElImageViewer',\n})\n\nconst props = defineProps(imageViewerProps)\nconst emit = defineEmits(imageViewerEmits)\n\nconst { t } = useLocale()\nconst ns = useNamespace('image-viewer')\nconst { nextZIndex } = useZIndex()\nconst wrapper = ref<HTMLDivElement>()\nconst imgRefs = ref<HTMLImageElement[]>([])\n\nconst scopeEventListener = effectScope()\n\nconst loading = ref(true)\nconst activeIndex = ref(props.initialIndex)\nconst mode = shallowRef<ImageViewerMode>(modes.CONTAIN)\nconst transform = ref({\n  scale: 1,\n  deg: 0,\n  offsetX: 0,\n  offsetY: 0,\n  enableTransition: false,\n})\nconst zIndex = ref(props.zIndex ?? nextZIndex())\n\nconst isSingle = computed(() => {\n  const { urlList } = props\n  return urlList.length <= 1\n})\n\nconst isFirst = computed(() => {\n  return activeIndex.value === 0\n})\n\nconst isLast = computed(() => {\n  return activeIndex.value === props.urlList.length - 1\n})\n\nconst currentImg = computed(() => {\n  return props.urlList[activeIndex.value]\n})\n\nconst arrowPrevKls = computed(() => [\n  ns.e('btn'),\n  ns.e('prev'),\n  ns.is('disabled', !props.infinite && isFirst.value),\n])\n\nconst arrowNextKls = computed(() => [\n  ns.e('btn'),\n  ns.e('next'),\n  ns.is('disabled', !props.infinite && isLast.value),\n])\n\nconst imgStyle = computed(() => {\n  const { scale, deg, offsetX, offsetY, enableTransition } = transform.value\n  let translateX = offsetX / scale\n  let translateY = offsetY / scale\n\n  switch (deg % 360) {\n    case 90:\n    case -270:\n      ;[translateX, translateY] = [translateY, -translateX]\n      break\n    case 180:\n    case -180:\n      ;[translateX, translateY] = [-translateX, -translateY]\n      break\n    case 270:\n    case -90:\n      ;[translateX, translateY] = [-translateY, translateX]\n      break\n  }\n\n  const style: CSSProperties = {\n    transform: `scale(${scale}) rotate(${deg}deg) translate(${translateX}px, ${translateY}px)`,\n    transition: enableTransition ? 'transform .3s' : '',\n  }\n  if (mode.value.name === modes.CONTAIN.name) {\n    style.maxWidth = style.maxHeight = '100%'\n  }\n  return style\n})\n\nfunction hide() {\n  unregisterEventListener()\n  emit('close')\n}\n\nfunction registerEventListener() {\n  const keydownHandler = throttle((e: KeyboardEvent) => {\n    switch (e.code) {\n      // ESC\n      case EVENT_CODE.esc:\n        props.closeOnPressEscape && hide()\n        break\n      // SPACE\n      case EVENT_CODE.space:\n        toggleMode()\n        break\n      // LEFT_ARROW\n      case EVENT_CODE.left:\n        prev()\n        break\n      // UP_ARROW\n      case EVENT_CODE.up:\n        handleActions('zoomIn')\n        break\n      // RIGHT_ARROW\n      case EVENT_CODE.right:\n        next()\n        break\n      // DOWN_ARROW\n      case EVENT_CODE.down:\n        handleActions('zoomOut')\n        break\n    }\n  })\n  const mousewheelHandler = throttle((e: WheelEvent) => {\n    const delta = e.deltaY || e.deltaX\n    handleActions(delta < 0 ? 'zoomIn' : 'zoomOut', {\n      zoomRate: props.zoomRate,\n      enableTransition: false,\n    })\n  })\n\n  scopeEventListener.run(() => {\n    useEventListener(document, 'keydown', keydownHandler)\n    useEventListener(document, 'wheel', mousewheelHandler)\n  })\n}\n\nfunction unregisterEventListener() {\n  scopeEventListener.stop()\n}\n\nfunction handleImgLoad() {\n  loading.value = false\n}\n\nfunction handleImgError(e: Event) {\n  loading.value = false\n  ;(e.target as HTMLImageElement).alt = t('el.image.error')\n}\n\nfunction handleMouseDown(e: MouseEvent) {\n  if (loading.value || e.button !== 0 || !wrapper.value) return\n  transform.value.enableTransition = false\n\n  const { offsetX, offsetY } = transform.value\n  const startX = e.pageX\n  const startY = e.pageY\n\n  const dragHandler = throttle((ev: MouseEvent) => {\n    transform.value = {\n      ...transform.value,\n      offsetX: offsetX + ev.pageX - startX,\n      offsetY: offsetY + ev.pageY - startY,\n    }\n  })\n  const removeMousemove = useEventListener(document, 'mousemove', dragHandler)\n  useEventListener(document, 'mouseup', () => {\n    removeMousemove()\n  })\n\n  e.preventDefault()\n}\n\nfunction reset() {\n  transform.value = {\n    scale: 1,\n    deg: 0,\n    offsetX: 0,\n    offsetY: 0,\n    enableTransition: false,\n  }\n}\n\nfunction toggleMode() {\n  if (loading.value) return\n\n  const modeNames = keysOf(modes)\n  const modeValues = Object.values(modes)\n  const currentMode = mode.value.name\n  const index = modeValues.findIndex((i) => i.name === currentMode)\n  const nextIndex = (index + 1) % modeNames.length\n  mode.value = modes[modeNames[nextIndex]]\n  reset()\n}\n\nfunction setActiveItem(index: number) {\n  const len = props.urlList.length\n  activeIndex.value = (index + len) % len\n}\n\nfunction prev() {\n  if (isFirst.value && !props.infinite) return\n  setActiveItem(activeIndex.value - 1)\n}\n\nfunction next() {\n  if (isLast.value && !props.infinite) return\n  setActiveItem(activeIndex.value + 1)\n}\n\nfunction handleActions(action: ImageViewerAction, options = {}) {\n  if (loading.value) return\n  const { minScale, maxScale } = props\n  const { zoomRate, rotateDeg, enableTransition } = {\n    zoomRate: props.zoomRate,\n    rotateDeg: 90,\n    enableTransition: true,\n    ...options,\n  }\n  switch (action) {\n    case 'zoomOut':\n      if (transform.value.scale > minScale) {\n        transform.value.scale = Number.parseFloat(\n          (transform.value.scale / zoomRate).toFixed(3)\n        )\n      }\n      break\n    case 'zoomIn':\n      if (transform.value.scale < maxScale) {\n        transform.value.scale = Number.parseFloat(\n          (transform.value.scale * zoomRate).toFixed(3)\n        )\n      }\n      break\n    case 'clockwise':\n      transform.value.deg += rotateDeg\n      emit('rotate', transform.value.deg)\n      break\n    case 'anticlockwise':\n      transform.value.deg -= rotateDeg\n      emit('rotate', transform.value.deg)\n      break\n  }\n  transform.value.enableTransition = enableTransition\n}\n\nwatch(currentImg, () => {\n  nextTick(() => {\n    const $img = imgRefs.value[0]\n    if (!$img?.complete) {\n      loading.value = true\n    }\n  })\n})\n\nwatch(activeIndex, (val) => {\n  reset()\n  emit('switch', val)\n})\n\nonMounted(() => {\n  registerEventListener()\n  // add tabindex then wrapper can be focusable via Javascript\n  // focus wrapper so arrow key can't cause inner scroll behavior underneath\n  wrapper.value?.focus?.()\n})\n\ndefineExpose({\n  /**\n   * @description manually switch image\n   */\n  setActiveItem,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;mCAiHc,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAbA,IAAA,MAAM,KAAyD,GAAA,OAAA,CAAA;AAAA,IAAA,MACpD,KAAA,GAAA;AAAA,MAAA,OACD,EAAA;AAAA,QACN,IAAA,EAAM,SAAkB;AAAA,QAC1B,IAAA,EAAA,OAAA,CAAA,UAAA,CAAA;AAAA,OACU;AAAA,MAAA,QACF,EAAA;AAAA,QACN,IAAA,EAAM,UAAuB;AAAA,QAC/B,IAAA,EAAA,OAAA,CAAA,eAAA,CAAA;AAAA,OACF;AASA,KAAM,CAAA;AACN,IAAM,MAAA,EAAA,CAAA,EAAK,cAA2B,CAAA;AACtC,IAAM,MAAA,EAAE,eAAe,CAAU,cAAA,CAAA,CAAA;AACjC,IAAA,MAAM,YAA8B,EAAA,GAAA,SAAA,EAAA,CAAA;AACpC,IAAM,MAAA,OAAA,GAAU,GAAwB,EAAC,CAAC;AAE1C,IAAA,MAAM;AAEN,IAAM,MAAA,kBAAkB,GAAA,WAAA,EAAA,CAAA;AACxB,IAAM,MAAA,OAAA,GAAA,GAAA,CAAc,IAAI,CAAA,CAAA;AACxB,IAAM,MAAA,WAAmC,GAAA,GAAA,CAAA,KAAA,CAAM,YAAO,CAAA,CAAA;AACtD,IAAA,MAAM,iBAAgB,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA;AAAA,IAAA,MACb,SAAA,GAAA,GAAA,CAAA;AAAA,MACP,KAAK,EAAA,CAAA;AAAA,MACL,GAAS,EAAA,CAAA;AAAA,MACT,OAAS,EAAA,CAAA;AAAA,MACT,OAAkB,EAAA,CAAA;AAAA,MACnB,gBAAA,EAAA,KAAA;AACD,KAAA,CAAA,CAAA;AAEA,IAAM,MAAA,MAAA,GAAA,eAA0B,CAAA,MAAA,KAAA,IAAA,GAAA,EAAA,GAAA,UAAA,EAAA,CAAA,CAAA;AAC9B,IAAA,MAAA,QAAoB,GAAA,QAAA,CAAA,MAAA;AACpB,MAAA,MAAA,SAAe,EAAU,GAAA,KAAA,CAAA;AAAA,MAC1B,OAAA,OAAA,CAAA,MAAA,IAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,kBAA6B,CAAA,MAAA;AAAA,MAC9B,OAAA,WAAA,CAAA,KAAA,KAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,MAAmB,GAAA,QAAA,CAAA,MAAA;AAAiC,MACrD,OAAA,WAAA,CAAA,KAAA,KAAA,KAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAO,MAAA,qBAA0B,CAAA,MAAA;AAAA,MAClC,OAAA,KAAA,CAAA,OAAA,CAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AAA8B,IAClC,kBAAU,GAAA,QAAA,CAAA,MAAA;AAAA,MACV,EAAA,CAAG,EAAE,KAAM,CAAA;AAAA,MACX,GAAG,CAAG,CAAA,MAAA,CAAA;AAA4C,MACnD,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,CAAA,KAAA,CAAA,QAAA,IAAA,OAAA,CAAA,KAAA,CAAA;AAED,KAAM,CAAA,CAAA;AAA8B,IAClC,kBAAU,GAAA,QAAA,CAAA,MAAA;AAAA,MACV,EAAA,CAAG,EAAE,KAAM,CAAA;AAAA,MACX,GAAG,CAAG,CAAA,MAAA,CAAA;AAA2C,MAClD,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,CAAA,KAAA,CAAA,QAAA,IAAA,MAAA,CAAA,KAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,QAAe,GAAA,QAAc,CAAA,MAAA;AAC7B,MAAA,kBAA2B,EAAA,OAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,GAAA,SAAA,CAAA,KAAA,CAAA;AAC3B,MAAA,IAAI,aAAa,OAAU,GAAA,KAAA,CAAA;AAE3B,MAAA,IAAA,UAAc,GAAA,OAAA,GAAA,KAAA,CAAA;AAAA,MACP,QAAA,GAAA,GAAA,GAAA;AAAA,QACA,KAAA,EAAA,CAAA;AACH,QAAA,KAAA,CAAA,GAAA;AAAC,UAAA,CAAC;AACF,UAAA,CAAA,UAAA,EAAA,UAAA,CAAA,GAAA,CAAA,UAAA,EAAA,CAAA,UAAA,CAAA,CAAA;AAAA,UACG,MAAA;AAAA,QACA,KAAA,GAAA,CAAA;AACH,QAAA,KAAA,CAAA,GAAA;AAAC,UAAA,CAAC;AACF,UAAA,CAAA,UAAA,EAAA,UAAA,CAAA,GAAA,CAAA,CAAA,UAAA,EAAA,CAAA,UAAA,CAAA,CAAA;AAAA,UACG,MAAA;AAAA,QACA,KAAA,GAAA,CAAA;AACH,QAAA,KAAA,CAAA,EAAA;AAAC,UAAA,CAAC;AACF,UAAA,CAAA,UAAA,EAAA,UAAA,CAAA,GAAA,CAAA,CAAA,UAAA,EAAA,UAAA,CAAA,CAAA;AAAA,UAAA,MAAA;AAGJ,OAAA;AAA6B,MAAA,MAChB,KAAA,GAAA;AAAgE,QAC3E,SAAA,EAAA,CAAY,uBAAqC,EAAA,GAAA,CAAA,eAAA,EAAA,UAAA,CAAA,IAAA,EAAA,UAAA,CAAA,GAAA,CAAA;AAAA,QACnD,UAAA,EAAA,gBAAA,GAAA,eAAA,GAAA,EAAA;AACA,OAAA,CAAA;AACE,MAAM,IAAA,IAAA,CAAA,KAAA,CAAA,IAAW,UAAkB,CAAA,OAAA,CAAA,IAAA,EAAA;AAAA,QACrC,KAAA,CAAA,QAAA,GAAA,KAAA,CAAA,SAAA,GAAA,MAAA,CAAA;AACA,OAAO;AAAA,MACR,OAAA,KAAA,CAAA;AAED,KAAgB,CAAA,CAAA;AACd,IAAwB,SAAA,IAAA,GAAA;AACxB,MAAA,uBAAY,EAAA,CAAA;AAAA,MACd,IAAA,CAAA,OAAA,CAAA,CAAA;AAEA,KAAiC;AAC/B,IAAM,SAAA,qBAA0B,GAAA;AAC9B,MAAA,MAAA,cAAU,GAAA,QAAA,CAAA,CAAA,CAAA,KAAA;AAAA,QAAA,QAEQ,CAAA,CAAA,IAAA;AACd,UAAA,KAAA;AACA,YAAA,KAAA,CAAA,kBAAA,IAAA,IAAA,EAAA,CAAA;AAAA,YAAA,MAEc;AACd,UAAW,KAAA,UAAA,CAAA,KAAA;AACX,YAAA,UAAA,EAAA,CAAA;AAAA,YAAA,MAEc;AACd,UAAK,KAAA,UAAA,CAAA,IAAA;AACL,YAAA,IAAA,EAAA,CAAA;AAAA,YAAA,MAEc;AACd,UAAA,KAAA,UAAA,CAAc,EAAQ;AACtB,YAAA,aAAA,CAAA,QAAA,CAAA,CAAA;AAAA,YAAA,MAEc;AACd,UAAK,KAAA,UAAA,CAAA,KAAA;AACL,YAAA,IAAA,EAAA,CAAA;AAAA,YAAA,MAEc;AACd,UAAA,KAAA,UAAA,CAAc,IAAS;AACvB,YAAA,aAAA,CAAA,SAAA,CAAA,CAAA;AAAA,YAAA,MAAA;AAAA,SAEL;AACD,OAAM,CAAA,CAAA;AACJ,MAAM,MAAA,iBAAU,GAAA,QAAY,CAAA,CAAA,CAAA,KAAA;AAC5B,QAAc,MAAA,KAAA,GAAA,CAAA,CAAA,MAAQ,IAAI,CAAA,CAAA,MAAA,CAAA;AAAsB,QAAA,aAC9B,CAAA,KAAA,GAAA,CAAA,GAAA,QAAA,GAAA,SAAA,EAAA;AAAA,UAChB,QAAkB,EAAA,KAAA,CAAA,QAAA;AAAA,UACnB,gBAAA,EAAA,KAAA;AAAA,SACF,CAAA,CAAA;AAED,OAAA,CAAA,CAAA;AACE,MAAiB,kBAAA,CAAA,GAAA,CAAA,MAAU;AAC3B,QAAiB,gBAAA,CAAA,QAAA,EAAU,SAAS,EAAiB,cAAA,CAAA,CAAA;AAAA,QACtD,gBAAA,CAAA,QAAA,EAAA,OAAA,EAAA,iBAAA,CAAA,CAAA;AAAA,OACH,CAAA,CAAA;AAEA,KAAmC;AACjC,IAAA,SAAA,uBAAwB,GAAA;AAAA,MAC1B,kBAAA,CAAA,IAAA,EAAA,CAAA;AAEA,KAAyB;AACvB,IAAA,SAAA,aAAgB,GAAA;AAAA,MAClB,OAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,KAAA;AACE,IAAA,SAAA,cAAgB,CAAA,CAAA,EAAA;AACf,MAAC,OAAE,CAAA,KAAkC,GAAA,KAAkB,CAAA;AAAA,MAC1D,CAAA,CAAA,MAAA,CAAA,GAAA,GAAA,CAAA,CAAA,gBAAA,CAAA,CAAA;AAEA,KAAA;AACE,IAAA,wBAAqB,CAAE,CAAW,EAAA;AAAqB,MAAA,IAAA,OAAA,CAAA,KAAA,IAAA,CAAA,CAAA,MAAA,KAAA,CAAA,IAAA,CAAA,OAAA,CAAA,KAAA;AACvD,QAAA,OAAA;AAEA,MAAM,SAAW,CAAA,KAAA,CAAA,gBAAsB,GAAA,KAAA,CAAA;AACvC,MAAA,MAAM,SAAS,EAAE,OAAA,EAAA,GAAA,SAAA,CAAA,KAAA,CAAA;AACjB,MAAA,MAAM,SAAS,CAAE,CAAA,KAAA,CAAA;AAEjB,MAAM,MAAA,MAAA,GAAA,CAAA,CAAA,KAAuB,CAAA;AAC3B,MAAA,MAAA,WAAkB,GAAA,QAAA,CAAA,CAAA,EAAA,KAAA;AAAA,QAAA,SACH,CAAA,KAAA,GAAA;AAAA,UACb,GAAA,SAAmB,CAAA,KAAA;AAAW,UAC9B,OAAA,EAAS,OAAU,GAAA,EAAA,CAAG,KAAQ,GAAA,MAAA;AAAA,UAChC,OAAA,EAAA,OAAA,GAAA,EAAA,CAAA,KAAA,GAAA,MAAA;AAAA,SACD,CAAA;AACD,OAAA,CAAA,CAAA;AACA,MAAiB,MAAA,eAAA,GAAA,gBAA2B,CAAA,QAAA,EAAA,WAAA,EAAA,WAAA,CAAA,CAAA;AAC1C,MAAgB,gBAAA,CAAA,QAAA,EAAA,SAAA,EAAA,MAAA;AAAA,QACjB,eAAA,EAAA,CAAA;AAED,OAAA,CAAE,CAAe;AAAA,MACnB,CAAA,CAAA,cAAA,EAAA,CAAA;AAEA,KAAiB;AACf,IAAA,SAAA,KAAkB,GAAA;AAAA,MAAA,SACT,CAAA,KAAA,GAAA;AAAA,QACP,KAAK,EAAA,CAAA;AAAA,QACL,GAAS,EAAA,CAAA;AAAA,QACT,OAAS,EAAA,CAAA;AAAA,QACT,OAAkB,EAAA,CAAA;AAAA,QACpB,gBAAA,EAAA,KAAA;AAAA,OACF,CAAA;AAEA,KAAsB;AACpB,IAAA,SAAY,UAAA,GAAA;AAAO,MAAA,IAAA,OAAA,CAAA,KAAA;AAEnB,QAAM,OAAA;AACN,MAAM,MAAA,SAAA,GAAA,MAAoB,CAAA,KAAA,CAAA,CAAA;AAC1B,MAAM,MAAA,UAAA,GAAA,MAAmB,CAAM,MAAA,CAAA,KAAA,CAAA,CAAA;AAC/B,MAAA,MAAM,WAAmB,GAAA,IAAA,CAAA,KAAA,CAAA,IAAU,CAAC;AACpC,MAAM,MAAA,KAAA,GAAA,UAAqB,CAAA,SAAe,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,IAAA,KAAA,WAAA,CAAA,CAAA;AAC1C,MAAK,MAAA,aAAc,KAAU,GAAA,CAAA,IAAA,SAAA,CAAA,MAAA,CAAA;AAC7B,MAAM,IAAA,CAAA,KAAA,GAAA,KAAA,CAAA,SAAA,CAAA,SAAA,CAAA,CAAA,CAAA;AAAA,MACR,KAAA,EAAA,CAAA;AAEA,KAAA;AACE,IAAM,SAAA,aAAoB,CAAA,KAAA,EAAA;AAC1B,MAAY,MAAA,GAAA,GAAA,KAAA,CAAA,cAAwB,CAAA;AAAA,MACtC,WAAA,CAAA,KAAA,GAAA,CAAA,KAAA,GAAA,GAAA,IAAA,GAAA,CAAA;AAEA,KAAgB;AACd,IAAI,SAAA,IAAA,GAAiB;AAAiB,MAAA,IAAA,OAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,QAAA;AACtC,QAAc,OAAA;AAAqB,MACrC,aAAA,CAAA,WAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AAEA,KAAgB;AACd,IAAI,SAAA,IAAO,GAAS;AAAiB,MAAA,IAAA,MAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,QAAA;AACrC,QAAc,OAAA;AAAqB,MACrC,aAAA,CAAA,WAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AAEA,KAAuB;AACrB,IAAA,SAAY,aAAA,CAAA,MAAA,EAAA,OAAA,GAAA,EAAA,EAAA;AAAO,MAAA,IAAA,OAAA,CAAA,KAAA;AACnB,QAAM;AACN,MAAM,MAAA,EAAE,QAAU,EAAA,QAAA,EAAA,GAAgC,KAAA,CAAA;AAAA,MAAA,gBAChC,EAAA,SAAA,EAAA,gBAAA,EAAA,GAAA;AAAA,QAChB,QAAW,EAAA,KAAA,CAAA,QAAA;AAAA,QACX,SAAkB,EAAA,EAAA;AAAA,QAClB,gBAAG,EAAA,IAAA;AAAA,QACL,GAAA,OAAA;AACA,OAAQ,CAAA;AAAA,MACD,QAAA,MAAA;AACH,QAAI,KAAA,SAAA;AACF,UAAU,IAAA,SAAA,CAAA,KAAc,CAAA,KAAA,GAAA,QACrB,EAAA;AACH,YACF,SAAA,CAAA,KAAA,CAAA,KAAA,GAAA,MAAA,CAAA,UAAA,CAAA,CAAA,SAAA,CAAA,KAAA,CAAA,KAAA,GAAA,QAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,WAAA;AAAA,UACG,MAAA;AACH,QAAI,KAAA,QAAA;AACF,UAAU,IAAA,SAAA,CAAA,KAAc,CAAA,KAAA,GAAA,QACrB,EAAA;AACH,YACF,SAAA,CAAA,KAAA,CAAA,KAAA,GAAA,MAAA,CAAA,UAAA,CAAA,CAAA,SAAA,CAAA,KAAA,CAAA,KAAA,GAAA,QAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,WAAA;AAAA,UACG,MAAA;AACH,QAAA,KAAA;AACA,UAAK,SAAA,CAAA,KAAU,CAAU,GAAA,IAAA,SAAS,CAAA;AAClC,UAAA,IAAA,CAAA,QAAA,EAAA,SAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AAAA,UACG,MAAA;AACH,QAAA,KAAA,eAAuB;AACvB,UAAK,SAAA,CAAA,KAAU,CAAU,GAAA,IAAA,SAAS,CAAA;AAClC,UAAA,IAAA,CAAA,QAAA,EAAA,SAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AAAA,UAAA,MAAA;AAEJ,OAAA;AAAmC,MACrC,SAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,gBAAA,CAAA;AAEA,KAAA;AACE,IAAA,KAAA,CAAA,UAAe,EAAA,MAAA;AACb,MAAM,QAAA,CAAA,MAAO;AACb,QAAI,aAAiB,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACnB,QAAA,IAAA,EAAA,IAAQ,IAAQ,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,QAAA,CAAA,EAAA;AAAA,UAClB,OAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,SACD;AAAA,OACF,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAM,KAAA,CAAA,WAAA,EAAA,CAAA,GAAA,KAAA;AACN,MAAA,KAAK;AAAa,MACnB,IAAA,CAAA,QAAA,EAAA,GAAA,CAAA,CAAA;AAED,KAAA,CAAA,CAAA;AACE,IAAsB,SAAA,CAAA,MAAA;AAGtB,MAAA,IAAA,GAAA;AAAuB,MACxB,qBAAA,EAAA,CAAA;AAED,MAAa,CAAA,EAAA,GAAA,CAAA,GAAA,GAAA,OAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,GAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AAAA,KAIX,CAAA,CAAA;AAAA,IACF,MAAC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}