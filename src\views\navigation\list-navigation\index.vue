<template>
	<div class="navigation-list-template">
		<div class="demo-t">
			<div class="header-r">
				默认导航样式
			</div>

			<div class="main-r">
				<demo1 />
			</div>
		</div>


		<div class="demo-t">
			<div class="header-r">
				带有badge的导航
			</div>

			<div class="main-r">
				<demo2 />
			</div>
		</div>

		<div class="demo-t">
			<div class="header-r">
				带有操作的导航
			</div>

			<div class="main-r">
				<demo3 />
			</div>
		</div>

		<div class="demo-t">
			<div class="header-r">
				自定义某一项的操作
			</div>

			<div class="main-r">
				<demo4 />
			</div>
		</div>

		<div class="demo-t">
			<div class="header-r">
				同时使用操作和badge
			</div>

			<div class="main-r">
				<demo5 />
			</div>
		</div>

		<div class="demo-t">
			<div class="header-r">
				隐藏搜索
			</div>

			<div class="main-r">
				<demo6 />
			</div>
		</div>

		
		<div class="demo-t">
			<div class="header-r">
				带操作的搜索
			</div>

			<div class="main-r">
				<demo7 />
			</div>
		</div>
	</div>
</template>
<script setup lang="tsx">
import demo1 from './components/demo1.vue';
import demo2 from './components/demo2.vue'
import demo3 from './components/demo3.vue'
import demo4 from './components/demo4.vue'
import demo5 from './components/demo5.vue'
import demo6 from './components/demo6.vue'
import demo7 from './components/demo7.vue'

</script>
<style lang="scss" scoped>
.navigation-list-template {
	width: 100%;
	height: 100%;
	padding: 8px;

	display: flex;

	.demo-t {
		width: 240px;
		height: calc(100% - 16px);

		flex-shrink:  0;

		display: flex;
		flex-direction: column;
		margin-right: 20px;

		& > .header-r {
			padding: 0 20px;
		}

		& > .main-r {
			margin-top: 8px;
			border: 1px solid rgba($color: #000000, $alpha: .1);
			height: 0;
			flex-grow: 1;
		}
	}
}
</style>
