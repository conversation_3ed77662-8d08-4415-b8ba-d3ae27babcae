<template>
	<div class="collapse-button" @click="handleClick" :class="{ 'is-active': !hasActive }">
		<icon-font :type="hasActive ? activeIcon : closeIcon"></icon-font>
	</div>
</template>
<script lang="ts" setup>
/**
 * 自定义按钮组件，折叠展示不同状态图标
 */
import { ref } from 'vue';
interface Props {
	active?: boolean;     // 是否为活跃状态
	activeIcon?: string;  // 活跃状态下图标
	closeIcon?: string;   // 关闭状态下图标
}
const props = withDefaults(defineProps<Props>(), {
	active: true,
	activeIcon: '',
	closeIcon: '',
});
const emit = defineEmits<{
	(ev: 'changeType', value: boolean): void;
}>();

var hasActive = ref(props.active);

function handleClick() {
	hasActive.value = !hasActive.value;
	emit('changeType', hasActive.value);
}
</script>
<style lang="scss" scoped>
.collapse-button {
	width: 32px;
	height: 32px;
	background: #ffffff;
	border-radius: 3px;
	border: 1px solid #e5e6eb;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	box-sizing: border-box;
	font-size: 16px;
	color: #86909c;
}

.collapse-button:hover {
	background: #f2f3f5;
	color: var(--el-color-primary);
}

.is-active {
	border: 1px solid var(--el-color-primary);
	color: var(--el-color-primary);
}
</style>
