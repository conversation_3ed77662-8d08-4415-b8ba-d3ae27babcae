<template>
	<div class="go-back-button">
		<icon-font :type="type"></icon-font>
	</div>
</template>
<script lang="ts" setup>
interface Props {
	type?: string; // 图标
}
const props = withDefaults(defineProps<Props>(), {
	type: '',
});
</script>
<style lang="scss" scoped>
.go-back-button {
	width: 32px;
	height: 32px;
	background: #ffffff;
	border-radius: 3px;
	border: 1px solid #e5e6eb;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	box-sizing: border-box;
	font-size: 16px;
	color: #86909c;
}

.go-back-button:hover {
	background: #f2f3f5;
	color: var(--el-color-primary);
}

.go-back-button:active {
	border: 1px solid var(--el-color-primary);
	color: var(--el-color-primary);
}
</style>
