<!--
  功能：excel导入组件-业务数据导入使用
  作者：ligw
  时间：2024年08月08日 16:07:08
  版本：v1.0
  修改记录：
  修改内容：
  修改人员：
  修改时间：
-->
<template>
    <div  @click="open">
      <slot></slot>
      <ElButton
        v-if="!$slots.default" 
        class="excel-upload-btn"  
        plain
        type="primary"
      >
      {{title}}
      </ElButton>
    </div>
    <ElDialog 
      v-model="visible" 
      :title="dialogTitle" 
      :close-on-click-modal="false"
      class="import-dialog"
    >
      <BatchImport 
        :exportFun="exportFun"
        :uploadFun="uploadFun" 
        v-bind="$attrs" 
        @complete="handleComplete"
        @cancel="close"
      />
    </ElDialog>
</template>
<script lang='ts' setup>
import { ref, defineExpose } from 'vue'
interface Props{
  title?: string;
  dialogTitle?: string;
  preview?: boolean; //是否预览文件
	multiple?: boolean;
  limit?: number;
  size?: number;
  accept?: string;
  uploadFun: (files: FormData) => XMLHttpRequest | Promise<ImportResultResponse>;
  // 模板导出方法
  exportFun: (prams:Function)=>void;

}
const props = withDefaults(defineProps<Props>(), {
  title: '弹窗导入',
  dialogTitle: '模板导入',
  preview: true,
  multiple: false,
  limit: 1,
  accept: '.xls,.xlsx',
  size: 5
})
const batchImportRef = ref<BatchImport>()
const result = ref<ImportResultResponse>({
  code: 0,
  data: [{
    failedResultList: [],
    totalCount: 0,
    failedCount: 0,
    successCount: 0
  }],
  message:''
})
const visible = ref<boolean>(false)
const open = () => {
  visible.value= true
}
const close = () => {
  visible.value = false
}
// 上传后，外发结果
const emit = defineEmits<{
	(ev: 'complete', response: any): void;
}>();
const handleComplete = (response: any) => {
  close()
  emit('complete',response)
}


</script>
<style  lang='scss'>
.import-dialog{
  width: 560px;
  padding: 0;
  .el-dialog__header{
    padding: 12px 20px;
    border-bottom: 1px solid #E5E6EB;
  }
  .el-dialog__body{
    padding: 10px 20px 16px 20px;
  }
}
</style>
