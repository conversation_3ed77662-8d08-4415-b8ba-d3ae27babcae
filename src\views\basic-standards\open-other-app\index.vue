<template>
	<div class="dialog-content">
		<el-button
			type="primary"
			class="button"
			@click="openPage()"
			>打开其他子应用指定页面</el-button
		>
		<div class="tip-text">需在测试环境方可验证，本地开发无法验证</div>
	</div>
</template>

<script lang="tsx" setup>
import { ref } from 'vue';
import { sendOpenOtherAppPageMsg } from '@/utils/message';

const openPage = () => {
	let appName = 'safety-assessment'; // 子应用名称
	let routerUrl = '/safe-production-check/special-reporting'; // 路由路径
	let routerParams = {
		// 路由参数
		k1: 123,
	};

	// 发送打开其他应用页面消息
	sendOpenOtherAppPageMsg(appName, routerUrl, routerParams);
};
</script>

<style lang="scss" scoped>
.dialog-content {
	padding: 20px;

	.button {
		margin-left: 20px;
	}
}
</style>
