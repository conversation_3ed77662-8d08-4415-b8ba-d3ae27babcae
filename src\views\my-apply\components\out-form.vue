<template>
    <header-main :viewTitle="showTitle" @close="close" :showCloseBtn="true">
        <template #main>
            <div class="edit-form">
                <div class="main">
                    <el-form ref="formRef" :model="ruleForm" :rules="rules" class="form-view" label-width="108px"
                        label-position="left">
                        <el-form-item label="申请单位" prop="companyNumber">
                            <el-select v-model="ruleForm.companyNumber" placeholder="请选择申请单位"
                                @change="unitChangeAction">
                                <el-option v-for="item in companyList" :key="item.uuid" :label="item.name"
                                    :value="item.uuid" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="申请部门" prop="deptNumber">
                            <el-select ref="deptRef" v-model="ruleForm.deptNumber" placeholder="请选择申请部门"
                                :loading="deptLoading" @change="deptChangeAction"
                                @click.native.prevent="handleDeptClick">
                                <el-option v-for="item in deptList" :key="item.uuid" :label="item.name"
                                    :value="item.uuid" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="出差人" prop="userdata">
                            <el-select class="multiple-select" v-model="ruleForm.userdata" multiple placeholder="请选择出差人"
                                @change="personChangeAction">
                                <el-option v-for="item in personList" :key="item.userNumber" :label="item.userName"
                                    :value="item" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="职务" prop="projectName">
                            <el-input v-model="ruleForm.projectName" placeholder="请输入" clearable />
                        </el-form-item>
                        <el-form-item label="拟出差开始/结束时间" prop="fullTime">
                            <div class="demo-datetime-picker">
                                <div class="block">
                                    <el-date-picker v-model="ruleForm.fullTime" type="datetimerange"
                                        start-placeholder="请选择开始时间" end-placeholder="请选择开始时间" format="YYYY-MM-DD HH:mm"
                                        date-format="YYYY/MM/DD" time-format="hh:mm" @change="timeChange" />
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item label="拟乘坐交通工具" prop="vehicleNumber">
                            <el-select v-model="ruleForm.vehicleNumber" placeholder="请选择拟乘坐交通工具"
                                @change="trafficChangeAction">
                                <el-option v-for="item in trafficList" :key="item.costNumber" :label="item.costName"
                                    :value="item.costNumber" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="预计出差费用" prop="accountThousands">
                            <AmountInput v-model="ruleForm.accountThousands" placeholder="请输入" style="width: 70%;" />
                        </el-form-item>
                        <el-form-item label="出差事由" prop="title">
                            <el-input rows="4" resize="vertical" maxlength="80" show-word-limit v-model="ruleForm.title"
                                placeholder="请输入" type="textarea" />
                        </el-form-item>
                        <div class="address">
                            <div class="xirui-flex-row-center address-header">
                                <div class="address-header-tag"></div>
                                <div class="address-header-text">出差地点</div>
                            </div>
                            <el-form-item class="no-label" label="" prop="extra_billDetails">
                                <out-form-table ref="pTableRef"></out-form-table>
                            </el-form-item>
                        </div>
                        <el-form-item label="附件" prop="fileList">
                            <el-upload v-model:file-list="fileList" class="upload-file"
                                action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15" multiple
                                :on-preview="handlePreview" :on-remove="handleRemove" :before-remove="beforeRemove"
                                :limit="3" accept="pdf、jpg、png、gif、doc、docx、xls、xlsx">
                                <div class="xirui-flex-row-center">
                                    <el-button>上传文件</el-button>
                                    <div class="file-tip">支持扩展名：pdf、jpg、png、gif、doc、docx、xls、xlsx</div>
                                </div>
                                <!-- <template #tip>
							<div class="el-upload__tip">jpg/png files with a size less than 500KB.</div>
						</template> -->
                            </el-upload>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="footer">
                    <div class="footer-view">
                        <el-button type="primary" :loading="submitLoading" @click="submitAction">提交</el-button>
                        <template v-if="isEdit">
                            <el-button :loading="saveLoading" @click="saveAction(false)">保存</el-button>
                            <el-button type="danger" plain :loading="deleteLoading" @click="deleteAction">删除</el-button>
                        </template>
                        <template v-else>
                            <el-button :loading="saveLoading" @click="saveAction(false)">保存草稿</el-button>
                            <el-button @click="cancelAction">取消</el-button>
                        </template>
                    </div>
                </div>
            </div>
        </template>
    </header-main>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, getCurrentInstance, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules, UploadProps, UploadUserFile } from 'element-plus';
import { api_getUnitsList, api_getDepartsList, api_postAddSave, api_postEditSave, api_postAddOrEditSubmit, api_getApprovalDetail, api_postApprovalDelete, USER_ID, USER_NAME, api_getPersonList, api_getTrafficList } from '../api';
import { ResponseUnitInfo, ResponseDepartInfo, ResponsePersonInfo, ResponseTrafficInfo } from '../api/types';
import AmountInput from './AmountInput.vue'
import outFormTable from './out-form-table.vue';

interface Props {
    title: string; // 标题
    type: string; // 类型  add:新增 edit:编辑
    rowID: string; // 表单id
}
const props = withDefaults(defineProps<Props>(), {
    title: '出差申请',
    type: 'add',
    rowID: '', // 表单id
});

const emit = defineEmits<{
    (e: 'close'): void;
    (e: 'success'): void;
}>();

// 监听 type 和 rowID 变化，自动请求详情
watch(
    () => [props.type, props.rowID], // 监听的依赖数组
    ([newType, newId]) => {
        if (newType === 'edit' && newId) {
            // 当类型为编辑且 id 存在时请求详情
            getDetail(newId)
        } else {
            // 非编辑状态时清空详情数据
        }
    },
    { immediate: true } // 组件初始化时立即执行一次
)

interface RuleForm {
    extendField1: string;
    title: string;
    bDescription: string;
    companyNumber: string;
    companyName: string;
    deptNumber: string;
    deptName: string;
    extendField2: string;
    phone: string;
    // fileList: any[];
    extra_files: any[];
    cNumber: string;
    projectName: string;
    bFormFlag: number;
    rowID: string;
    subProjectID: string;
    extra_billDetails: any[];
    userdata: any[];
    accountThousands: number | string; // 金额千分位
    fullTime: string[]; // 拟出差开始/结束时间
    vehicleNumber: string; // 拟乘坐交通工具id
    vehicleName: string; // 拟乘坐交通工具名
}
const formRef = ref<FormInstance>();
// 子组件实例
const pTableRef = ref(null);
const ruleForm = reactive<RuleForm>({
    extendField1: "",
    title: "",
    bDescription: "",
    companyNumber: "",
    companyName: "",
    deptNumber: "",
    deptName: "",
    extendField2: "",
    phone: "",
    // fileList: any[];
    extra_files: [],
    cNumber: "CB031",
    projectName: "",
    bFormFlag: 31,
    rowID: "",
    subProjectID: "",
    extra_billDetails: [],
    userdata: [{
        userNumber: USER_ID,
        userName: USER_NAME,
    },],
    accountThousands: "",
    fullTime: ["", ""],
    vehicleNumber: "",
    vehicleName: "",
});
const rules = reactive<FormRules<RuleForm>>({
    companyNumber: [
        {
            required: true,
            message: '请选择申请单位',
            trigger: 'change',
        },
    ],
    deptNumber: [
        {
            required: true,
            message: '请选择申请部门',
            trigger: 'change',
        },
    ],
    userdata: [
        {
            required: true,
            message: '请选择出差人',
            trigger: 'change',
        },
    ],
    projectName: [
        {
            required: true,
            message: '请输入职务',
            trigger: 'blur',
        },
    ],
    bDescription: [
        {
            required: true,
            message: '请输入主要采购内容',
            trigger: 'blur',
        },
    ],
    fullTime: [
        // 1. 必选校验：确保选择了完整的时间范围
        {
            required: true,
            message: '请选择时间范围',
            trigger: 'change'
        },
        // 2. 自定义校验：开始时间不能晚于结束时间
        {
            validator: (rule, value, callback) => {
                // value 是 [startTime, endTime] 数组

                if (value && value.length === 2) {
                    const tempStartTime = new Date(value[0])
                    const tempEndTime = new Date(value[1])
                    if (value[0] === '') {
                        callback(new Error('请选择时间范围'))
                    } else if (tempStartTime >= tempEndTime) {
                        callback(new Error('开始时间不能晚于结束时间'))
                    } else {
                        callback() // 校验通过
                    }
                } else {
                    callback() // 空值时不校验（由 required 规则处理）
                }
            },
            trigger: 'change'
        },
    ],
    vehicleNumber: [
        { required: true, message: '请选择拟乘坐交通工具', trigger: 'change' }
    ],
    accountThousands: [
        {
            required: true,
            message: '请输入预计金额',
            trigger: 'blur',
        },
    ],
    title: [
        {
            required: true,
            message: '请输入出差事由',
            trigger: 'blur',
        },
    ],
    extra_billDetails: [
        // {
        //     required: true,
        //     message: '请选择出差地点',
        //     trigger: 'blur'
        // },
        {
            validator: async (rule, value, callback) => {
                // 1. 通过ref获取子组件的tableData
                const tableData = pTableRef.value?.getTableData() || []
                ruleForm.extra_billDetails = tableData;
                // 2. 校验数组是否为空
                if (tableData.length === 0) {
                    callback(new Error('出差地点不能为空'))
                } else {
                    // let hasInvalidData = false;
                    // for (const item in tableData) {
                    //     if (!(item.StartCityNumber && item.EndCityNumber)) {
                    //         hasInvalidData = true;
                    //         break;
                    //     }
                    // }
                    if (tableData.some(item => !item.StartCityNumber) || tableData.some(item => !item.EndCityNumber)) {
                        callback(new Error('请填写完整的出差地点信息'))
                    } else {
                        callback() // 校验通过
                    }
                }
            },
            trigger: ['submit', 'blur'] // 提交或失焦时校验
        }
    ]
});

const companyList = ref<ResponseUnitInfo[]>([]); // 单位列表
const deptList = ref<ResponseDepartInfo[]>([]); // 部门列表
const deptRef = ref(null);
const personList = ref<ResponsePersonInfo[]>([]); // 人员列表
const trafficList = ref<ResponseTrafficInfo[]>([]); // 交通列表

const deptLoading = ref(false); // 部门列表加载状态
const saveLoading = ref(false); // 保存按钮加载状态
const submitLoading = ref(false); // 提交按钮加载状态
const deleteLoading = ref(false); // 删除按钮加载状态

// const fullTime = ref([]); // [开始时间,结束时间]
const startTime = ref(''); // 开始时间
const endTime = ref(''); // 结束时间
// 获取组件实例
const instance = getCurrentInstance()
// 通过实例访问全局的 $moment
const $moment = instance.appContext.config.globalProperties.$moment;

// 计算属性
const isEdit = computed(() => props.type === 'edit');
const showTitle = computed(() => {
    let tempTitle = isEdit.value ? '修改' : '增加';
    return tempTitle + props.title;
});
const userName = computed(() => {
    return ruleForm.userdata.map((m) => m.userName).join();
})
const userNumber = computed(() => {
    return ruleForm.userdata.map((m) => m.userNumber).join();
})

// 单位切换
function unitChangeAction(newValue: any) {
    // 根据新value匹配对应的选项对象
    const selectedOption = companyList.value.find((item) => item.uuid === newValue) || {};
    ruleForm.companyName = selectedOption.name || '';
    // 重置部门数据
    ruleForm.deptNumber = '';
    ruleForm.deptName = '';
    // 获取部门数据
    getDepartsList(ruleForm.companyNumber);
}
// 部门切换
function deptChangeAction(newValue: any) {
    const selectedOption = deptList.value.find((item) => item.uuid === newValue) || {};
    ruleForm.deptName = selectedOption.name || '';
}
// 人员切换
function personChangeAction(newValue: any) {
    console.log('人员切换', newValue);
}
// 交通切换
function trafficChangeAction(newValue: any) {
    // 根据新value匹配对应的选项对象
    const selectedOption = trafficList.value.find((item) => item.costNumber === newValue) || {};
    ruleForm.vehicleName = selectedOption.costNumber || '';
}

// 时间切换
function timeChange(timeRange: any) {
    console.log('日期切换', timeRange);
    if (timeRange) {
        // 解构数组：[开始时间, 结束时间]
        const [start, end] = timeRange
        startTime.value = $moment(start).format("YYYY-MM-DD HH:mm:ss")
        endTime.value = $moment(end).format("YYYY-MM-DD HH:mm:ss")
    } else {
        // 清空选择时重置
        startTime.value = ''
        endTime.value = ''
    }
    console.log('1日期切换', startTime.value);
    console.log('2日期切换', endTime.value);
}

// 表单重置
const resetForm = () => {
    formRef.value?.resetFields();
    Object.assign(ruleForm, {
        extendField1: "",
        title: "",
        bDescription: "",
        companyNumber: "",
        companyName: "",
        deptNumber: "",
        deptName: "",
        extendField2: "",
        phone: "",
        // fileList: any[];
        extra_files: [],
        cNumber: "CB031",
        projectName: "",
        bFormFlag: 31,
        rowID: "",
        subProjectID: "",
        extra_billDetails: [],
        userdata: [{
            userNumber: USER_ID,
            userName: USER_NAME,
        },],
        accountThousands: "",
        fullTime: ["", ""],
        vehicleNumber: "",
        vehicleName: "",
    });
};

// 保存/保存草稿
function saveAction(isSubmit = false) {
    formRef.value?.validate((valid) => {
        if (valid) {
            // 校验通过，执行提交逻辑
            console.log('提交的数据:', ruleForm);
            saveForm(isSubmit); // 提交时，先保存，再提交 
        } else {
            // 校验失败
            ElMessage.error('表单校验失败，请检查输入');
            console.log('提交的数据:', ruleForm);
        }
    });
}
// 提交
function submitAction() {
    saveAction(true);
}

// 删除
function deleteAction() {
    ElMessageBox.confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
    })
        .then(() => {
            deleteForm();
        })
        .catch(() => {
        })
}
// 取消
function cancelAction() {
    close();
}

// 关闭页面
function close() {
    console.log('close', ruleForm.userdata);

    resetForm();
    emit('close');
}

// 操作成功：保存/保存草稿/提交/删除
function optionSuccess() {
    // ElMessage.success('操作成功');
    emit('success');
}

function handleDeptClick() {
    // 判断其他变量是否有值
    if (!ruleForm.companyNumber) {
        // 无值：提示并不展开
        ElMessage.warning('请先选择申请单位');
        return;
    }

    // 有值：手动触发下拉框展开
    // el-select 内置方法 togglePopup() 可切换展开/收起状态
    if (deptRef.value) {
        deptRef.value.togglePopup();
    }
}



const fileList = ref<UploadUserFile[]>([
    // {
    // 	name: 'element-plus-logo.svg',
    // 	url: 'https://element-plus.org/images/element-plus-logo.svg',
    // },
    // {
    // 	name: 'element-plus-logo2.svg',
    // 	url: 'https://element-plus.org/images/element-plus-logo.svg',
    // },
]);

const handleRemove: UploadProps['onRemove'] = (file, uploadFiles) => {
    console.log(file, uploadFiles);
};

const handlePreview: UploadProps['onPreview'] = (uploadFile) => {
    console.log(uploadFile);
};

const handleExceed: UploadProps['onExceed'] = (files, uploadFiles) => {
    ElMessage.warning(
        `The limit is 3, you selected ${files.length} files this time, add up to ${files.length + uploadFiles.length} totally`,
    );
};

const beforeRemove: UploadProps['beforeRemove'] = (uploadFile, uploadFiles) => {
    return ElMessageBox.confirm(`Cancel the transfer of ${uploadFile.name} ?`).then(
        () => true,
        () => false,
    );
};

onMounted(() => {
    getUnitsList();
    getPersonList();
    getTrafficList();
});

// 获取详情
function getDetail(rowID: string) {
    api_getApprovalDetail(rowID).then((res) => {
        console.log('获取详情成功', res);
        const data = res[0];
        ruleForm.rowID = rowID;
        ruleForm.title = data.title;
        ruleForm.bDescription = data.bDescription;
        ruleForm.extra_files = data.extra_files;
        ruleForm.companyName = data.companyName;
        ruleForm.companyNumber = data.companyNumber;
        ruleForm.deptNumber = data.deptNumber;
        ruleForm.deptName = data.deptName;
        ruleForm.projectName = data.projectName;
        // .createName = data.creatorName;
        ruleForm.extra_billDetails = data.extra_billDetails.map((m) => {
            return {
                EndCityName: m.EndCityName,
                EndCityNumber: m.EndCityNumber,
                StartCityNumber: m.StartCityNumber,
                StartCityName: m.StartCityName,
            };
        });
        getDepartsList(ruleForm.companyNumber);
        const userName = data.bDescription.split("|")[0].split(",");
        const userNumber = data.bDescription.split("|")[1].split(",");
        ruleForm.userdata = [];
        userName.forEach((f, index) => {
            ruleForm.userdata.push({
                userName: f,
                userNumber: userNumber[index],
            });
        });

        ruleForm.accountThousands = data.extendField2.split("|")[2];
        startTime.value = $moment(data.extendField2.split("|")[0]).format(
            "YYYY-MM-DD HH:mm:ss"
        );
        endTime.value = $moment(data.extendField2.split("|")[1]).format(
            "YYYY-MM-DD HH:mm:ss"
        );
        ruleForm.fullTime = [startTime.value, endTime.value];
        ruleForm.vehicleNumber = data.extendField2.split("|")[3];
        ruleForm.vehicleName = data.extendField2.split("|")[4];
    });
}

// 获取单位列表
function getUnitsList() {
    api_getUnitsList().then((res) => {
        companyList.value = res;
    });
}

// 获取部门列表
function getDepartsList(unitId = '') {
    deptLoading.value = true;
    api_getDepartsList(unitId).then((res) => {
        deptList.value = res;
    }).finally(() => {
        deptLoading.value = false;
    });
}

// 获取出差人列表
function getPersonList() {
    api_getPersonList().then((res) => {
        personList.value = res[0].row || [{
            userNumber: USER_ID,
            userName: USER_NAME,
        },];
    });
}

// 获取交通工具列表
function getTrafficList() {
    api_getTrafficList().then((res) => {
        trafficList.value = res[0].records;
    });
}

// 保存/保存草稿表单
function saveForm(isSubmit = false) {
    if (!isSubmit) {
        saveLoading.value = true;
    }
    let url = api_postAddSave;
    if (isEdit.value) {
        url = api_postEditSave;
    }
    // const account = parseFloat(ruleForm.accountThousands.replace(/,/g, ""));
    const account = ruleForm.accountThousands;
    ruleForm.bDescription = `${userName.value}|${userNumber.value}`;
    ruleForm.extendField2 = `${startTime.value}|${endTime.value}|${account}|${ruleForm.vehicleNumber}|${ruleForm.vehicleName}`;
    url(ruleForm, !isSubmit).then((res: any) => {
        console.log('保存成功:', res);
        if (isSubmit) {
            // 提交
            ruleForm.rowID = res[0].rowID;
            submitForm();
        } else {
            // 保存成功
            // ElMessage.success('保存成功');
            optionSuccess();
        }

    }).finally(() => {
        saveLoading.value = false;
    });
}

// 提交表单
function submitForm() {
    submitLoading.value = true;
    api_postAddOrEditSubmit(ruleForm.rowID).then((res: any) => {
        optionSuccess();
    }).finally(() => {
        submitLoading.value = false;
    });
}
// 删除表单
function deleteForm() {
    deleteLoading.value = true;
    api_postApprovalDelete(ruleForm.rowID).then((res: any) => {
        optionSuccess();
    }).finally(() => {
        deleteLoading.value = false;
    });
}
</script>
<style lang="scss" scoped>
.edit-form {
    display: flex;
    flex-direction: column;
    height: 100%;

    .main {
        flex: 1;
        min-height: 0;
        overflow: auto;

        .form-view {
            width: 777px;
            margin: 20px auto;
            padding-right: 150px;
        }

        .upload-file {
            width: 100%;

            .file-tip {
                margin-left: 15px;
                font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
                font-weight: 400;
                font-size: 14px;
                color: #86909c;
                line-height: 14px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }

        .address {
            // background: #F7F8FA;
            // border-radius: 4px 4px 4px 4px;
            border: 1px solid #E5E6EB;
            width: 100%;

            padding: 0 8px;
            margin-bottom: 18px;

            .address-header {
                height: 48px;

                &-tag {
                    width: 3px;
                    height: 16px;
                    background: #0060C1;
                    border-radius: 0px 0px 0px 0px;
                }

                &-text {
                    margin-left: 8px;
                    font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
                    font-weight: 500;
                    font-size: 16px;
                    color: #1D2129;
                    line-height: 24px;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                }
            }


        }


    }

    .footer {
        border-top: 1px solid #e5e6eb;
        height: 64px;
        display: flex;
        align-items: center;

        .footer-view {
            width: 777px;
            margin: 0 auto;
        }
    }
}

.demo-datetime-picker {
    display: flex;
    width: 100%;
    padding: 0;
    flex-wrap: wrap;
    justify-content: space-around;
    align-items: stretch;
}

.demo-datetime-picker .block {
    flex: 1;
    text-align: center;
}

.line {
    width: 1px;
    background-color: var(--el-border-color);
}

/* 使用深度选择器修改后缀容器宽度 */
.form-view :deep(.el-input-group__append) {
    /* 设置固定宽度 */
    width: 40px;
}

/* 针对单个表单项修改label宽度为0 */
.form-view :deep(.no-label .el-form-item__label) {
    width: 0 !important;
    /* 宽度设为0 */
    padding-right: 0 !important;
    /* 移除右侧内边距 */
    overflow: hidden;
    /* 隐藏溢出的label文本 */
}

/* 同步调整内容区域的偏移（可选，根据实际布局调整） */
.form-view :deep(.no-label .el-form-item__content) {
    margin-left: 0 !important;
    /* 移除内容区域的左偏移 */
}

.form-view :deep(.multiple-select .el-select__wrapper) {
    padding-left: 16px !important;
}
</style>