<template>
	<div class="aside-main-box">
		<div
			class="aside"
			:class="{ 'close-aside': !showAside }"
			:style="{ width: asideWidth + 'px' }"
		>
			<slot name="aside"></slot>
		</div>
		<div class="main">
			<div
				v-if="showMainHeader"
				class="main-header"
			>
				<div
					class="header-title"
					:class="{ 'header-title-click': headerTitleClick }"
					@click="clickHeaderTitle"
				>
					{{ titleText }}
				</div>
				<el-divider
					v-if="$slots.header"
					class="header-divider"
					direction="vertical"
				></el-divider>
				<slot name="header"></slot>
			</div>
			<div class="main-content">
				<slot name="main"></slot>
			</div>
		</div>
	</div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
interface Props {
	asideWidth?: number; // 左侧区域的宽度
	showMainHeader?: boolean; //是否展示头部
	titleText?: string; // 标题文字
	headerTitleClick?: boolean; //头部标题是否可以点击
}
const props = withDefaults(defineProps<Props>(), {
	asideWidth: 240, // 默认宽度
	showMainHeader: true,
	titleText: '',
	headerTitleClick: false,
});
const emit = defineEmits<{
	// (ev: 'changeType', value: boolean): void;
	(ev: 'click-header-title'): void;
}>();

var showAside = ref(true); // 展示左侧内容
//
const clickHeaderTitle = () => {
	props.headerTitleClick && emit('click-header-title');
};
// 切换左侧区块展示
const changeAsideShow = (val: boolean) => {
	console.log('changeAsideShow', val);
	showAside.value = val;
};
</script>
<style lang="scss" scoped>
.aside-main-box {
	display: flex;
	height: 100%;
	.aside {
		border-right: 1px solid #e5e6eb;
		transition: width 0.3s ease;
	}

	.close-aside {
		width: 0 !important;
		border-right: 0;
	}

	.main {
		flex: 1;
		z-index: 1;
		min-width: 0;
		display: flex;
		flex-direction: column;
		background-color: #ffffff;
		.main-header {
			height: 48px;
			border-bottom: 1px solid #e5e6eb;
			display: flex;
			align-items: center;
			padding: 0 8px;

			.header-title {
				font-family: PingFangSC, PingFang SC;
				font-weight: 500;
				font-size: 18px;
				color: #1d2129;
				line-height: 22px;
				margin-left: 8px;
			}
			.header-title-click {
				cursor: pointer;
				color: #0075c2;
			}
			.header-divider {
				height: 12px;
				margin: 0 20px;
				border-left: 1px solid #c9cdd4;
			}
		}

		.main-content {
			flex: 1;
			min-height: 0;
			display: flex;
			flex-direction: column;
		}
	}
}
</style>
