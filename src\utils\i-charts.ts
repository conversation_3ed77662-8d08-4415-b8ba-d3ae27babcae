import { watch } from 'vue';
import { resize<PERSON>hange } from '@/store';

import * as echarts from 'echarts/core';
import {
	Pie<PERSON><PERSON>,
	PieSeriesOption,
	TreemapChart,
	TreeSeriesOption,
	BarChart,
	BarSeriesOption,
	LineChart,
	LineSeriesOption,
	Heatmap<PERSON>hart,
	Heatmap<PERSON>eriesOption,
	GraphChart,
	GraphSeriesOption,
} from 'echarts/charts';
import {
	DataZoomComponent,
	DataZoomComponentOption,
	TitleComponent,
	TitleComponentOption,
	TooltipComponent,
	TooltipComponentOption,
	LegendComponent,
	LegendComponentOption,
	DatasetComponent,
	GridComponent,
	GridComponentOption,
	Mark<PERSON><PERSON>Component,
	MarkLineComponentOption,
	VisualMapComponent,
	VisualMapComponentOption,
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
echarts.use([
	Pie<PERSON><PERSON>,
	<PERSON>map<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>omponent,
	<PERSON>set<PERSON>omponent,
	TitleComponent,
	<PERSON>vas<PERSON><PERSON><PERSON>,
	Toolt<PERSON><PERSON><PERSON>ponent,
	Legend<PERSON>omponent,
	MarkL<PERSON><PERSON>omponent,
	VisualMap<PERSON>omponent,
	<PERSON><PERSON>hart,
	Heatmap<PERSON>hart,
	DataZoomComponent,
]);

export type ECOption = echarts.ComposeOption<
	| PieSeriesOption
	| TreeSeriesOption
	| TitleComponentOption
	| GridComponentOption
	| TooltipComponentOption
	| LegendComponentOption
	| BarSeriesOption
	| MarkLineComponentOption
	| VisualMapComponentOption
	| LineSeriesOption
	| HeatmapSeriesOption
	| DataZoomComponentOption
	| GraphSeriesOption
>;
export type ECharts = echarts.ECharts;
export { echarts };

type Init = typeof echarts.init;
const init: Init = (el, theme, opts) => {
	let dom = el;
	if (typeof el === 'string') {
		dom = document.getElementById(el)!;
	}
	const myChart = echarts.init(dom, theme, opts);
	watch(resizeChange, () => {
		myChart.resize();
	});
	setTimeout(() => {
		myChart.resize();
	}, 100);
	return myChart;
};
export default init;
