<template>
	<div class="edit-form">
		<div class="main">
			<el-descriptions
				class="form-view custom-example-descriptions"
				:column="1"
				direction="horizontal"
			>
				<el-descriptions-item label="所属项目">示例项目名称文字</el-descriptions-item>
				<el-descriptions-item label="所属项目类型">所属项目类型</el-descriptions-item>
				<el-descriptions-item label="创建时间"> 2023-03-04 </el-descriptions-item>
				<el-descriptions-item label="名称">张三</el-descriptions-item>
				<el-descriptions-item label="手机号">15586368427</el-descriptions-item>
				<el-descriptions-item label="创建时间"> 2023-03-04 </el-descriptions-item>
				<el-descriptions-item label="地址">
					河南省郑州市高新区，河南省郑州市高新区，河南省郑州市高新区，河南省郑州市高新区，河南省郑州市高新区河南省郑州市高新区，河南省郑州市高新区，河南省郑州市高新区，河南省郑州市高新区，河南省郑州市高新区
				</el-descriptions-item>
				<el-descriptions-item label="超长描述备注描述备注">
					河南省郑州市高新区，河南省郑州市高新区，河南省郑州市高新区，河南省郑州市高新区，河南省郑州市高新区
				</el-descriptions-item>
			</el-descriptions>
		</div>
		<div class="footer">
			<div class="footer-view">
				<el-button>取消</el-button>
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
import { reactive, ref } from 'vue';
import type { ComponentSize, FormInstance, FormRules } from 'element-plus';
</script>
<style lang="scss" scoped>
.edit-form {
	display: flex;
	flex-direction: column;
	height: 100%;
	.main {
		flex: 1;
		min-height: 0;
		overflow: auto;

		.form-view {
			width: 777px;
			margin: 20px auto;
		}
	}
	.footer {
		border-top: 1px solid #e5e6eb;
		height: 64px;
		display: flex;
		align-items: center;
		.footer-view {
			width: 777px;
			margin: 0 auto;
		}
	}
}
</style>
<style lang="scss">
.custom-example-descriptions {
	.el-descriptions__label {
		width: 120px;
	}
}
</style>
