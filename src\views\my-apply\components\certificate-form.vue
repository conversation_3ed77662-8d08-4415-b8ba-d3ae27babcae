<template>
    <header-main :viewTitle="showTitle" @close="close" :showCloseBtn="true">
        <template #main>
            <div class="edit-form">
                <div class="main">
                    <el-form ref="formRef" :model="ruleForm" :rules="rules" class="form-view" label-width="108px"
                        label-position="left">
                        <el-form-item label="申请单位" prop="companyNumber">
                            <el-select v-model="ruleForm.companyNumber" placeholder="请选择申请单位"
                                @change="unitChangeAction">
                                <el-option v-for="item in companyList" :key="item.uuid" :label="item.name"
                                    :value="item.uuid" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="申请部门" prop="deptNumber">
                            <el-select ref="deptRef" v-model="ruleForm.deptNumber" placeholder="请选择申请部门"
                                :loading="deptLoading" @change="deptChangeAction"
                                @click.native.prevent="handleDeptClick">
                                <el-option v-for="item in deptList" :key="item.uuid" :label="item.name"
                                    :value="item.uuid" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="使用形式" prop="zzTypeNumber">
                            <el-radio-group v-model="ruleForm.zzTypeNumber">
                                <el-radio value="0">证照使用</el-radio>
                                <el-radio value="1">证照外出</el-radio>
                                <el-radio value="2">其它</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="使/借用时间" prop="fullTime">
                            <div class="demo-datetime-picker">
                                <div class="block">
                                    <el-date-picker v-model="ruleForm.fullTime" type="datetimerange"
                                        start-placeholder="请选择开始时间" end-placeholder="请选择开始时间" format="YYYY-MM-DD HH:mm"
                                        date-format="YYYY/MM/DD" time-format="hh:mm" @change="timeChange" />
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item label="证件名称" prop="title">
                            <el-input v-model="ruleForm.title" placeholder="请输入证件名称" clearable />
                        </el-form-item>
                        <el-form-item label="份数" prop="zjNumber">
                            <el-input-number v-model="ruleForm.zjNumber" label="请输入份数" :min="1" :max="9999"
                                controls-position="right" :step="1" style="width: 70%;" />
                        </el-form-item>
                        <el-form-item label="用途及保送单位" prop="projectName">
                            <el-input rows="4" resize="vertical" maxlength="80" show-word-limit
                                v-model="ruleForm.projectName" placeholder="请输入" type="textarea" />
                        </el-form-item>
                        <el-form-item label="附件" prop="fileList">
                            <el-upload v-model:file-list="fileList" class="upload-file"
                                action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15" multiple
                                :on-preview="handlePreview" :on-remove="handleRemove" :before-remove="beforeRemove"
                                :limit="3" accept="pdf、jpg、png、gif、doc、docx、xls、xlsx">
                                <div class="xirui-flex-row-center">
                                    <el-button>上传文件</el-button>
                                    <div class="file-tip">支持扩展名：pdf、jpg、png、gif、doc、docx、xls、xlsx</div>
                                </div>
                                <!-- <template #tip>
							<div class="el-upload__tip">jpg/png files with a size less than 500KB.</div>
						</template> -->
                            </el-upload>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="footer">
                    <div class="footer-view">
                        <el-button type="primary" :loading="submitLoading" @click="submitAction">提交</el-button>
                        <template v-if="isEdit">
                            <el-button :loading="saveLoading" @click="saveAction(false)">保存</el-button>
                            <el-button type="danger" plain :loading="deleteLoading" @click="deleteAction">删除</el-button>
                        </template>
                        <template v-else>
                            <el-button :loading="saveLoading" @click="saveAction(false)">保存草稿</el-button>
                            <el-button @click="cancelAction">取消</el-button>
                        </template>
                    </div>
                </div>
            </div>
        </template>
    </header-main>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, getCurrentInstance, watch } from 'vue';
import type { ComponentSize, FormInstance, FormRules, UploadProps, UploadUserFile } from 'element-plus';
import { api_getUnitsList, api_getDepartsList, api_postAddSave, api_postEditSave, api_postAddOrEditSubmit, api_getApprovalDetail, api_postApprovalDelete } from '../api';
import { ResponseUnitInfo, ResponseDepartInfo } from '../api/types';

interface Props {
    title: string; // 标题
    type: string; // 类型  add:新增 edit:编辑
    rowID: string; // 表单id
}
const props = withDefaults(defineProps<Props>(), {
    title: '证件使用',
    type: 'add',
    rowID: '', // 表单id
});

const emit = defineEmits<{
    (e: 'close'): void;
    (e: 'success'): void;
}>();

// 监听 type 和 rowID 变化，自动请求详情
watch(
    () => [props.type, props.rowID], // 监听的依赖数组
    ([newType, newId]) => {
        if (newType === 'edit' && newId) {
            // 当类型为编辑且 id 存在时请求详情
            getDetail(newId)
        } else {
            // 非编辑状态时清空详情数据
        }
    },
    { immediate: true } // 组件初始化时立即执行一次
)

// 计算属性
const isEdit = computed(() => props.type === 'edit');
const showTitle = computed(() => {
    let tempTitle = isEdit.value ? '修改' : '增加';
    return tempTitle + props.title;
});

interface RuleForm {
    extendField1: string;
    title: string;
    bDescription: string;
    companyNumber: string;
    companyName: string;
    deptNumber: string;
    deptName: string;
    extendField2: string;
    projectName: string; // 报送单位
    phone: string;
    // fileList: any[];
    extra_files: any[];
    cNumber: string;
    bFormFlag: number;
    rowID: string;
    subProjectID: string;
    billDetails: any[];
    zzTypeNumber: string; // 使用形式
    // zzTypeName: string; // 使用形式名称
    fullTime: string[];
    zjNumber: number;

}
const formRef = ref<FormInstance>();
const ruleForm = reactive<RuleForm>({
    extendField1: "",
    title: "",
    bDescription: "",
    companyNumber: "",
    companyName: "",
    deptNumber: "",
    deptName: "",
    extendField2: "",
    projectName: "", // 报送单位
    phone: "",
    // fileList: [],
    extra_files: [],
    cNumber: "CB024",
    bFormFlag: 24,
    rowID: "",
    subProjectID: "",
    billDetails: [],
    zzTypeNumber: "", // 使用形式
    // zzTypeName: "", // 使用形式名称
    fullTime: ["", ""], // 时间范围
    zjNumber: 1, // 份数
});
const rules = reactive<FormRules<RuleForm>>({
    companyNumber: [
        {
            required: true,
            message: '请选择申请单位',
            trigger: 'change',
        },
    ],
    deptNumber: [
        {
            required: true,
            message: '请选择申请部门',
            trigger: 'change',
        },
    ],
    zzTypeNumber: [
        { required: true, message: '请选择使用方式', trigger: 'change' }
    ],
    fullTime: [
        // 1. 必选校验：确保选择了完整的时间范围
        {
            required: true,
            message: '请选择时间范围',
            trigger: 'change'
        },
        // 2. 自定义校验：开始时间不能晚于结束时间
        {
            validator: (rule, value, callback) => {
                // value 是 [startTime, endTime] 数组

                if (value && value.length === 2) {
                    const tempStartTime = new Date(value[0])
                    const tempEndTime = new Date(value[1])

                    if (value[0] === '') {
                        callback(new Error('请选择时间范围'))
                    } else if (tempStartTime >= tempEndTime) {
                        callback(new Error('开始时间不能晚于结束时间'))
                    } else {
                        callback() // 校验通过
                    }
                } else {
                    callback() // 空值时不校验（由 required 规则处理）
                }
            },
            trigger: 'change'
        },
    ],
    title: [
        {
            required: true,
            message: '请输入证件名称',
            trigger: 'blur',
        },
    ],
    zjNumber: [
        {
            required: true,
            message: '请选择份数',
            trigger: 'blur',
        },
    ],
    projectName: [
        {
            required: true,
            message: '请输入',
            trigger: 'blur',
        },
    ],
});

const companyList = ref<ResponseUnitInfo[]>([]); // 单位列表
const deptList = ref<ResponseDepartInfo[]>([]); // 部门列表
const deptRef = ref(null);

const deptLoading = ref(false); // 部门列表加载状态
const saveLoading = ref(false); // 保存按钮加载状态
const submitLoading = ref(false); // 提交按钮加载状态
const deleteLoading = ref(false); // 删除按钮加载状态

// const fullTime = ref([]); // [开始时间,结束时间]
const startTime = ref(''); // 开始时间
const endTime = ref(''); // 结束时间
// 获取组件实例
const instance = getCurrentInstance()
// 通过实例访问全局的 $moment
const $moment = instance.appContext.config.globalProperties.$moment;
// const nowTime = ref($moment().format("HH:mm:ss"));
// const zzTypeNumber = ref(''); // 使用形式
// // const zzTypeName = ref(''); // 使用形式名称
// const zjNumber = ref(1); // 份数


// 单位切换
function unitChangeAction(newValue: any) {
    // 根据新value匹配对应的选项对象
    const selectedOption = companyList.value.find((item) => item.uuid === newValue) || {};
    ruleForm.companyName = selectedOption.name || '';
    // 重置部门数据
    ruleForm.deptNumber = '';
    ruleForm.deptName = '';
    // 获取部门数据
    getDepartsList(ruleForm.companyNumber);
}
// 部门切换
function deptChangeAction(newValue: any) {
    const selectedOption = deptList.value.find((item) => item.uuid === newValue) || {};
    ruleForm.deptName = selectedOption.name || '';
}

// 时间切换
function timeChange(timeRange: any) {
    console.log('日期切换', timeRange);
    if (timeRange) {
        // 解构数组：[开始时间, 结束时间]
        const [start, end] = timeRange
        startTime.value = $moment(start).format("YYYY-MM-DD HH:mm:ss")
        endTime.value = $moment(end).format("YYYY-MM-DD HH:mm:ss")
    } else {
        // 清空选择时重置
        startTime.value = ''
        endTime.value = ''
    }
    console.log('1日期切换', startTime.value);
    console.log('2日期切换', endTime.value);
}

const resetForm = () => {
    formRef.value?.resetFields();
    Object.assign(ruleForm, {
        extendField1: "",
        title: "",
        bDescription: "",
        companyNumber: "",
        companyName: "",
        deptNumber: "",
        deptName: "",
        extendField2: "",
        projectName: "", // 报送单位
        phone: "",
        // fileList: [],
        extra_files: [],
        cNumber: "CB024",
        bFormFlag: 24,
        rowID: "",
        subProjectID: "",
        billDetails: [],
        zzTypeNumber: "", // 使用形式
        // zzTypeName: "", // 使用形式名称
        fullTime: ["", ""], // 时间范围
        zjNumber: 1, // 份数
    });
};

// 保存/保存草稿
function saveAction(isSubmit = false) {
    formRef.value?.validate((valid) => {
        if (valid) {
            // 校验通过，执行提交逻辑
            console.log('提交的数据:', ruleForm);
            saveForm(isSubmit); // 提交时，先保存，再提交 
        } else {
            // 校验失败
            ElMessage.error('表单校验失败，请检查输入');
            console.log('提交的数据:', ruleForm);
        }
    });
}
// 提交
function submitAction() {
    saveAction(true);
}

// 删除
function deleteAction() {
    ElMessageBox.confirm('确定要删除吗？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
    })
        .then(() => {
            deleteForm();
        })
        .catch(() => {
        })
}
// 取消
function cancelAction() {
    close();
}

// 关闭页面
function close() {
    resetForm();
    emit('close');
}

// 操作成功：保存/保存草稿/提交/删除
function optionSuccess() {
    // ElMessage.success('操作成功');
    emit('success');
}

function handleDeptClick() {
    // 判断其他变量是否有值
    if (!ruleForm.companyNumber) {
        // 无值：提示并不展开
        ElMessage.warning('请先选择申请单位');
        return;
    }

    // 有值：手动触发下拉框展开
    // el-select 内置方法 togglePopup() 可切换展开/收起状态
    if (deptRef.value) {
        deptRef.value.togglePopup();
    }
}



const fileList = ref<UploadUserFile[]>([
    // {
    // 	name: 'element-plus-logo.svg',
    // 	url: 'https://element-plus.org/images/element-plus-logo.svg',
    // },
    // {
    // 	name: 'element-plus-logo2.svg',
    // 	url: 'https://element-plus.org/images/element-plus-logo.svg',
    // },
]);

const handleRemove: UploadProps['onRemove'] = (file, uploadFiles) => {
    console.log(file, uploadFiles);
};

const handlePreview: UploadProps['onPreview'] = (uploadFile) => {
    console.log(uploadFile);
};

const handleExceed: UploadProps['onExceed'] = (files, uploadFiles) => {
    ElMessage.warning(
        `The limit is 3, you selected ${files.length} files this time, add up to ${files.length + uploadFiles.length} totally`,
    );
};

const beforeRemove: UploadProps['beforeRemove'] = (uploadFile, uploadFiles) => {
    return ElMessageBox.confirm(`Cancel the transfer of ${uploadFile.name} ?`).then(
        () => true,
        () => false,
    );
};

onMounted(() => {
    getUnitsList();
});

// 获取详情
function getDetail(rowID: string) {
    api_getApprovalDetail(rowID).then((res) => {
        console.log('获取详情成功', res);
        // companyList.value = res;
        const data = res[0];
        // ruleForm.rowID = rowID;
        // ruleForm.extendField1 = data.extendField1;
        // ruleForm.title = data.title;
        // ruleForm.extendField2 = data.extendField2;
        // ruleForm.phone = data.phone;
        // ruleForm.bDescription = data.bDescription;
        // ruleForm.extra_files = data.extra_files;
        // ruleForm.companyName = data.companyName;
        // ruleForm.companyNumber = data.companyNumber;
        // ruleForm.deptNumber = data.deptNumber;
        // ruleForm.deptName = data.deptName;
        // getDepartsList(ruleForm.companyNumber);
        // createName = data.creatorName
        ruleForm.rowID = rowID;
        ruleForm.title = data.title;
        ruleForm.extendField2 = data.extendField2;
        ruleForm.extra_files = data.extra_files;
        ruleForm.projectName = data.projectName;
        // createName = data.creatorName;
        // this.startTime = this.$moment(data.extendField2.split("|")[0]).format(
        //     "YYYY-MM-DD"
        // );
        // this.endTime = this.$moment(data.extendField2.split("|")[1]).format(
        //     "YYYY-MM-DD"
        // );
        startTime.value = $moment(data.extendField2.split("|")[0]).format("YYYY-MM-DD HH:mm:ss")
        endTime.value = $moment(data.extendField2.split("|")[1]).format("YYYY-MM-DD HH:mm:ss")
        ruleForm.fullTime = [startTime.value, endTime.value];
        ruleForm.zzTypeNumber = data.extendField2.split("|")[2];
        // ruleForm.zzTypeName = ruleForm.zzType.filter(
        //     (f) => f.value == ruleForm.zzTypeNumber
        // )[0].label;
        ruleForm.zjNumber = parseInt(data.extendField2.split("|")[3]);
        ruleForm.companyName = data.companyName;
        ruleForm.companyNumber = data.companyNumber;
        ruleForm.deptNumber = data.deptNumber;
        ruleForm.deptName = data.deptName;
        getDepartsList(ruleForm.companyNumber);
    });
}

// 获取单位列表
function getUnitsList() {
    api_getUnitsList().then((res) => {
        companyList.value = res;
    });
}

// 获取部门列表
function getDepartsList(unitId = '') {
    deptLoading.value = true;
    api_getDepartsList(unitId).then((res) => {
        deptList.value = res;
    }).finally(() => {
        deptLoading.value = false;
    });
}

// 保存/保存草稿表单
function saveForm(isSubmit = false) {
    if (!isSubmit) {
        saveLoading.value = true;
    }
    let url = api_postAddSave;
    if (isEdit.value) {
        url = api_postEditSave;
    }
    // ruleForm.extendField2 = `${startTime.value} ${nowTime.value}|${endTime.value} ${nowTime.value}|${zzTypeNumber.value}|${zjNumber.value}`;
    ruleForm.extendField2 = `${startTime.value}|${endTime.value}|${ruleForm.zzTypeNumber}|${ruleForm.zjNumber}`;
    url(ruleForm, !isSubmit).then((res: any) => {
        console.log('保存成功:', res);
        if (isSubmit) {
            // 提交
            ruleForm.rowID = res[0].rowID;
            submitForm();
        } else {
            // 保存成功
            // ElMessage.success('保存成功');
            optionSuccess();
        }

    }).finally(() => {
        saveLoading.value = false;
    });
}

// 提交表单
function submitForm() {
    submitLoading.value = true;
    api_postAddOrEditSubmit(ruleForm.rowID).then((res: any) => {
        optionSuccess();
    }).finally(() => {
        submitLoading.value = false;
    });
}
// 删除表单
function deleteForm() {
    deleteLoading.value = true;
    api_postApprovalDelete(ruleForm.rowID).then((res: any) => {
        optionSuccess();
    }).finally(() => {
        deleteLoading.value = false;
    });
}
</script>
<style lang="scss" scoped>
.edit-form {
    display: flex;
    flex-direction: column;
    height: 100%;

    .main {
        flex: 1;
        min-height: 0;
        overflow: auto;

        .form-view {
            width: 777px;
            margin: 20px auto;
            padding-right: 150px;
        }

        .upload-file {
            width: 100%;

            .file-tip {
                margin-left: 15px;
                font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
                font-weight: 400;
                font-size: 14px;
                color: #86909c;
                line-height: 14px;
                text-align: left;
                font-style: normal;
                text-transform: none;
            }
        }
    }

    .footer {
        border-top: 1px solid #e5e6eb;
        height: 64px;
        display: flex;
        align-items: center;

        .footer-view {
            width: 777px;
            margin: 0 auto;
        }
    }
}

.demo-datetime-picker {
    display: flex;
    width: 100%;
    padding: 0;
    flex-wrap: wrap;
    justify-content: space-around;
    align-items: stretch;
}

.demo-datetime-picker .block {
    flex: 1;
    text-align: center;
}

.line {
    width: 1px;
    background-color: var(--el-border-color);
}
</style>
