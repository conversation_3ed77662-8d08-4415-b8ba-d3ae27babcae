<!--
  功能：上传下载组件示例页面
  作者：ligw
  时间：2024年08月09日 10:53:31
  版本：v1.0
  修改记录：
  修改内容：
  修改人员：
  修改时间：
-->
<template>
  <div class="container">
		<h1>上传下载</h1>
		<h3>excel单文件导入 - excel-upload-single</h3>
		<div class="part">
			<ExcelUploadSingle  title="导入任务" 
      action="sas"
      :data="{id:1}" @complete="handleExcelComplete" >
      <span>下拉框</span>
    </ExcelUploadSingle>
      <div class="note">
        说明：<br>
        1、title(导入): 支持按钮名称自定义<br>
        2、action：支持导入接口自定义<br>
        3、complete：支持上传成功事件外发,用于导入完成后业务实现，如刷新表格数据<br>
        4、successMessage（上传成功！）：支持上传成功提示语自定义<br>
        5、slot：支持自定义触发器<br>
        6、其他配置项同el-upload
      </div>
		</div>
		<h3>单文件导出 - file-export</h3>
		<div class="part">
			<FileExport  title="导出任务" :exportFun="handleExport" />
      <div class="note">
        说明：<br>
        1、title(导出): 支持按钮名称自定义<br>
        2、exportFun（(callback)=>{callback(prams)}）：支持外部导出逻辑实现,并接收文件流、文件名称等进行下载。<br>
        3、successMessage（导出成功！）：支持导出成功提示语自定义<br>
        4、也可自行实现导出组件，完成业务后调用@/utils/file.ts中downloadByData方法进行文件下载
      </div>
		</div>
    <ElForm :model="formData" :rules="rules">
      
   
    <h3>图片上传 - image-upload</h3>
		<div class="part">
      <ElFormItem prop="images" label="图片">
        <ImageUpload v-model="formData.images"  />
      </ElFormItem>
      <div class="note">
        说明：<br>
        1、accept(.jpg,.png,.jpeg,.gif,.bmp): 支持图片格式限制<br>
        2、limit(1): 支持图片数量限制。<br>
        3、size：支持图片大小限制<br>
        注：model数据需做对应处理为FileItem[]格式,提交时对model数据做业务处理
      </div>
		</div>
    <h3>视频上传 - video-upload</h3>
		<div class="part">
      <ElFormItem prop="video" label="视频">
        <VideoUpload  v-model="formData.video" />
      </ElFormItem>
      <div class="note">
        说明：<br>
        1、accept(.mp4,.ogg,.webm): 支持视频格式限制<br>
        2、limit(1): 支持图片数量限制。<br>
        3、size：支持图片大小限制<br>
        注：model数据需做对应处理为FileItem[]格式,提交时对model数据做业务处理
      </div>
		</div>
    <h3>文件上传 - file-upload</h3>
		<div class="part">
      <ElFormItem prop="file" label="文件" >
        <FileUpload  v-model="formData.file" style="width: 500px;" 
        accept=".docx"
        @delete="handleDelete"></FileUpload>
      </ElFormItem>
      <div class="note">
        说明：<br>
        1、accept: 支持文件格式限制<br>
        2、limit(1): 支持文件数量限制。<br>
        3、size：支持单个文件大小限制<br>
        注：model数据需做对应处理为FileItem[]格式,提交时对model数据做业务处理
      </div>
		</div>
    <h3>批量导入 - batch-import</h3>
		<div class="part">
      <ElFormItem prop="file" label="文件" >
        <BatchImport :uploadFun="handleUpload" :size="200" :exportFun="handleExport" ></BatchImport>
      </ElFormItem>
      <ExcelUploadCommon :uploadFun="handleUpload" :exportFun="handleExport"/>
      <div class="note">
        说明：支持模板下载，单文件、多文件批量上传；组件分为弹窗导入-ExcelUploadCommon，
        导入组件-BatchImport, 以及导入结果组件ImportResult<br>
        1、title: 支持按钮触发器文案自定义<br>
        2、dialogTitle: 支持导入弹窗标题文案自定义。<br>
        3、size：支持单个文件大小限制<br>
        4、multiple（false）：支持单个文件/多个文件上传切换<br>
        5、uploadFun: (files: FormData) => XMLHttpRequest | Promise(ImportResultResponse)：上传实现;<br>  
        6、exportFun: (prams:Function)=>void ：模板导出实现;
      </div>
		</div>
    <h3>二维码 - vue-qrcode</h3>
		<div class="part">
      <VueQrcode text="sss" />
      <div class="note">
        说明：
      </div>
		</div>
  </ElForm>
	</div>
</template>
<script lang='ts' setup>
import { ref } from 'vue';
import ExcelUploadSingle from '@/components/upload/excel-upload-single.vue';
import FileExport from '@/components/upload/file-export.vue';
import ImageUpload from '@/components/upload/image-upload.vue';
import VideoUpload from '@/components/upload/video-upload.vue'
import type { FileItem } from '@/components/upload/image-upload.vue';
import { ElMessage } from 'element-plus';
const formData = ref<any>({
  images: [{
    "fileAccessPath": "/file/0/66bb373a1e9d980007a24869.jpg",
    "fileCreateTime": "2024-08-13 18:36:42",
    "fileName": "logo",
    "fileSize": 3791,
    "fileStoragePath": "/file/0/66bb373a1e9d980007a24869.jpg",
    "fileType": "image/jpg",
    "fileUuid": "66bb373a1e9d980007a24869",
    uid: 2
  }],
  video: [{
    "fileAccessPath": "/file/0/66bb2a851e9d980007a24858.mp4",
    "fileCreateTime": "2024-08-13 17:42:29",
    "fileName": "flow_in_the_sky",
    "fileSize": 1209069,
    "fileStoragePath": "/file/0/66bb2a851e9d980007a24858.mp4",
    "fileType": "video/mp4",
    "fileUuid": "66bb2a851e9d980007a24858",
    uid: 1
  },
  {
    "fileAccessPath": "/file/0/66bb373a1e9d980007a24869.jpg",
    "fileCreateTime": "2024-08-13 18:36:42",
    "fileName": "logo",
    "fileSize": 3791,
    "fileStoragePath": "/file/0/66bb373a1e9d980007a24869.jpg",
    "fileType": "image/jpg",
    "fileUuid": "66bb373a1e9d980007a24869",
    uid: 2
    }],
  file: [{
    "fileAccessPath": "/file/0/66bb2a851e9d980007a24858.mp4",
    "fileCreateTime": "2024-08-13 17:42:29",
    "fileName": "flow_in_the_sky",
    "fileSize": 1209069,
    "fileStoragePath": "/file/0/66bb2a851e9d980007a24858.mp4",
    "fileType": "video/mp4",
    "fileUuid": "66bb2a851e9d980007a24858",
    uid: 1
  },
  {
    "fileAccessPath": "/file/0/66bb373a1e9d980007a24869.jpg",
    "fileCreateTime": "2024-08-13 18:36:42",
    "fileName": "logo",
    "fileSize": 3791,
    "fileStoragePath": "/file/0/66bb373a1e9d980007a24869.jpg",
    "fileType": "image/jpg",
    "fileUuid": "66bb373a1e9d980007a24869",
    uid: 2
    }]
})
const rules = ref<any>({
	images: [{ required: true, trigger: ['blur','change'], message: '请选择图片' }],
	video: [{ required: true, trigger: ['blur','change'], message: '请选择视频' }],
})
const handleExcelComplete = (res:any) => {
  console.log(res,'上传成功，刷新表格数据。');
  
}
const handleDelete = (res:any) => {
  console.log(res,'res-delete');
  
}
const handleExport = (callback:Function) => {
  // 导出逻辑-调用后台接口返回文件流
  //接口调用完毕，回调进行文件下载
  callback(new Blob(['ssss']),'测试文件.xlsx')
}

const handleUpload = (file: FormData) => {
  // TODO 上传业务逻辑
  return new Promise<ImportResultResponse>((resolve, reject) => {
    resolve({
      code: 0,
      data: [{
        totalCount: 100,
        failedCount: 10,
        failedResultList:[{no: 1, errorMessage: '字段A不能为空'},{no: 1, errorMessage: '字段B未匹配'}]
      }],
      message: ''
    })
  })
}
</script>
<style scoped lang='scss'>
.container{
  width: 100%;
  height: 100%;
  padding: 0 20px;
  overflow: auto;
  .part{
    display: flex;
    >.note{
      margin-left: 20px;
    }
  }
}
</style>
