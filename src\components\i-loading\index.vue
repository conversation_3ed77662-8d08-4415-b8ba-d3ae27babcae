// 使用全局的样式 在 index.html 中
<template>
	<div class="g-loading-box" ref="refBox" style="display: none">
		<div class="g-loading-dot-spin">
			<i class="g-loading-dot-item"></i>
			<i class="g-loading-dot-item"></i>
			<i class="g-loading-dot-item"></i>
			<i class="g-loading-dot-item"></i>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';

interface Props {
	loading: boolean;
}
const props = withDefaults(defineProps<Props>(), {});
const refBox = ref<HTMLDivElement>();
onMounted(() => {
	watch(
		() => props.loading,
		(val) => {
			const rootDom = refBox.value;
			if (!rootDom) return;
			if (val) {
				// 先显示遮罩，防止用户重复点击，延迟显示背景，避免闪屏问题
				rootDom.style.opacity = '0';
				rootDom.style.display = 'block';
				setTimeout(() => {
					rootDom.style.opacity = '1';
				}, 10);
			} else {
				rootDom.style.display = 'none';
			}
		},
		{ immediate: true },
	);
});
</script>
