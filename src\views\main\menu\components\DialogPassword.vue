<template>
	<el-dialog v-model="modelValue_" title="修改密码" append-to-body align-center destroy-on-close width="600px">
		<el-form ref="refForm" class="t-form-box g-form-box" :model="formState" label-position="top" :rules="rules">
			<el-form-item label="原密码" prop="oldPassword">
				<el-input v-model="formState.oldPassword" type="password" show-password maxlength="50" placeholder="请输入" />
			</el-form-item>
			<el-form-item label="新密码" prop="newPassword">
				<el-input v-model="formState.newPassword" type="password" show-password maxlength="50" placeholder="请输入" />
				<div class="t-text">密码必须包含 小写字母+数字 ，最少6位</div>
			</el-form-item>
			<el-form-item label="确认新密码" prop="confirmPassword">
				<el-input v-model="formState.confirmPassword" type="password" show-password maxlength="50" placeholder="请输入" />
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button @click="modelValue_ = false">关闭</el-button>
				<el-button type="primary" @click="onSave">保存</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { api_changePassword } from '@/api/user';

interface Props {
	modelValue: boolean;
}
const props = withDefaults(defineProps<Props>(), {});
const emit = defineEmits<{
	(ev: 'update:modelValue', value: boolean): void;
}>();

const modelValue_ = computed({
	get() {
		return props.modelValue;
	},
	set(value) {
		emit('update:modelValue', value);
	},
});
const refForm = ref<ElFormInstance>();

const defaultForm: RequestPassword = {
	oldPassword: '',
	newPassword: '',
	confirmPassword: '',
};
const formState = ref(cloneDeep(defaultForm));
const rules: FormRules<RequestPassword> = {
	oldPassword: {
		required: true,
		validator(rule: any, value: any, callback: Function) {
			console.log('rule_true', value);
			const errText = validatorPw(value);
			if (errText) {
				callback(new Error(errText));
			} else {
				callback();
			}
		},
		trigger: ['change', 'blur'],
	},
	newPassword: {
		required: true,
		validator(rule: any, value: any, callback: Function) {
			console.log('rule_true', value);
			const errText = validatorPw(value);
			if (errText) {
				callback(new Error(errText));
			} else if (formState.value.confirmPassword && value !== formState.value.confirmPassword) {
				callback(new Error('新密码与确认密码不同'));
			} else {
				callback();
			}
		},
		trigger: ['change', 'blur'],
	},
	confirmPassword: {
		required: true,
		validator(rule: any, value: any, callback: Function) {
			console.log('rule_true', value);
			const errText = validatorPw(value);
			if (errText) {
				callback(new Error(errText));
			} else if (value !== formState.value.newPassword) {
				callback(new Error('确认密码与新密码不同'));
			} else {
				callback();
			}
		},
		trigger: ['change', 'blur'],
	},
};
function validatorPw(value) {
	if (!value) {
		return '不能为空';
	} else if (!isCellLowercase(value)) {
		return '必须包含小写字母';
	} else if (!isCellNumber(value)) {
		return '必须包含数字';
	}
	return '';
}
// 判断是否包含大写字母
function isCellUppercase(value) {
	if (!/^(?=.*[A-Z])/.test(value)) {
		return false;
	} else {
		return true;
	}
}

// 判断是否包含小写字母
function isCellLowercase(value) {
	if (!/^(?=.*[a-z])/.test(value)) {
		return false;
	} else {
		return true;
	}
}

// 判断是否包含数字
function isCellNumber(value) {
	if (!/^(?=.*[0-9])/.test(value)) {
		return false;
	} else {
		return true;
	}
}
// 判断是否包含特殊字符
function isCellSpecial(value) {
	if (!/^(?=.*[^a-zA-Z0-9])/.test(value)) {
		return false;
	} else {
		return true;
	}
}
function onSave() {
	refForm.value
		?.validate()
		.then(() => {
			return api_changePassword(formState.value);
		})
		.then(() => {
			modelValue_.value = false;
		});
}
</script>
<style lang="scss" scoped>
.t-form-box {
	margin: 20px;
	.t-text {
		font-size: 12px;
		color: var(--el-color-info-light-3);
	}
}
</style>
