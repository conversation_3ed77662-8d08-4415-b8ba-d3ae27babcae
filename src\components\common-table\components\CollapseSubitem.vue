<template>
	<el-select class="collapse-subitem-select xirui-custom-select" v-model="value" placeholder="请选择">
		<template #label="{ label, value }">
			<IconFont type="icon-filter-line" />
			<span>{{ label }}</span>
		</template>
		<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
	</el-select>
</template>

<script lang="tsx" setup>
import { ref } from 'vue';
const value = ref<string>('hide');
const options = [
	{
		value: 'hide',
		label: '折叠全部',
	},
	{
		value: 'show',
		label: '展示全部',
	}
];
</script>
<style lang="scss" scoped>
.collapse-subitem-select {
  width: 116px;
}
</style>
