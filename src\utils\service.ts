import axios, { type AxiosInstance, type AxiosRequestConfig } from 'axios';
import qs from 'qs';
import { ElMessage } from 'element-plus';
import { get, merge } from 'lodash-es';
import { envConfig } from '@/config';
import { getLocalToken } from '@/utils/token';
import { sendTokenInvalidMsg, sendLoginInvalidMsg } from '@/utils/message';

/** 退出登录并强制刷新页面（会重定向到登录页） */
function logout() {
	const url = envConfig.VITE_CAS_AUTH_URL + '?appName=' + envConfig.VITE_APP_NAME;
	window.location.href = url;
}
//扩展config自定义参数
declare module 'axios' {
	interface AxiosRequestConfig {
		form_urlencoded?: boolean; //Content-Type = application/x-www-form-urlencoded
		noToken?: boolean; //接口请求是否携带token
		serviceName?: string; //接口调用时的服务，不传时默认为VITE_BASE_API（服务名称在public文件夹下config.json中配置，并在global.d.ts中的EnvConfig定义中声明类型）
	}
}

/** 创建请求实例 */
function createService() {
	// 创建一个 axios 实例命名为 service
	const service = axios.create();
	// 请求拦截
	service.interceptors.request.use(
		(config) => {
			config.headers['X-Requested-With'] = 'XMLHttpRequest';
			if (Object.prototype.toString.call(config.data) === '[object FormData]') {
				config.headers['Content-Type'] = 'multipart/form-data;charset=UTF-8';
			} else {
				if (config.form_urlencoded) {
					config.headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8';
					config.data = qs.stringify(config.data);
				} else {
					config.headers['Content-Type'] = 'application/json;charset=UTF-8';
					if (config.method === 'post' || config.method === 'put' || config.method === 'delete') {
						config.data = JSON.stringify(config.data);
					} else {
						config.data = qs.stringify(config.data);
					}
				}
			}

			// 接口前添加token
			const token = getLocalToken();
			if (token && !config.noToken) {
				config.headers['Authorization'] = token;
			}
			config.baseURL = config.serviceName ? envConfig[config.serviceName] : envConfig.VITE_BASE_API;

			return config;
		},
		// 发送失败
		(error) => Promise.reject(error),
	);
	// 响应拦截（可根据具体业务作出相应的调整）
	service.interceptors.response.use(
		(response) => {
			// apiData 是 api 返回的数据
			const apiData = response.data;
			// 二进制数据则直接返回
			const responseType = response.request?.responseType;
			if (responseType === 'blob' || responseType === 'arraybuffer') return apiData;
			// 这个 code 是和后端约定的业务 code
			const code = apiData.code;
			// 如果没有 code, 代表这不是项目后端开发的 api
			if (code === undefined) {
				ElMessage.error('服务器开小差！');
				return Promise.reject(apiData);
			}
			switch (code) {
				case 0:
					// 业务正常
					return apiData;
				case 1:
					// 业务失败
					ElMessage.error('服务器开小差！');
					return Promise.reject(apiData);
				case 20200:
					// 用户未登录
					// return logout();
					sendLoginInvalidMsg();
					break;
				case 20300:
					// 无效Token
					// return logout();
					sendTokenInvalidMsg();
					break;
				case 20301:
					// Token过期
					// return logout();
					sendTokenInvalidMsg();
					break;
				case 20302:
					// Token缺失
					// return logout();
					sendTokenInvalidMsg();
					break;
				default:
					// 不是正确的 code
					ElMessage.error(apiData.message || 'Error');
					return Promise.reject(apiData);
			}
		},
		(error) => {
			if (error.code === 'ECONNABORTED') {
				ElMessage.error('请求超时！');
			}
			return Promise.reject(error);
		},
	);
	return service;
}

/** 创建请求方法 */
function createRequest(service: AxiosInstance) {
	return function <T>(config: AxiosRequestConfig): Promise<T> {
		const defaultConfig = {
			timeout: 10000,
			withCredentials: true,
			baseURL: '',
		};
		// 将默认配置 defaultConfig 和传入的自定义配置 config 进行合并成为 mergeConfig
		const mergeConfig = merge(defaultConfig, config);
		return service(mergeConfig);
	};
}

/** 用于网络请求的实例 */
const service = createService();
/** 用于网络请求的方法 */
export const request = createRequest(service);
