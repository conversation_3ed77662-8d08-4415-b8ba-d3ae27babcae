import { defineStore } from 'pinia';
import { RouteRecordRaw, RouteRecordName, RouteParamsRaw, LocationQueryRaw } from 'vue-router';

interface RouteParamsItem {
	fullPath?: string;
	name?: RouteRecordName | null | undefined;
	params?: RouteParamsRaw;
	path?: string;
	query?: LocationQueryRaw;
}
interface RoutesState {
	routes: RouteRecordRaw[]; // 活跃的路由
	routeParams: RouteParamsItem[];//路由参数
	cacheRoutes: RouteRecordRaw[];//缓存的路由
}
export const useStoreActiveRoutes = defineStore({
	id: 'active-routes',
	state: (): RoutesState => {
		return {
			routes: [],
			routeParams: [],
			cacheRoutes: []
		};
	},
	getters: {
		getRouteNames(): string[] {
			return this.routes.map((item) => item.name as string);
		},
		getRoutes(): RouteRecordRaw[] {
			return this.routes;
		},
		getRouteParams(): RouteParamsItem[] {
			return this.routeParams;
		},
		getCacheRouteNames(): string[] {
			return this.cacheRoutes.map((item) => item.name as string);
		},
		getCacheRoutes(): RouteRecordRaw[] {
			return this.cacheRoutes;
		},
	},
	actions: {
		//存储活跃的路由的参数
		setRouteParams(item: RouteParamsItem) {
			if (item.name === 'redirect') {//用于重定向的页面不存储路由参数
				return
			}
			const findIndex = this.routeParams.findIndex((item_) => item_.name === item.name);
			if (findIndex === -1) {
				this.routeParams.push(item);
			} else {
				this.routeParams.splice(findIndex, 1, item)
			}

		},
		//存储活跃的路由和缓存的路由
		setRoute(item: RouteRecordRaw) {
			if (item.name === 'redirect') {//用于重定向的页面不存储展示到标签栏
				return
			}
			const has = this.routes.find((item_) => item_.name === item.name);
			const hasCache = this.cacheRoutes.find((item_) => item_.name === item.name);
			if (!has) {
				this.routes.push(item);
			}
			if (!hasCache && item.meta?.keepAlive) {
				this.cacheRoutes.push(item);
			}
		},
		// 关闭当前
		removeRoute(item: RouteRecordRaw | string) {
			if (typeof item === 'string') {
				const hasCache = this.cacheRoutes.find((item_) => item_.name === item);
				this.routes = this.routes.filter((item_) => item_.name !== item);
				this.routeParams = this.routeParams.filter((item_) => item_.name !== item);
				if (hasCache) {
					this.cacheRoutes = this.cacheRoutes.filter((item_) => item_.name !== item);
				}
			} else {
				const hasCache = this.cacheRoutes.find((item_) => item_.name === item.name);
				this.routes = this.routes.filter((item_) => item_.name !== item.name);
				this.routeParams = this.routeParams.filter((item_) => item_.name !== item.name);
				if (hasCache) {
					this.cacheRoutes = this.cacheRoutes.filter((item_) => item_.name !== item.name);
				}
			}
		},
		// 关闭左侧
		removeLeftRoute(item: RouteRecordRaw | string) {
			let routeIndex = -1
			if (typeof item === 'string') {
				routeIndex = this.routes.findIndex((item_) => item_.name === item);
			} else {
				routeIndex = this.routes.findIndex((item_) => item_.name === item.name);
			}
			this.routeParams.splice(0, routeIndex)
			let delRoutes: RouteRecordRaw[] = this.routes.splice(0, routeIndex)
			let map = new Map();
			delRoutes.forEach(obj => map.set(obj.name, obj));
			this.cacheRoutes.forEach(obj => !map.has(obj.name) && map.set(obj.name, obj));
			this.cacheRoutes = [...map.values()];

		},
		// 关闭右侧
		removeRightRoute(item: RouteRecordRaw | string) {
			let routeIndex = -1
			if (typeof item === 'string') {
				routeIndex = this.routes.findIndex((item_) => item_.name === item);
			} else {
				routeIndex = this.routes.findIndex((item_) => item_.name === item.name);
			}
			this.routeParams.splice(routeIndex + 1)
			let delRoutes: RouteRecordRaw[] = this.routes.splice(routeIndex + 1)
			let map = new Map();
			delRoutes.forEach(obj => map.set(obj.name, obj));
			this.cacheRoutes.forEach(obj => !map.has(obj.name) && map.set(obj.name, obj));
			this.cacheRoutes = [...map.values()];
		},
		// 关闭其它
		removeOther(item: RouteRecordRaw | string) {
			if (typeof item === 'string') {
				const hasCache = this.cacheRoutes.find((item_) => item_.name === item);
				this.routes = this.routes.filter((item_) => item_.name === item);
				this.routeParams = this.routeParams.filter((item_) => item_.name === item);
				if (hasCache) {
					this.cacheRoutes = this.cacheRoutes.filter((item_) => item_.name === item);
				} else {
					this.cacheRoutes = []
				}
			} else {
				const hasCache = this.cacheRoutes.find((item_) => item_.name === item.name);
				this.routes = this.routes.filter((item_) => item_.name === item.name);
				this.routeParams = this.routeParams.filter((item_) => item_.name === item.name);
				if (hasCache) {
					this.cacheRoutes = this.cacheRoutes.filter((item_) => item_.name === item.name);
				} else {
					this.cacheRoutes = []
				}
			}
		},
		//清除指定缓存页面
		clearCacheRoutesItem(item: RouteRecordRaw | string) {
			if (typeof item === 'string') {
				const hasCache = this.cacheRoutes.find((item_) => item_.name === item);
				if (hasCache) {
					this.cacheRoutes = this.cacheRoutes.filter((item_) => item_.name !== item);
				}
			} else {
				const hasCache = this.cacheRoutes.find((item_) => item_.name === item.name);
				if (hasCache) {
					this.cacheRoutes = this.cacheRoutes.filter((item_) => (item_.name !== item.name) || (item_.path !== item.path));
				}
			}
		},
		//清除指定路由参数
		clearRouteParamsItem(item: RouteParamsItem | string) {
			if (typeof item === 'string') {
				const has = this.routeParams.find((item_) => item_.name === item);
				if (has) {
					has.params = {}
					has.query = {}
				}
			} else {
				const has = this.routeParams.find((item_) => item_.name === item.name);
				if (has) {
					has.params = {}
					has.query = {}
				}
			}
		},
		clear() {
			this.routes = [];
		},
		clearCacheRoutes() {
			this.cacheRoutes = []
		},
		clearRouteParams() {
			this.routeParams.forEach((item) => {
				item.params = {}
				item.query = {}
			})
		},
	},
});
