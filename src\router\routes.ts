// 不需要权限就可以访问的路由
import { RouteRecordRaw } from 'vue-router';

export const commonRoutes: RouteRecordRaw[] = [
	{
		path: '/403',
		name: '403',
		component: () => import('@/views/error/403.vue'),
		meta: {
			hidden: true,
			title: '403',
		},
	},
	{
		path: '/404',
		name: '404',
		component: () => import('@/views/error/404.vue'),
		meta: {
			hidden: true,
			title: '404',
		},
	},
	{
		path: '/500',
		name: '500',
		component: () => import('@/views/error/500.vue'),
		meta: {
			hidden: true,
			title: '500',
		},
	},
	{
		path: '/',
		name: 'root',
		component: () => import('@/views/main/index.vue'),
		meta: {
			hidden: true,
			title: '首页',
		},
	},
	{
		path: '/redirect',
		name: 'redirect',
		component: () => import('@/views/Redirect/index.vue'),
		meta: {
			hidden: true,
			title: 'redirect',
		},
	},
];

// main 的子路由，通过当前登录人的权限筛选后添加到 main 的 children中
export const mainRouteList: RouteRecordRaw[] = [
	{
		path: '/normalApproval',
		name: 'normalApproval',
		component: () => import('@/views/main/index.vue'),
		meta: {
			title: '通用审批',
			authCode: '通用审批',
			icon: 'icon-building-blue',
			primaryIcon: 'icon-building-white',
		},
		children: [
			{
				path: '/business-type',
				name: 'business_type',
				component: () => import('@/views/business-type/index.vue'),
				meta: {
					title: '业务类别',
					authCode: '业务类别',
				},
			},
			{
				path: '/my-apply',
				name: 'my_apply',
				component: () => import('@/views/my-apply/index.vue'),
				meta: {
					title: '我的申请',
					authCode: '我的申请',
				},
			},
			{
				path: '/my-approval',
				name: 'my_approval',
				component: () => import('@/views/my-approval/index.vue'),
				meta: {
					title: '我的审批',
					authCode: '我的审批',
				},
			},
		],
	},
	// {
	// 	path: '/apply',
	// 	name: 'apply',
	// 	component: () => import('@/views/main/index.vue'),
	// 	meta: {
	// 		title: '我的申请',
	// 		authCode: 'APPLY',
	// 		icon: 'icon-building-blue',
	// 		primaryIcon: 'icon-building-white',
	// 	},
	// 	children: [
	// 		{
	// 			path: '/my-apply',
	// 			name: 'my_apply',
	// 			component: () => import('@/views/my-apply/index.vue'),
	// 			meta: {
	// 				title: '我的申请',
	// 				authCode: 'MY_APPLY',
	// 			},
	// 		},
	// 	],
	// },
	// {
	// 	path: '/approval',
	// 	name: 'approval',
	// 	component: () => import('@/views/main/index.vue'),
	// 	meta: {
	// 		title: '我的审批',
	// 		authCode: 'APPROVAL',
	// 		icon: 'icon-building-blue',
	// 		primaryIcon: 'icon-building-white',
	// 	},
	// 	children: [
	// 		{
	// 			path: '/my-approval',
	// 			name: 'my_approval',
	// 			component: () => import('@/views/my-approval/index.vue'),
	// 			meta: {
	// 				title: '我的审批',
	// 				authCode: 'MY_APPROVAL',
	// 			},
	// 		},
	// 	],
	// },
	{
		path: '/basic-standards',
		name: 'basic-standards',
		component: () => import('@/views/main/index.vue'),
		meta: {
			title: '基础标准',
			authCode: '基础标准',
			icon: 'icon-building-blue',
			primaryIcon: 'icon-building-white',
			isDev: true,
		},
		children: [
			{
				path: 'dialog',
				name: 'dialog',
				component: () => import('@/views/basic-standards/dialog/index.vue'),
				meta: {
					title: '弹窗规范',
					authCode: '',
				},
			},
			{
				path: 'confirm',
				name: 'confirm',
				component: () => import('@/views/basic-standards/confirm/index.vue'),
				meta: {
					title: '消息提示',
					authCode: '',
				},
			},
			{
				path: 'authority-table-btn',
				name: 'authority-table-btn',
				component: () => import('@/views/basic-standards/authority-table-btn/index.vue'),
				meta: {
					title: '表格按钮权限',
					authCode: '',
				},
			},
			{
				path: 'authority-utils',
				name: 'authority-utils',
				component: () => import('@/views/basic-standards/authority-utils/index.vue'),
				meta: {
					title: '工具类判断权限',
					authCode: '',
				},
			},
			{
				path: 'text',
				name: 'text',
				component: () => import('@/views/basic-standards/text/index.vue'),
				meta: {
					title: '文字规范',
					authCode: '',
				},
			},
			{
				path: 'open-other-app',
				name: 'open-other-app',
				component: () => import('@/views/basic-standards/open-other-app/index.vue'),
				meta: {
					title: '打开其他子应用页面',
					authCode: '',
				},
			},
		],
	},
	{
		path: '/example',
		name: 'example',
		component: () => import('@/views/main/index.vue'),
		meta: {
			title: '页面示例',
			authCode: '页面示例',
			icon: 'icon-building-blue',
			primaryIcon: 'icon-building-white',
			isDev: true,
		},
		children: [
			{
				path: 'manage',
				name: 'manage',
				component: () => import('@/views/example/user-manage/index.vue'),
				meta: {
					title: '用户管理（API示例）',
					authCode: '',
				},
			},
			{
				path: 'list-table',
				name: 'list-table',
				component: () => import('@/views/example/list-table/index.vue'),
				meta: {
					title: '列表-表格',
					authCode: '',
				},
			},
			{
				path: 'tree-table',
				name: 'tree-table',
				component: () => import('@/views/example/tree-table/index.vue'),
				meta: {
					title: '树列表-表格',
					authCode: '',
				},
			},
		],
	},
	{
		path: '/common-components',
		name: 'common-components',
		component: () => import('@/views/main/index.vue'),
		meta: {
			title: '通用组件',
			authCode: '通用组件',
			icon: 'icon-building-blue',
			primaryIcon: 'icon-building-white',
			isDev: true,
		},
		children: [
			{
				path: 'operation-tree-navigation',
				name: 'operation-tree-navigation',
				component: () => import('@/views/common-components/operation-tree/index.vue'),
				meta: {
					title: '列表导航',
					authCode: '',
				},
			},
			{
				path: 'upload',
				name: 'upload',
				component: () => import('@/views/common-components/upload/index.vue'),
				meta: {
					title: '文件上传',
					authCode: '',
				},
			},
			{
				path: 'multi-groups-search-example',
				name: 'multi-groups-search-example',
				component: () => import('@/views/common-components/multi-groups-search/index.vue'),
				meta: {
					title: '多组筛选',
					authCode: '',
				},
			},
		],
	},
	{
		path: '/business-components',
		name: 'business-components',
		component: () => import('@/views/main/index.vue'),
		meta: {
			title: '业务组件',
			authCode: '业务组件',
			icon: 'icon-building-blue',
			primaryIcon: 'icon-building-white',
			isDev: true,
		},
		children: [
			{
				path: 'select-organization',
				name: 'select-organization',
				component: () => import('@/views/business-components/select-organization/index.vue'),
				meta: {
					title: '选择组织机构',
					authCode: '',
				},
			},
			{
				path: 'select-personnel',
				name: 'select-personnel',
				component: () => import('@/views/business-components/select-personnel/index.vue'),
				meta: {
					title: '选择人员',
					authCode: '',
				},
			},
		],
	},
	{
		path: '/table',
		name: 'table',
		component: () => import('@/views/main/index.vue'),
		meta: {
			title: '表格使用',
			authCode: '表格使用',
			icon: 'icon-building-blue',
			primaryIcon: 'icon-building-white',
			isDev: true,
		},
		children: [
			{
				path: 'use-example',
				name: 'use-example',
				component: () => import('@/views/table/use-example/index.vue'),
				meta: {
					title: '使用示例',
					authCode: '',
				},
			},
			{
				path: 'status-column',
				name: 'status-column',
				component: () => import('@/views/table/status-column/index.vue'),
				meta: {
					title: '状态列',
					authCode: '',
				},
			},
			{
				path: 'create-button',
				name: 'create-button',
				component: () => import('@/views/table/create-button/index.vue'),
				meta: {
					title: '创建按钮',
					authCode: '',
				},
			},
			{
				path: 'batch-operation',
				name: 'batch-operation',
				component: () => import('@/views/table/batch-operation/index.vue'),
				meta: {
					title: '批量操作',
					authCode: '',
				},
			},
			{
				path: 'span-method',
				name: 'span-method',
				component: () => import('@/views/table/span-method/index.vue'),
				meta: {
					title: '合并列或行',
					authCode: '',
				},
			},
		],
	},
	{
		path: '/query',
		name: 'query',
		component: () => import('@/views/main/index.vue'),
		meta: {
			title: '表格搜索',
			authCode: '表格搜索',
			icon: 'icon-building-blue',
			primaryIcon: 'icon-building-white',
			isDev: true,
		},
		children: [
			{
				path: 'common-input-search',
				name: 'common-input-search',
				component: () => import('@/views/query/common-input-search/index.vue'),
				meta: {
					title: '通用输入框搜索',
					authCode: '',
					keepAlive: true,
				},
			},
			{
				path: 'common-all-search',
				name: 'common-all-search',
				component: () => import('@/views/query/common-all-search/index.vue'),
				meta: {
					title: '表头全量搜索',
					authCode: '',
					keepAlive: true,
				},
			},
			{
				path: 'input-search',
				name: 'input-search',
				component: () => import('@/views/query/input-search/index.vue'),
				meta: {
					title: '表头输入框搜索',
					authCode: '',
					keepAlive: true,
				},
			},
			{
				path: 'select-search',
				name: 'select-search',
				component: () => import('@/views/query/select-search/index.vue'),
				meta: {
					title: '表头下拉框搜索',
					authCode: '',
					keepAlive: true,
				},
			},
			{
				path: 'date-search',
				name: 'date-search',
				component: () => import('@/views/query/date-search/index.vue'),
				meta: {
					title: '表头日期框搜索',
					authCode: '',
					keepAlive: true,
				},
			},
			{
				path: 'sort-search',
				name: 'sort-search',
				component: () => import('@/views/query/sort-search/index.vue'),
				meta: {
					title: '表头排序搜索',
					authCode: '',
					keepAlive: true,
				},
			},
			// {
			// 	path: 'high-sort-search',
			// 	name: 'high-sort-search',
			// 	component: () => import('@/views/query/high-sort-search/index.vue'),
			// 	meta: {
			// 		title: '表头排序搜索-高级排序',
			// 		authCode: '',
			// 		keepAlive: true,
			// 	},
			// },
		],
	},
	{
		path: '/form',
		name: 'form',
		component: () => import('@/views/main/index.vue'),
		meta: {
			title: '表单',
			authCode: '表单',
			icon: 'icon-building-blue',
			primaryIcon: 'icon-building-white',
			isDev: true,
		},
		children: [
			{
				path: 'base-form',
				name: 'base-form',
				component: () => import('@/views/form/base-form/index.vue'),
				meta: {
					title: '基础表单',
					authCode: '',
				},
			},
			{
				path: 'group-form',
				name: 'group-form',
				component: () => import('@/views/form/group-form/index.vue'),
				meta: {
					title: '分组表单',
					authCode: '',
				},
			},
			{
				path: 'step-form',
				name: 'step-form',
				component: () => import('@/views/form/step-form/index.vue'),
				meta: {
					title: '分步骤表单',
					authCode: '',
				},
			},
		],
	},
	{
		path: '/demo',
		name: 'demo',
		component: () => import('@/views/main/index.vue'),
		meta: {
			title: 'Demo',
			authCode: 'Demo',
			icon: 'icon-user-blue',
			primaryIcon: 'icon-user-white',
			isDev: true,
		},
		children: [
			{
				path: '',
				name: '',
				component: () => null,
				meta: {
					title: '打开浏览器新窗口',
					authCode: '',
					linkUrl: 'http://172.16.40.124/system-portal/home',
				},
			},
			{
				path: 'menu',
				name: 'demo_menu',
				component: () => import('@/views/demo/menu/index.vue'),
				meta: {
					title: '菜单导航',
					authCode: '',
				},
			},
			{
				path: 'hide-menu',
				name: 'demo_hide',
				component: () => import('@/views/demo/menu/hide-menu.vue'),
				meta: {
					title: '隐藏的菜单',
					hidden: true,
					authCode: '',
				},
			},
			{
				path: 'store',
				name: 'demo_store',
				component: () => import('@/views/demo/store/index.vue'),
				meta: {
					title: '全局状态管理',
					authCode: '',
				},
			},
			{
				path: 'echarts',
				name: 'demo_echarts',
				component: () => import('@/views/demo/echarts/index.vue'),
				meta: {
					title: 'echarts',
					authCode: '',
				},
			},
			{
				path: 'image',
				name: 'demo_image',
				component: () => import('@/views/demo/image/index.vue'),
				meta: {
					title: '引入图片',
					authCode: '',
				},
			},

			{
				path: 'date',
				name: 'demo_date',
				component: () => import('@/views/demo/date/index.vue'),
				meta: {
					title: '日期转换',
					authCode: '',
				},
			},
			{
				path: 'getApi',
				name: 'demo_getApi',
				component: () => import('@/views/demo/get-api/index.vue'),
				meta: {
					title: '调用接口',
					authCode: '',
				},
			},
			{
				path: 'icon',
				name: 'icon',
				component: () => import('@/views/demo/icon/index.vue'),
				meta: {
					title: 'icon图标',
					authCode: '',
				},
			},
		],
	},
];
