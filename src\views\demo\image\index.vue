<template>
	<div>
		<h1>引入图片</h1>
		<h3>直接引入</h3>
		<img class="t-img" src="@/assets/images/403.png" alt="" />
		<h3>import引入</h3>
		<img class="t-img" :src="imgUrl" alt="" />
		<h3>background-image</h3>
		<div class="t-img"></div>
	</div>
</template>

<script lang="ts" setup>
import imgUrl from '@/assets/images/403.png';
</script>
<style lang="scss" scoped>
.t-img {
	width: 200px;
	height: 200px;
	display: block;
}
div.t-img {
	background-image: url('@/assets/images/403.png');
}
</style>
